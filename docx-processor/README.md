# DocxProcessor

## Processor setup

If you only want to start the processor server, don't need to use the frontend or even make changes to the backend server, use the following options.

### Direct use of `Django`

1. Install `poetry` dependency manager

    ```bash
    sudo apt install pipx
    pipx install poetry
    ```

2. Backend setup

    ```bash
    cd packages/backend/
    poetry install
    ```

3. Start the backend

    ```bash
    poetry run python manage.py runserver
    ```
    or
    ```bash
    poetry run python manage.py runserver 0.0.0.0:8000
    ```

### Use `docker`

1. From the `docx-processor` folder of the repository root, namely (partial tree)

    <pre><font color="#C01C28"><b>.</b></font>
    ├── <font color="#26A269"><b>configure-API.sh</b></font>
    ├── <font color="#26A269"><b>configure.sh</b></font>
    ├── <font color="#26A269"><b>create_customer.sh</b></font>
    ├── <font color="#C01C28"><b>doc</b></font>
    ├── docker-compose-include.yml
    ├── docker-compose-kc.yml
    ├── docker-compose.yml
    ├── <font color="#C01C28"><b>docx-processor</b></font>
    │   ├── ...
    │   ├── docker-compose-docx-processor.yml
    │   ├── docx-processor-backend.yml
    │   ├── <font color="#C01C28"><b>examples</b></font>
    │   ├── <font color="#C01C28"><b>frontend</b></font>
    │   ├── <font color="#C01C28"><b>lab</b></font>
    │   ├── <font color="#C01C28"><b>packages</b></font>
    │   ├── README.md
    │   ├── <font color="#C01C28"><b>reports</b></font>
    │   ├── <font color="#C01C28"><b>tests</b></font>
    │   └── ...
    ├── ...
    ├── <font color="#C01C28"><b>practis-backend</b></font>
    ├── <font color="#C01C28"><b>practis-frontend</b></font>
    └── ...
    </pre>

2. Run

    ```bash
    cd docx-processor/
    docker compose docx-processor-backend.yml
    ```

3. Now you can generate a document with

    ```bash
    curl -X POST http://localhost:8000/api/process-template \
        -H "Content-Type: application/json" \
        -d @template-mapping.json \
        -o processed-document.docx
    ```

    Where the JSON file named `template-mapping.json` is

    ```json
    {
        "template": "sut-template.docx",
        "mapping": {
            "[sut.version]": "Version 1.0.1",
            "[sut.name]": "My Beautiful SUT",
            "[sut.description]": "This is a simple description of the SUT. It is a beautiful system that does amazing things.",
            "[Date]": "@DATE",
            "[Full Date]": "@DATE_LONG",
            "[sut.objectives]": "The objectives are to assess how simple and easy to use the SUT, that does amazing things, is.",
            "[sut.scope]": "The scope of this study is limited to the SUT physical ports.",
            "__sections__": [
                {
                    "start": "[objectives-section-start]",
                    "end": "[objectives-section-end]",
                    "state": "visible"
                },
                {
                    "start": "[scope-section-start]",
                    "end": "[scope-section-end]",
                    "state": "visible"
                }
            ]
        }
    }
    ```

## Run the PoC (with the frontend)

From the project clone (`practis-all/`)

```bash
cd docx-processor
docker-compose -f docker-compose-docx-processor.yml up -d
```

You should end with

```bash
docker ps
CONTAINER ID   IMAGE                     COMMAND                  CREATED         STATUS         PORTS                                       NAMES
6060710b1c9b   docx-processor-frontend   "/docker-entrypoint.…"   5 minutes ago   Up 5 minutes   0.0.0.0:80->80/tcp, :::80->80/tcp           docx-processor-frontend-1
b45cf5838a40   docx-processor-backend    "poetry run python m…"   5 minutes ago   Up 5 minutes   0.0.0.0:8001->8000/tcp, :::8001->8000/tcp   docx_processor_backend
```

In the `docx-processor/tests/` folder you can find template file, JSON file (the data used to generate the final documents) for the templates. It also contains the expected generated documents.

There are 2 subfolders:

- `Custom Template Report` containing templates to provide and the placeholder mappings for which the PoC server has default replacement data.
- `Custom Document Processing` containing templates to provide and also the data for the PoC server.

## Project setup (with the frontend)

The following commands are run from the `docx-processor` directory in project root directory.

Backend server setup, [see the "Direct use of `Django`" section](#direct-use-of-django).

To run the server

```bash
cd frontend
pnpm i
pnpm dev
```

### Example files

Template file: `docx-processor/packages/backend/backend/templates/default-template.docx`

JSON file giving values mapping to logical placeholder : `docx-processor/packages/backend/backend/templates/default-logical-replacements.json`

## Troubleshooting

### Poetry cache issue

Clear cached files by `poetry`

```bash
rm -rf ~/.cache/pypoetry/virtualenvs/docx-processor-api-*
```

### Backend not running

If you see the following display in the frontend, it means that the backend is not running.

<img src="backend_not_running.png">

## Project description

Proof of concept (PoC) targeting:

1. generate *reports* using a Word document as template
2. develop a CLI app that generates reports using templates
3. develop a web app where we can use the generator

The generator supports next features

- a Word document containing placeholders is used as input, a replacement set of values is used to replace the placeholders with the values
- a mapping configuration can be used to map actual placeholders to logical placeholders, so that the backend server can use any template as long as it provides the predefined logical placeholders
- some predefined special values can be set for the replacements (like `@DATE`), see the *Special Values* section below
- supports some settings to customize the special values, through a JSON file; see *Settings File* section below
- both the replacement values and the placeholders mapping are given as `JSON` files, see *Replacements File* and sections below 

In the web app frontend, users can ask for a report generation, they have a choice of 3 options:

- using the default one template and the default data
- upload their own template and use the default data
- upload their own template and use their own data

The idea is to have a page where users can trigger a document generation

- they either generate a report using a default template with default values for the PoC
- or they
  - upload a template document,
  - fill a table where they set the mapping for the placeholders, the mapping table defaults to the ones that match the default template,
  - can change the mappings as they want,
  - can load a JSON mapping definition file,
  - can generate a document once they set up the mapping 
- or, same as above, but they must give the values for each placeholder

The `Python/Django` backend generates the Word documents.

The ReactJS frontend has a component (`DocumentProcessor`) where the users select what they want to generate. 

A CLI tool is part of the project, it is is handy to use locally.

We use `Nx`, see the `NxNotes.md` document for more info.

> Nx is a powerful open-source build system that provides tools and techniques for enhancing developer productivity, optimizing CI performance, and maintaining code quality.

## Special Values

Special values start with `@` for dynamic content:

- `@DATE`: Current date using configured format
- `@DATE_LONG`: Current date in long format
- `@TIME`: Current time
- `@YEAR`: Current year
- `@MONTH`: Current month number
- `@MONTH_NAME`: Current month name
- `@DAY`: Current day of month
- `@WEEKDAY`: Current day name

## Settings File

```json
{
    "locale": "fr_FR",
    "date_format": "%d/%m/%Y",
    "time_format": "%H:%M:%S",
    "long_date_format": "%d %B %Y",
    "decimal_separator": ",",
    "thousands_separator": " ",
    "currency_symbol": "€",
    "currency_position": "after"
}
```

The `currency_position` can be "before" or "after".

## Replacements File

Example file.

```json
{
    "[Version x.y.z]": "2.0.1",
    "[Title]": "A Title",
    "[Date]": "@DATE",
    "[Full Date]": "@DATE_LONG",
    "[Current Time]": "@TIME",
    "[Project Name]": "My Project",
    "[Author]": "John Doe"
}
```

The placeholders are the property names of the JSON data and the value are the replacements to apply in the template document.

## Placeholders Mapping File

Example file.

```json
{
    "version": "[Version x.y.z]",
    "title": "[Title]",
    "date": "[Date]",
    "current_time": "[Current Time]",
    "project_name": "[Project Name]",
    "generated_on": "[Generated On]",
    "author": "[Author]"
}
```

The property names are the logical data supported by the backend, the values define the placeholders in the template to use to apply to apply the replacements. The backend use default replacement values.

## Reserved Keys in Document Processing

### Convention

The document processor uses a double underscore (*dunder*) naming convention for special features and operations, similar to Python's naming convention for special methods. This clearly distinguishes special processing keys from regular placeholder keys.

Format: `__feature_name__`

### Current Reserved Keys

- `__array_data__`: Handles repeating rows in tables or in sections with typed data
  ```json
  {
    "__array_data__": [
      {
        "placeholders": ["[placeholder1]", "[placeholder2]"],
        "values": [
          ["value1", "value2"],
          ["value3", "value4"]
        ]
      }
    ]
  }
  ```

- `__charts__`: handles charts generation, support 3 types: pie, lines and bar (see examples)
  ```json
  {
    "__charts__": [
      {
          "placeholder": "[vuln-severity]",
          "type": "pie",
          "title": "Vulnerabilities by Severity",
          "data": {
              "labels": ["Critical", "High", "Medium", "Low"],
              "values": [2, 5, 8, 3],
              "colors": ["#ff0000", "#ff9900", "#ffcc00", "#00cc00"]
          },
          "options": {
              "width": 8,
              "height": 5,
              "legend": {
              "position": "right"
              },
              "showPercentages": true
          }
      }
    ]
  }
  ```

- `__sections__`: section of the JSON file where we define the start and end of sections and if they should be visible or removed in the generated document
  ```json
  {
    "__sections__": [
        {
            "start": "[start-of-section]",
            "end": "[end-of-section]"
        },
        {
            "start": "[start-of-hidden]",
            "end": "[end-of-hidden]",
            "state": "hidden"
        }
    ]
  }
  ```

### Different value type for table values

In a table, the replacement values of the placeholder can be typed, 2 data types are supported: `date` and `currency`.

Date example: 
```json
{ "type": "date", "value": "2024-03-15", "format": "%B %d, %Y" }
```

Currency example: 
```json
{ "type": "currency", "amount": 5000.50 }
```

### Implementation Guidelines

When implementing new reserved keys:

1. Add the key to `PlaceholderMapper.RESERVED_KEYS`:
   ```python
   RESERVED_KEYS = {
       '__array_data__',
       '__sections__',
       '__list_items__',
       # Add new keys here
   }
   ```

2. Define a schema for the key's value structure:
   ```python
   NEW_FEATURE_SCHEMA = {
       "type": "object",
       "properties": {...},
       "required": [...]
   }
   ```

3. Update the `REPLACEMENTS_SCHEMA` to include the new key:
   ```python
   REPLACEMENTS_SCHEMA = {
       "type": "object",
       "properties": {
           "__array_data__": COLLECTION_SCHEMA,
           "__new_feature__": NEW_FEATURE_SCHEMA
       },
       "additionalProperties": {"type": "string"}
   }
   ```

4. Create a processor class for the feature:
   ```python
   class NewFeatureProcessor:
       def __init__(self, settings):
           self.settings = settings
           self.logger = logging.getLogger("docx_processor.new_feature")

       def process(self, document, feature_data):
           # Implementation
           pass
   ```

5. Update the main processor to handle the new feature:
   ```python
   def process_document(self, replacements: Dict[str, Any]):
       # Handle regular replacements
       regular_replacements = {
           k: v for k, v in replacements.items() 
           if not k.startswith('__') or not k.endswith('__')
       }
       super().process_document(regular_replacements)

       # Handle special features
       if '__new_feature__' in replacements:
           self.new_feature_processor.process(
               self.doc, 
               replacements['__new_feature__']
           )
   ```

## HTML support for replacement values

### Supported HTML Elements

The following HTML elements are supported:

- Text formatting: `<b>`, `<strong>`, `<i>`, `<em>`, `<u>`
- Headers: `<h1>`, `<h2>`, `<h3>`
- Lists: `<ul>`, `<ol>`, `<li>`
- Paragraphs: `<p>`
- Hyperlinks: `<a href="...">`
- Line breaks: `<br>`

## Use existing predefined templates

You can use the `process-template` endpoint to send data replacement for one of the predefined template document.

The predefined documents are located in `packages/backend/backend/templates/default-template.docx`.

Here is an example of a payload for the endpoint (a file named `template-mapping.json`)

```json
{
    "template": "sut-template.docx",
    "mapping": {
        "[sut.version]": "Version 1.0.1",
        "[sut.name]": "My Beautiful SUT",
        "[sut.description]": "This is a simple description of the SUT. It is a beautiful system that does amazing things.",
        "[Date]": "@DATE",
        "[Full Date]": "@DATE_LONG",
        "[sut.objectives]": "The objectives are to assess how simple and easy to use the SUT, that does amazing things, is.",
        "[sut.scope]": "The scope of this study is limited to the SUT physical ports.",
        "__sections__": [
            {
                "start": "[objectives-section-start]",
                "end": "[objectives-section-end]",
                "state": "visible"
            },
            {
                "start": "[scope-section-start]",
                "end": "[scope-section-end]",
                "state": "visible"
            }
        ]
    }
}
```

You can call the endpoint with the following `curl` command:

```bash
curl -X POST http://localhost:8000/api/process-template \
     -H "Content-Type: application/json" \
     -d @template-mapping.json \
     -o processed-document.docx
```

The `-o processed-document.docx` option saves the response (the processed document) to a file named `processed-document.docx`.

## Template document processing

1. Load template:
   
   ```py
   self.doc = Document(template_path)  # Loads DOCX into Python-docx objects
   ```

1. Transform objects:
   
   - Process sections (`_process_sections`)
   - Process headers/footers
   - Replace placeholders (`replace_placeholder`)
   - Process tables and charts (in `EnhancedDocxTemplateProcessor`)

1. Save transformed objects:

    ```py
    self.doc.save(output_path)  # Saves objects back to DOCX format
    ```

The underlying library (python-docx) handles the actual DOCX format parsing and serialization, while our code focuses on the transformation logic working with the object representation of the document.

Each major component (sections, tables, charts) is processed separately but follows this same pattern of working with document objects rather than raw DOCX content

The section processing is a critical step that needs to happen before other replacements.

The current implementation in `_process_sections` handles this by:

1. Processing sections first in `process_document`:

    ```py
    # Process sections first
    self._process_sections(self.doc, replacements)
    ```

2. Then processing headers/footers/other sections

    The `process_document` method iterates through all the placeholders in `processed_replacements` and applies the replacements throughout the document.

    Here's the flow:

    1. `processed_replacements` contains key-value pairs where:

    - key = placeholder (like "[Title]")
    - value = new_text (what to replace it with)

    2. For each placeholder, it calls `replace_placeholder` which:

    - Processes headers and footers
    - Processes the main document content
    - Handles both simple text and table cells
    - Preserves formatting during replacement

    The replacement happens hierarchically:

    ```txt
    process_document
    └─ replace_placeholder
        ├─ _process_container (headers)
        ├─ _process_container (footers)
        └─ _process_container (main document)
            ├─ process paragraphs
            └─ process tables
    ```

### Tables processing

Here's how the document processor handles tables with placeholders and arrays:

1. Table Detection and Processing:

   - The `TableProcessor` class handles tables with repeatable rows
   - When processing starts, it counts all tables and processes them sequentially

2. Collection Matching:

   - For each table row, the processor looks for placeholders
   - It then tries to find a matching collection in the `__array_data__` section of replacements
   - A collection contains:
     - `placeholders`: List of placeholder names
     - `values`: Array of value sets to fill those placeholders

3. Row Processing:
    
    From [table_processor.py], when a matching collection is found:

   - It validates the collection structure
   - For each set of values in the collection:
     - Creates a new row based on the template row
     - Maps each placeholder to its corresponding value
     - Formats the value according to settings
     - Replaces the placeholder text while preserving formatting

4. Integration Flow:

    From [EnhancedDocxTemplateProcessor]:

   - Regular replacements are processed first
   - Then table collections are processed
   - Finally any chart data is processed if present

For example, if you have a table row with [name] and [age] placeholders, and your replacements include:

```json
{
    "__array_data__": [{
        "placeholders": ["[name]", "[age]"],
        "values": [
            ["John", "25"],
            ["Jane", "30"]
        ]
    }]
}
```

The processor will create two rows, replacing [name] and [age] with each set of values while maintaining the table's formatting.

### Sections processing

This code will process sections based on the __sections__ key in the replacements dictionary. Sections with the state "hidden" will be removed from the document, while sections with the state "visible" (or no state specified) will be processed normally.

Here's how it works (`_process_sections` method in `processor.py)`:

1. First, it finds all section boundaries in the container using `_find_section_boundaries`

2. For each section found, it:

   - Checks if any placeholder matches a table collection
   - Removes section markers
   - Creates copies of the section content for each value set
   - Processes replacements within each copy

#### Conditional sections

This approach for conditional sections keeps things simple.

We use specific name for section markers like `{section_name}` and `{section_name_end}`.  Some benefits, instead of markers like `{IF:condition}` include:

- Separation of Logic & Content: The template remains clean, without embedding conditions directly in the text.
- Consistency: It follows the same pattern as placeholders, making it intuitive for users already familiar with the processor.

#### Processing Logic

- The processor scans for `__sections__` in the JSON, an array of
    ```json
    {
        "start": "<placeholder>",
        "end": "<placeholder>",
        "state": "hidden|visible"
    } 
    ``` 
- If state is "hidden", it removes everything between start and end.
- If state is "visible" or absent, the section stays.

#### Example Processing

Say the template DOCX is

```
Dear [customer_name],

[has_discount_section]
Congratulations! You got a [discount_percent]%³ discount.
[has_discount_section_end]

Thank you for your purchase.
```

JSON input is

```json
{
    "[customer_name]": "John Doe",
    "__conditional_sections__": [
         {
             "start": "[has_discount_section]",
             "end": "[has_discount_section_end]",
             "state": "hidden"
         }
    ]
}
```

The generated output is

```
Dear John Doe,

Thank you for your purchase.
```