{"name": "@docx-processor/frontend", "version": "0.0.1", "private": true, "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.2.1", "@mui/material": "^6.2.1", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-slot": "^1.1.1", "@tanstack/react-query": "^5.62.7", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.468.0", "react": "18.3.1", "react-dom": "18.3.1", "react-router-dom": "6.11.2", "recharts": "^2.15.0", "tailwind-merge": "^2.5.5", "uuid": "^11.0.3"}, "devDependencies": {"@types/node": "18.16.9", "@types/react": "18.3.1", "@types/react-dom": "18.3.0", "@types/uuid": "^9.0.0", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.8.1", "eslint": "^9.8.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "prettier": "^2.6.2", "sass": "^1.55.0", "typescript": "~5.6.2", "vite": "^5.0.0"}}