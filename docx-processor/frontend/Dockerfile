FROM node:18-alpine AS build

# Install pnpm globally
RUN npm install -g pnpm

# Set working directory
WORKDIR /app

# Copy package.json and related files first (for better caching)
COPY package.json ./

# Install dependencies
RUN pnpm install

# Copy the project files
COPY . .

# Build the project
RUN pnpm build

# Production environment stage
FROM nginx:alpine

# Copy the build output from the previous stage
COPY --from=build /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]