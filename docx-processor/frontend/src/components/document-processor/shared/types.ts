export type ProcessingMode = 'choice' | 'default' | 'custom';

export interface PlaceholderMapping {
  [key: string]: string;
}

export interface MappingResponse {
  mappings: Record<string, string>;
  categories: Record<string, CategoryDefinition>;
  special_values: string[];
}

export interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error';
}

export interface ReportOptionsProps {
  showSnackbar: (message: string, severity: 'success' | 'error') => void;
  onBack?: () => void;
}

export interface CategoryDefinition {
  color: string;
  keys: string[];
  label: string;
}

export interface CustomGenerationProps extends ReportOptionsProps {
  onBack: () => void;
  showSnackbar: (message: string, severity: 'success' | 'error') => void;
  mappings: Record<string, string>;
  specialValues: string[];
  loading: boolean;
  onMappingChange: (key: string, value: string) => void;
  categories: Record<string, CategoryDefinition>;
}

export interface MappingEditorProps {
  mappings: PlaceholderMapping;
  categories: Record<string, CategoryDefinition>;
  onMappingChange: (key: string, value: string) => void;
  onSave: () => void;
}

export interface DiffViewProps {
  originalMappings: PlaceholderMapping;
  newMappings: PlaceholderMapping;
  categories: Record<string, CategoryDefinition>;
  onApplyChange: (key: string) => void;
}

export interface BulkEditDialogProps {
  selectedMappings: [string, string][];
  onApply: (changes: Record<string, string>) => void;
}

export interface Placeholder {
  key: string;
  value: string;
}

export type ProcessingModeType = 'direct' | 'mapping';