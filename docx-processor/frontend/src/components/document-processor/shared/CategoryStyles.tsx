import { Box, SxProps, TextField, alpha } from '@mui/material';
import { styled } from '@mui/material/styles';
import { Theme } from '@mui/material/styles';

interface CategoryCircleProps {
  color?: string;
  sx?: SxProps<Theme>;
}

interface StyledTextFieldProps {
  categoryColor?: string;
  sx?: SxProps<Theme>;
}

export const CategoryCircle = styled(Box)<CategoryCircleProps>(({ color = 'grey.300' }) => ({
  width: 12,
  height: 12,
  borderRadius: '50%',
  backgroundColor: color,
  display: 'inline-block',
  marginRight: 8,
}));

export const CategoryTextField = styled(TextField)<StyledTextFieldProps>(({ categoryColor }) => ({
  '& .MuiOutlinedInput-root': {
    '& fieldset': {
      ...(categoryColor && { borderColor: alpha(categoryColor, 0.7) }),
    },
    '&.Mui-focused fieldset': {
      ...(categoryColor && {
        borderColor: categoryColor,
        borderWidth: 2,
      }),
    },
  },
  ...(categoryColor && {
    '& label.Mui-focused': {
      color: categoryColor,
    },
  }),
}));

export const FieldWrapper = styled(Box)({
  position: 'relative',
  '& .category-indicator': {
    position: 'absolute',
    right: 4,
    top: -6,
    zIndex: 1,
  },
  '& .MuiFormLabel-root': {
    right: '24px',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  },
});