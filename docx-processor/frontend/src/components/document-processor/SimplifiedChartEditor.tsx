import { useEffect, useState } from 'react';
import {
  Box,
  TextField,
  Button,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
} from '@mui/material';
import { Add as PlusIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { v4 as uuidv4 } from 'uuid';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
} from 'recharts';

interface DatasetRow {
  id: string;
  name: string;
  values: string;
  color: string;
}

interface PieSlice {
  id: string;
  category: string;
  value: string;
  color: string;
}

interface ChartEditorProps {
  chart: any;
  onUpdate: (chart: any) => void;
  onDelete: (chart: any) => void;
}

export const SimplifiedChartEditor = ({
  chart,
  onUpdate,
  onDelete,
}: ChartEditorProps) => {
  // Line/Bar state
  const [datasets, setDatasets] = useState<DatasetRow[]>([]);
  const [categories, setCategories] = useState('');

  // Pie state
  const [pieSlices, setPieSlices] = useState<PieSlice[]>([]);

  // Initialize from props
  useEffect(() => {
    if (chart.data) {
      if (chart.type === 'pie') {
        const slices = chart.data.labels.map((label: string, idx: number) => ({
          id: uuidv4(),
          category: label,
          value: (chart.data.values[idx] || 0).toString(),
          color:
            chart.data.colors?.[idx] ||
            `hsl(${(idx * 360) / chart.data.labels.length}, 70%, 50%)`,
        }));
        setPieSlices(slices);
        setDatasets([]); // Clear other state
        setCategories('');
      } else {
        setCategories(chart.data.labels.join(', '));
        const newDatasets = (chart.data.datasets || []).map((ds: any) => ({
          id: uuidv4(),
          name: ds.name,
          values: ds.values.join(', '),
          color: ds.color || '#000000',
        }));
        setDatasets(newDatasets);
        setPieSlices([]); // Clear other state
      }
    }
  }, [chart.data, chart.type]);

  const handleTypeChange = (newType: string) => {
    if (newType === 'pie') {
      // Convert existing categories/datasets to pie slices if possible
      const cats = categories
        .split(',')
        .map((c) => c.trim())
        .filter(Boolean);
      if (cats.length > 0 && datasets.length > 0) {
        const values = datasets[0].values.split(',').map((v) => v.trim());
        setPieSlices(
          cats.map((cat, idx) => ({
            id: uuidv4(),
            category: cat,
            value: values[idx] || '0',
            color: `hsl(${(idx * 360) / cats.length}, 70%, 50%)`,
          }))
        );
      } else {
        // Start with one empty slice
        setPieSlices([
          {
            id: uuidv4(),
            category: '',
            value: '0',
            color: 'hsl(0, 70%, 50%)',
          },
        ]);
      }
      setDatasets([]);

      setCategories('');
    } else {
      // Convert pie slices to categories/dataset if switching from pie
      if (pieSlices.length > 0) {
        setCategories(pieSlices.map((slice) => slice.category).join(', '));
        setDatasets([
          {
            id: uuidv4(),
            name: 'Dataset 1',
            values: pieSlices.map((slice) => slice.value).join(', '),
            color:
              '#' +
              Math.floor(Math.random() * 16777215)
                .toString(16)
                .padStart(6, '0'),
          },
        ]);
      }
      setPieSlices([]);
    }
    onUpdate({ ...chart, type: newType });
  };

  const handleAddPieSlice = () => {
    const newSlice: PieSlice = {
      id: uuidv4(),
      category: '',
      value: '0',
      color: `hsl(${
        (pieSlices.length * 360) / (pieSlices.length + 1)
      }, 70%, 50%)`,
    };
    setPieSlices([...pieSlices, newSlice]);
  };

  const handleAddDataset = () => {
    const cats = categories
      .split(',')
      .map((c) => c.trim())
      .filter(Boolean);
    const newDataset = {
      id: uuidv4(),
      name: `Dataset ${datasets.length + 1}`,
      values: Array(cats.length).fill('0').join(', '),
      color: `#${Math.floor(Math.random() * 16777215)
        .toString(16)
        .padStart(6, '0')}`,
    };
    setDatasets([...datasets, newDataset]);
  };

  const handleSave = () => {
    if (chart.type === 'pie') {
      onUpdate({
        ...chart,
        data: {
          labels: pieSlices.map((slice) => slice.category),
          values: pieSlices.map((slice) => parseFloat(slice.value) || 0),
          colors: pieSlices.map((slice) => slice.color),
        },
      });
    } else {
      const cats = categories
        .split(',')
        .map((c) => c.trim())
        .filter(Boolean);
      onUpdate({
        ...chart,
        data: {
          labels: cats,
          datasets: datasets.map((d) => ({
            name: d.name,
            values: d.values
              .split(',')
              .map((v) => parseFloat(v.trim()))
              .filter((v) => !isNaN(v)),
            color: d.color,
          })),
        },
      });
    }
  };

  const getPreviewData = () => {
    if (chart.type === 'pie') {
      return pieSlices.map((slice) => ({
        name: slice.category,
        value: parseFloat(slice.value) || 0,
      }));
    }

    const cats = categories
      .split(',')
      .map((c) => c.trim())
      .filter(Boolean);
    return cats.map((cat, idx) => ({
      name: cat,
      ...datasets.reduce((acc, ds) => {
        const values = ds.values.split(',').map((v) => parseFloat(v.trim()));
        return { ...acc, [ds.name]: values[idx] || 0 };
      }, {}),
    }));
  };

  return (
    <Box>
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={4}>
          <TextField
            fullWidth
            label="Title"
            value={chart.title || ''}
            onChange={(e) => onUpdate({ ...chart, title: e.target.value })}
          />
        </Grid>
        <Grid item xs={4}>
          <TextField
            fullWidth
            label="Placeholder"
            value={chart.placeholder || ''}
            onChange={(e) =>
              onUpdate({ ...chart, placeholder: e.target.value })
            }
          />
        </Grid>
        <Grid item xs={3}>
          <FormControl fullWidth>
            <InputLabel>Type</InputLabel>
            <Select
              value={chart.type}
              label="Type"
              onChange={(e) => handleTypeChange(e.target.value)}
            >
              <MenuItem value="line">Line</MenuItem>
              <MenuItem value="bar">Bar</MenuItem>
              <MenuItem value="pie">Pie</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={1}>
          <IconButton color="error" onClick={() => onDelete(chart)}>
            <DeleteIcon />
          </IconButton>
        </Grid>
      </Grid>

      {chart.type === 'pie' ? (
        // Pie Chart Data Input
        <Box>
          {pieSlices.map((slice, index) => (
            <Grid container spacing={2} key={slice.id} sx={{ mb: 2 }}>
              <Grid item xs={5}>
                <TextField
                  fullWidth
                  label="Category"
                  value={slice.category}
                  onChange={(e) => {
                    const newSlices = [...pieSlices];
                    newSlices[index] = { ...slice, category: e.target.value };
                    setPieSlices(newSlices);
                  }}
                />
              </Grid>
              <Grid item xs={5}>
                <TextField
                  fullWidth
                  label="Value"
                  type="number"
                  value={slice.value}
                  onChange={(e) => {
                    const newSlices = [...pieSlices];
                    newSlices[index] = { ...slice, value: e.target.value };
                    setPieSlices(newSlices);
                  }}
                />
              </Grid>
              <Grid item xs={1}>
                <TextField
                  type="color"
                  value={slice.color}
                  onChange={(e) => {
                    const newSlices = [...pieSlices];
                    newSlices[index] = { ...slice, color: e.target.value };
                    setPieSlices(newSlices);
                  }}
                  sx={{ width: '100%' }}
                />
              </Grid>
              <Grid item xs={1}>
                <IconButton
                  color="error"
                  onClick={() =>
                    setPieSlices(pieSlices.filter((s) => s.id !== slice.id))
                  }
                  disabled={pieSlices.length === 1}
                >
                  <DeleteIcon />
                </IconButton>
              </Grid>
            </Grid>
          ))}
          <Button
            variant="outlined"
            onClick={handleAddPieSlice}
            startIcon={<PlusIcon />}
          >
            Add Slice
          </Button>
        </Box>
      ) : (
        // Line/Bar Chart Data Input
        <Box>
          <TextField
            fullWidth
            label="Categories (comma-separated)"
            value={categories}
            onChange={(e) => setCategories(e.target.value)}
            sx={{ mb: 3 }}
          />

          {datasets.map((dataset, index) => (
            <Grid container spacing={2} key={dataset.id} sx={{ mb: 2 }}>
              <Grid item xs={3}>
                <TextField
                  fullWidth
                  label="Dataset Name"
                  value={dataset.name}
                  onChange={(e) => {
                    const newDatasets = [...datasets];
                    newDatasets[index] = { ...dataset, name: e.target.value };
                    setDatasets(newDatasets);
                  }}
                />
              </Grid>
              <Grid item xs={7}>
                <TextField
                  fullWidth
                  label="Values (comma-separated)"
                  value={dataset.values}
                  onChange={(e) => {
                    const newDatasets = [...datasets];
                    newDatasets[index] = { ...dataset, values: e.target.value };
                    setDatasets(newDatasets);
                  }}
                />
              </Grid>
              <Grid item xs={1}>
                <TextField
                  type="color"
                  value={dataset.color}
                  onChange={(e) => {
                    const newDatasets = [...datasets];
                    newDatasets[index] = { ...dataset, color: e.target.value };
                    setDatasets(newDatasets);
                  }}
                  sx={{ width: '100%' }}
                />
              </Grid>
              <Grid item xs={1}>
                <IconButton
                  color="error"
                  onClick={() =>
                    setDatasets(datasets.filter((d) => d.id !== dataset.id))
                  }
                >
                  <DeleteIcon />
                </IconButton>
              </Grid>
            </Grid>
          ))}
          <Button
            variant="outlined"
            onClick={handleAddDataset}
            startIcon={<PlusIcon />}
          >
            Add Dataset
          </Button>
        </Box>
      )}

      <Box sx={{ display: 'flex', gap: 2, my: 3 }}>
        <Button variant="contained" onClick={handleSave}>
          Apply Changes
        </Button>
      </Box>

      {/* Chart Preview */}
      {((chart.type === 'pie' && pieSlices.length > 0) ||
        (chart.type !== 'pie' && datasets.length > 0 && categories)) && (
        <Box sx={{ height: 300 }}>
          <ResponsiveContainer width="100%" height="100%">
            {chart.type === 'pie' ? (
              <PieChart>
                <Pie
                  data={getPreviewData()}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  label
                >
                  {pieSlices.map((slice) => (
                    <Cell key={slice.id} fill={slice.color} />
                  ))}
                </Pie>
                <RechartsTooltip />
                <Legend />
              </PieChart>
            ) : chart.type === 'line' ? (
              <LineChart data={getPreviewData()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <RechartsTooltip />
                <Legend />
                {datasets.map((ds) => (
                  <Line
                    key={ds.id}
                    type="monotone"
                    dataKey={ds.name}
                    stroke={ds.color}
                  />
                ))}
              </LineChart>
            ) : (
              <BarChart data={getPreviewData()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <RechartsTooltip />
                <Legend />
                {datasets.map((ds) => (
                  <Bar key={ds.id} dataKey={ds.name} fill={ds.color} />
                ))}
              </BarChart>
            )}
          </ResponsiveContainer>
        </Box>
      )}
    </Box>
  );
};

export default SimplifiedChartEditor;
