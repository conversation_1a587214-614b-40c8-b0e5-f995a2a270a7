import { useState, useCallback } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  TextField,
  Typography,
  IconButton,
  Alert,
  CircularProgress,
  Autocomplete,
  Paper,
  Tooltip,
  Card,
  CardContent,
} from '@mui/material';
import {
  X as CloseIcon,
  ArrowRight,
  RefreshCw,
  AlertCircle,
} from 'lucide-react';
import axios from 'axios';
import { CategoryDefinition } from './shared/types';

interface ExtractPlaceholdersDialogProps {
  open: boolean;
  onClose: () => void;
  template: File | null;
  categories: Record<string, CategoryDefinition>;
  onMappingsExtracted: (mappings: Record<string, string>) => void;
  existingMappings: Record<string, string>;
}

export const TemplateExtractDialog = ({
  open,
  onClose,
  template,
  categories,
  onMappingsExtracted,
  existingMappings,
}: ExtractPlaceholdersDialogProps) => {
  const [loading, setLoading] = useState(false);
  const [extractedPlaceholders, setExtractedPlaceholders] = useState<string[]>([]);
  const [mappings, setMappings] = useState<Record<string, string>>({});
  const [error, setError] = useState<string | null>(null);
  const [pattern, setPattern] = useState('\\[([^\\]]+)\\]');

  const extractPlaceholders = useCallback(async () => {
    if (!template) return;

    setLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('template', template);
      formData.append('pattern', pattern);

      const response = await axios.post<{ placeholders: string[] }>(
        '/api/extract-placeholders',
        formData
      );
      const placeholders = response.data.placeholders || [];

      setExtractedPlaceholders(placeholders);

      // Initialize mappings with existing ones
      const initialMappings = placeholders.reduce((acc, placeholder) => {
        const existingMapping = Object.entries(existingMappings).find(
          ([_, templateKey]) => templateKey === placeholder
        );

        acc[placeholder] = existingMapping ? existingMapping[0] : '';
        return acc;
      }, {} as Record<string, string>);

      setMappings(initialMappings);
    } catch (error) {
      setError('Failed to extract placeholders from template');
      console.error('Error extracting placeholders:', error);
    } finally {
      setLoading(false);
    }
  }, [template, pattern, existingMappings]);

  const handleClose = () => {
    setExtractedPlaceholders([]);
    setMappings({});
    setError(null);
    onClose();
  };

  const handleApply = () => {
    const validMappings = Object.entries(mappings)
      .filter(([_, logicalKey]) => logicalKey)
      .reduce((acc, [placeholder, logicalKey]) => {
        acc[logicalKey] = placeholder;
        return acc;
      }, {} as Record<string, string>);

    onMappingsExtracted(validMappings);
    handleClose();
  };

  // Get all available logical keys from categories
  const availableLogicalKeys = Object.values(categories)
    .flatMap((category) => category.keys)
    .filter((key) => !Object.values(mappings).includes(key));

  const renderContent = () => {
    if (loading) {
      return (
        <Card>
          <CardContent>
            <Box className="flex justify-center items-center p-8">
              <CircularProgress />
            </Box>
          </CardContent>
        </Card>
      );
    }

    if (error) {
      return (
        <Alert
          severity="error"
          className="mb-4"
          action={
            <Button color="inherit" size="small" onClick={extractPlaceholders}>
              Retry
            </Button>
          }
        >
          {error}
        </Alert>
      );
    }

    if (extractedPlaceholders.length === 0) {
      return (
        <Card>
          <CardContent>
            <Box className="flex flex-col items-center justify-center gap-2 py-8 text-gray-500">
              <AlertCircle className="h-8 w-8" />
              <Typography>
                {error
                  ? 'Error extracting placeholders'
                  : 'Enter a pattern and click refresh to extract placeholders'}
              </Typography>
            </Box>
          </CardContent>
        </Card>
      );
    }

    return (
      <Paper className="overflow-hidden">
        <Box sx={{ px: 2, py: 1, bgcolor: 'grey.100' }}>
          <Typography variant="subtitle2" className="font-semibold">
            Map Template Placeholders to Logical Placeholders
          </Typography>
        </Box>

        <Box sx={{ p: 2 }}>
          <Box sx={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
            gap: 2
          }}/>
          {extractedPlaceholders.map((placeholder) => (
            <Box
              key={placeholder}
                sx={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  gap: 1,
                  p: 1,
                  bgcolor: 'grey.50',
                  borderRadius: 1,
                  '&:hover': { bgcolor: 'grey.100' }
                }}
            >
                <Typography 
                  variant="body2" 
                  sx={{ 
                    flex: '0 0 auto',
                    minWidth: '120px',
                    pt: 1,
                    fontWeight: 500
                  }}
                >
                  {placeholder}
                </Typography>

                <ArrowRight className="h-5 w-5 text-gray-400 mt-2 mx-1" />

              <Autocomplete
                size="small"
                  fullWidth
                options={availableLogicalKeys}
                value={mappings[placeholder] || null}
                onChange={(_, newValue) => {
                  setMappings((prev) => ({
                    ...prev,
                    [placeholder]: newValue || '',
                  }));
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    placeholder="Select logical placeholder"
                  />
                )}
              />
            </Box>
          ))}
        </Box>
      </Paper>
    );
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="lg" 
      fullWidth
      PaperProps={{
        sx: {
          bgcolor: '#f5f5f5',
        }
      }}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">Extract Template Placeholders</Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon className="h-4 w-4" />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box className="space-y-4" display="flex" flexDirection="column" gap={3}>
          {/* Pattern Input */}
          <Paper className="p-4 space-y-3">
            <Box display="flex" gap={2} alignItems="flex-start">
              <TextField
                fullWidth
                size="small"
                label="Placeholder Pattern (RegExp)"
                value={pattern}
                onChange={(e) => setPattern(e.target.value)}
                helperText="Define the pattern and click refresh to extract placeholders"
              />
              <Tooltip title="Extract placeholders">
                <IconButton
                  onClick={extractPlaceholders}
                  disabled={loading}
                  sx={{ mt: 0.5 }}
                >
                  <RefreshCw
                    className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`}
                  />
                </IconButton>
              </Tooltip>
            </Box>
          </Paper>

          {renderContent()}
        </Box>
      </DialogContent>

      <DialogActions className="border-t p-4">
        <Button onClick={handleClose}>Cancel</Button>
        <Button
          variant="contained"
          onClick={handleApply}
          disabled={loading || !Object.values(mappings).some(Boolean)}
        >
          Apply Mappings
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TemplateExtractDialog;
