import {
  Box,
  IconButton,
  Button,
  TextField,
  Grid,
  Card,
  CardContent,
} from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { v4 as uuidv4 } from 'uuid';

export interface SectionData {
  id: string;
  start: string;
  end: string;
  state: string;
}

export interface SectionManagementProps {
  sections: SectionData[];
  onSectionsChange: (sections: SectionData[]) => void;
}

export const SectionManagement = ({
  sections,
  onSectionsChange,
}: SectionManagementProps) => {
  const addSection = () => {
    onSectionsChange([
      ...sections,
      {
        id: uuidv4(),
        start: '',
        end: '',
        state: 'visible',
      },
    ]);
  };

  const removeSection = (id: string) => {
    onSectionsChange(sections.filter((s) => s.id !== id));
  };

  const updateSection = (
    id: string,
    field: keyof SectionData,
    value: string
  ) => {
    onSectionsChange(
      sections.map((s) => (s.id === id ? { ...s, [field]: value } : s))
    );
  };

  return (
    <Box>
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box>
            <Box display="flex" flexDirection="column" gap={2}>
              {sections.map((section) => (
                <Grid
                  container
                  key={section.id}
                  spacing={2}
                  alignItems="center"
                >
                  <Grid item xs={4}>
                    <TextField
                      fullWidth
                      label="Start Placeholder"
                      value={section.start}
                      onChange={(e) =>
                        updateSection(section.id, 'start', e.target.value)
                      }
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={4}>
                    <TextField
                      fullWidth
                      label="End Placeholder"
                      value={section.end}
                      onChange={(e) =>
                        updateSection(section.id, 'end', e.target.value)
                      }
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={2}>
                    <Button
                      fullWidth
                      variant="outlined"
                      onClick={() => {
                        const nextState =
                          section.state === 'visible'
                            ? 'hidden'
                            : section.state === 'hidden'
                            ? ''
                            : 'visible';
                        updateSection(section.id, 'state', nextState);
                      }}
                    >
                      {section.state || <em>default</em>}
                    </Button>
                  </Grid>
                  <Grid item xs={2}>
                    <IconButton
                      onClick={() => removeSection(section.id)}
                      color="error"
                      size="small"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Grid>
                </Grid>
              ))}
            </Box>
            <Box mt={2}>
              <Button
                variant="outlined"
                onClick={addSection}
                startIcon={<AddIcon />}
              >
                Add Section
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};
