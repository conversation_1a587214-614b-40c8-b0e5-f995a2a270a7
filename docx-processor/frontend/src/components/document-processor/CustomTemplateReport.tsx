import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Button,
  IconButton,
  CircularProgress,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  Upload as UploadIcon,
  CloudDownload as DownloadIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import AutoFixHighIcon from '@mui/icons-material/AutoFixHigh';

import { CategoryDefinition } from './shared/types';
import PlaceholderFilters from './PlaceholderFilters';
import MappingsList from './MappingsList';
import AddLogicalPlaceholderDialog from './AddLogicalPlaceholderDialog';
import TemplateExtractDialog from './TemplateExtractDialog';
import { downloadFile } from './utils';

interface Mapping {
  logicalKey: string;
  templateKey: string;
  category: string;
}

interface BackendData {
  mappings: Record<string, string>;
  categories: Record<string, CategoryDefinition>;
  special_values: string[];
}

interface GenerateResponse {
  error?: string;
}

interface CustomTemplateReportProps {
  showSnackbar: (message: string, severity: 'success' | 'error') => void;
}

const CustomTemplateReport = ({ showSnackbar }: CustomTemplateReportProps) => {
  const navigate = useNavigate();
  const [template, setTemplate] = useState<File | null>(null);
  const [mappings, setMappings] = useState<Mapping[]>([]);
  const [validMappings, setValidMappings] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [extractDialogOpen, setExtractDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [categories, setCategories] = useState<
    Record<string, CategoryDefinition>
  >({});
  const [defaultMappings, setDefaultMappings] = useState<
    Record<string, string>
  >({});
  const [error, setError] = useState<string | null>(null);
  const [generating, setGenerating] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get<BackendData>(
          '/api/placeholder-mappings'
        );
        setCategories(response.data.categories);
        setDefaultMappings(response.data.mappings);
        setError(null);
      } catch (err) {
        setError('Failed to load placeholder configurations');
        console.error('Error fetching placeholder mappings:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleTemplateUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setTemplate(file);
    }
  };

  const handleMappingUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        const text = await file.text();
        const uploadedMappings = JSON.parse(text);
        const newMappings = Object.entries(uploadedMappings)
          .map(([logical, template]) => {
            const category =
              Object.entries(categories).find(([_, cat]) =>
                cat.keys.includes(logical)
              )?.[0] || '';

            return {
              logicalKey: logical,
              templateKey: template as string,
              category,
            };
          })
          .filter((m) => m.category);

        setMappings(newMappings);

        // Update validMappings with all mappings that have non-empty templateKey
        const newValidMappings = new Set(
          newMappings
            .filter((m) => m.templateKey.trim() !== '')
            .map((m) => m.logicalKey)
        );
        setValidMappings(newValidMappings);
      } catch (error) {
        console.error('Error parsing mapping file:', error);
        showSnackbar('Error loading mapping file', 'error');
      }
    }
  };

  const handleExtractedMappings = (
    extractedMappings: Record<string, string>
  ) => {
    const newMappings = Object.entries(extractedMappings)
      .map(([logical, template]) => {
        const category =
          Object.entries(categories).find(([_, cat]) =>
            cat.keys.includes(logical)
          )?.[0] || '';

        return {
          logicalKey: logical,
          templateKey: template,
          category,
        };
      })
      .filter((m) => m.category);

    setMappings((prev) => {
      // Merge with existing mappings, preventing duplicates
      const existingKeys = new Set(prev.map((m) => m.logicalKey));
      const uniqueNewMappings = newMappings.filter(
        (m) => !existingKeys.has(m.logicalKey)
      );
      return [...prev, ...uniqueNewMappings];
    });

    // Update valid mappings
    setValidMappings((prev) => {
      const newValid = new Set(prev);
      newMappings.forEach((m) => {
        if (m.templateKey.trim()) {
          newValid.add(m.logicalKey);
        }
      });
      return newValid;
    });

    showSnackbar('Successfully imported extracted mappings', 'success');
  };

  const handleGenerateReport = async () => {
    if (!template || validMappings.size === 0) return;

    setGenerating(true);

    try {
      const formData = new FormData();
      formData.append('template', template);

      const mappingsObject = mappings.reduce(
        (acc, { logicalKey, templateKey }) => {
          if (validMappings.has(logicalKey)) {
            acc[logicalKey] = templateKey;
          }
          return acc;
        },
        {} as Record<string, string>
      );

      formData.append('mappings', JSON.stringify(mappingsObject));

      const response = await axios.post<Blob | GenerateResponse>(
        '/api/generate-custom',
        formData,
        {
          responseType: 'blob',
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      if (response.data instanceof Blob) {
        downloadFile(response.data, 'generated-custom-document.docx');
        showSnackbar('Document generated successfully', 'success');
      } else {
        showSnackbar('Error generating document', 'error');
      }
    } catch (err) {
      console.error('Error generating document:', err);
      showSnackbar('Error generating document', 'error');
    } finally {
      setGenerating(false);
    }
  };

  const downloadMapping = useCallback(() => {
    const mapping = mappings.reduce(
      (acc, { logicalKey, templateKey }) => ({
        ...acc,
        [logicalKey]: templateKey,
      }),
      {}
    );
    const blob = new Blob([JSON.stringify(mapping, null, 2)], {
      type: 'application/json',
    });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'template-mapping.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  }, [mappings]);

  const addMapping = (logicalKey: string) => {
    const category =
      Object.entries(categories).find(([_, cat]) =>
        cat.keys.includes(logicalKey)
      )?.[0] || '';

    if (category) {
      const templateKey = defaultMappings[logicalKey] || `[${logicalKey}]`;
      setMappings([
        ...mappings,
        {
          logicalKey,
          templateKey,
          category,
        },
      ]);
      if (templateKey.trim()) {
        setValidMappings((prev) => new Set(prev).add(logicalKey));
      }
    }
  };

  const filteredMappings = mappings.filter((mapping) => {
    const matchesSearch =
      mapping.logicalKey.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mapping.templateKey.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory =
      selectedCategory === 'all' || mapping.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const usedLogicalKeys = mappings.map((m) => m.logicalKey);

  if (loading) {
    return (
      <Container sx={{ py: 4, display: 'flex', justifyContent: 'center' }}>
        <CircularProgress />
      </Container>
    );
  }

  if (error) {
    return (
      <Container sx={{ py: 4 }}>
        <Typography color="error" align="center">
          {error}
        </Typography>
      </Container>
    );
  }

  const getMappingsForExtraction = () => {
    return mappings.reduce((acc, { logicalKey, templateKey }) => {
      acc[logicalKey] = templateKey;
      return acc;
    }, {} as Record<string, string>);
  };

  return (
    <>
      <Container sx={{ py: 4 }}>
        <Box display="flex" alignItems="center" gap={2} mb={4}>
          <IconButton onClick={() => navigate('/')} size="large">
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h4" component="h1" fontWeight="bold">
            Custom Template Report
          </Typography>
        </Box>

        <Box display="flex" flexDirection="column" gap={3}>
          {/* Template Upload */}
          <Card>
            <CardHeader title="Document Template" />
            <CardContent>
              <Box display="flex" gap={2} alignItems="center">
                <Button
                  variant="outlined"
                  component="label"
                  startIcon={<UploadIcon />}
                >
                  Upload Template
                  <input
                    type="file"
                    hidden
                    accept=".docx"
                    onChange={handleTemplateUpload}
                  />
                </Button>
                {template && (
                  <>
                    <Typography color="success.main">
                      Template: {template.name}
                    </Typography>
                    <Button
                      variant="outlined"
                      startIcon={<AutoFixHighIcon className="w-4 h-4" />}
                      onClick={() => setExtractDialogOpen(true)}
                    >
                      Extract Placeholders
                    </Button>
                  </>
                )}
              </Box>
            </CardContent>
          </Card>

          {/* Mapping Section */}
          <Card>
            <CardHeader
              title="Template Placeholder Mapping"
              action={
                <Box display="flex" gap={1}>
                  <Button
                    variant="outlined"
                    startIcon={<SaveIcon />}
                    onClick={downloadMapping}
                  >
                    Save Mapping
                  </Button>
                  <Button
                    variant="outlined"
                    component="label"
                    startIcon={<UploadIcon />}
                  >
                    Load Mapping
                    <input
                      type="file"
                      hidden
                      accept=".json"
                      onChange={handleMappingUpload}
                    />
                  </Button>
                </Box>
              }
            />
            <CardContent>
              <PlaceholderFilters
                searchTerm={searchTerm}
                onSearchChange={setSearchTerm}
                selectedCategory={selectedCategory}
                onCategoryChange={setSelectedCategory}
                categories={categories}
              />

              <MappingsList
                mappings={filteredMappings}
                categories={categories}
                onUpdateMapping={(logicalKey, templateKey) => {
                  setMappings(
                    mappings.map((m) =>
                      m.logicalKey === logicalKey ? { ...m, templateKey } : m
                    )
                  );
                  // Update valid mappings set based on the new template key
                  if (templateKey.trim()) {
                    setValidMappings((prev) => new Set(prev).add(logicalKey));
                  } else {
                    setValidMappings((prev) => {
                      const next = new Set(prev);
                      next.delete(logicalKey);
                      return next;
                    });
                  }
                }}
                onRemoveMapping={(logicalKey) => {
                  setMappings(
                    mappings.filter((m) => m.logicalKey !== logicalKey)
                  );
                  setValidMappings((prev) => {
                    const next = new Set(prev);
                    next.delete(logicalKey);
                    return next;
                  });
                }}
              />

              {/* Add Mapping Button */}
              <Button
                variant="outlined"
                onClick={() => setAddDialogOpen(true)}
                startIcon={<AddIcon />}
                sx={{ mt: 3 }}
                fullWidth
              >
                Add Mapping
              </Button>
            </CardContent>
          </Card>

          {/* Generate Button */}
          <Box display="flex" justifyContent="flex-end">
            <Button
              variant="contained"
              size="large"
              disabled={!template || validMappings.size === 0 || generating}
              startIcon={
                generating ? <CircularProgress size={24} /> : <DownloadIcon />
              }
              onClick={handleGenerateReport}
            >
              Generate Report
            </Button>
          </Box>
        </Box>
      </Container>

      <AddLogicalPlaceholderDialog
        open={addDialogOpen}
        onClose={() => setAddDialogOpen(false)}
        onAddPlaceholder={addMapping}
        categories={categories}
        existingLogicalKeys={usedLogicalKeys}
        selectedCategory={selectedCategory}
      />

      <TemplateExtractDialog
        open={extractDialogOpen}
        onClose={() => setExtractDialogOpen(false)}
        template={template}
        categories={categories}
        onMappingsExtracted={handleExtractedMappings}
        existingMappings={getMappingsForExtraction()}
      />
    </>
  );
};

export default CustomTemplateReport;
