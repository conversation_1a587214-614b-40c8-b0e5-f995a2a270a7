import { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  InputAdornment,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import { Search as SearchIcon, Label as LabelIcon } from '@mui/icons-material';
import { CategoryCircle } from './shared/CategoryStyles';
import { CategoryDefinition } from './shared/types';

interface AddLogicalPlaceholderDialogProps {
  open: boolean;
  onClose: () => void;
  onAddPlaceholder: (logicalKey: string) => void;
  categories: Record<string, CategoryDefinition>;
  existingLogicalKeys: string[];
  selectedCategory: string;
}

const AddLogicalPlaceholderDialog = ({
  open,
  onClose,
  onAddPlaceholder,
  categories,
  existingLogicalKeys,
  selectedCategory,
}: AddLogicalPlaceholderDialogProps) => {
  const [searchTerm, setSearchTerm] = useState('');

  const availablePlaceholders = Object.entries(categories)
    .filter(
      ([categoryName]) =>
        selectedCategory === 'all' || categoryName === selectedCategory
    )
    .flatMap(([categoryName, category]) =>
      category.keys
        .filter((key) => !existingLogicalKeys.includes(key))
        .map((key) => ({
          key,
          category: categoryName,
          color: category.color,
          label: category.label,
        }))
    );

  const filteredPlaceholders = availablePlaceholders.filter(
    (placeholder) =>
      placeholder.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
      placeholder.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const groupedPlaceholders = filteredPlaceholders.reduce(
    (acc, placeholder) => {
      if (!acc[placeholder.category]) {
        acc[placeholder.category] = [];
      }
      acc[placeholder.category].push(placeholder);
      return acc;
    },
    {} as Record<string, typeof filteredPlaceholders>
  );

  const handleSelect = (key: string) => {
    onAddPlaceholder(key);
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <div className="space-y-4">
        <Typography variant="h6" className="font-semibold">
          Add Logical Placeholder
        </Typography>
          {availablePlaceholders.length > 0 && (
            <TextField
              fullWidth
              placeholder="Search placeholders..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              size="small"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          )}
          </div>
      </DialogTitle>
      <DialogContent className="p-0">
        {availablePlaceholders.length > 0 ? (
          <div className="flex flex-col">
            <div className="overflow-y-auto max-h-96">
              {Object.entries(groupedPlaceholders).map(
                ([category, placeholders]) => (
                  <div key={category} className="mb-4">
                    <div className="bg-gray-50 px-4 py-2 sticky top-0">
                      <Typography
                        variant="subtitle1"
                        className="font-semibold flex items-center gap-2"
                      >
                        <CategoryCircle
                          sx={{ bgcolor: placeholders[0].color }}
                        />
                        {placeholders[0].label}
                      </Typography>
                    </div>
                    <List dense disablePadding>
                      {placeholders.map((placeholder) => (
                        <ListItem key={placeholder.key} disablePadding>
                          <ListItemButton
                            onClick={() => handleSelect(placeholder.key)}
                            className="hover:bg-gray-50"
                          >
                            <ListItemIcon className="min-w-8">
                              <LabelIcon
                                fontSize="small"
                                sx={{ color: placeholder.color }}
                              />
                            </ListItemIcon>
                            <ListItemText
                              primary={
                                <Typography
                                  variant="body2"
                                  className="text-gray-700"
                                >
                                  {placeholder.key}
                                </Typography>
                              }
                            />
                          </ListItemButton>
                        </ListItem>
                      ))}
                    </List>
                  </div>
                )
              )}

              {filteredPlaceholders.length === 0 && (
                <Box className="text-center py-8">
                  <Typography color="text.secondary">
                    No placeholders found matching your search
                  </Typography>
                </Box>
              )}
            </div>
          </div>
        ) : (
          <Box className="text-center py-12">
            <Typography color="text.secondary">
              All logical placeholders have been added
            </Typography>
          </Box>
        )}
      </DialogContent>
      <DialogActions className="border-t p-4">
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddLogicalPlaceholderDialog;
