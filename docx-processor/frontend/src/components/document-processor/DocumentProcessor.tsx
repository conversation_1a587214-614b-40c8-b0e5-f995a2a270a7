import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  CardHeader,
} from '@mui/material';
import { 
  FileText, 
  Upload, 
  FileUp,
} from 'lucide-react';

const DocumentProcessor = () => {
  const navigate = useNavigate();
  
  return (
    <Container sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" sx={{ mb: 4 }}>
        Document Generation
      </Typography>
      
      <Box display="grid" gridTemplateColumns={{ md: 'repeat(3, 1fr)' }} gap={3}>
        {/* Standard Report Generation */}
        <Card sx={{ height: '100%' }}>
          <CardHeader 
            title={
              <Box display="flex" alignItems="center" gap={1}>
                <FileText />
                Standardized Report
              </Box>
            }
            titleTypographyProps={{ variant: 'h6' }}
          />
          <CardContent>
            <Typography color="text.secondary" paragraph>
              Generate a standardized report using our predefined templates and configurations.
              Perfect for creating consistent, well-structured reports.
            </Typography>
            <Box display="flex" justifyContent="flex-end" sx={{ mt: 2 }}>
              <Typography
                onClick={() => navigate('/report/options')}
                sx={{
                  color: 'primary.main',
                  cursor: 'pointer',
                  '&:hover': { textDecoration: 'underline' }
                }}
              >
                Generate Report →
              </Typography>
            </Box>
          </CardContent>
        </Card>

        {/* Custom Template with Standard Placeholders */}
        <Card sx={{ height: '100%' }}>
          <CardHeader 
            title={
              <Box display="flex" alignItems="center" gap={1}>
                <FileUp />
                Custom Template Report
              </Box>
            }
            titleTypographyProps={{ variant: 'h6' }}
          />
          <CardContent>
            <Typography color="text.secondary" paragraph>
              Use your own document template while maintaining compatibility with our standard placeholders.
              Ideal for customized layouts with standardized data.
            </Typography>
            <Box display="flex" justifyContent="flex-end" sx={{ mt: 2 }}>
              <Typography
                onClick={() => navigate('/custom-template/new')}
                sx={{
                  color: 'primary.main',
                  cursor: 'pointer',
                  '&:hover': { textDecoration: 'underline' }
                }}
              >
                Start Template Upload →
              </Typography>
            </Box>
          </CardContent>
        </Card>

        {/* Full Custom Document Processing */}
        <Card sx={{ height: '100%' }}>
          <CardHeader 
            title={
              <Box display="flex" alignItems="center" gap={1}>
                <Upload />
                Custom Document Processing
              </Box>
            }
            titleTypographyProps={{ variant: 'h6' }}
          />
          <CardContent>
            <Typography color="text.secondary" paragraph>
              Process your own document templates with custom placeholders and values.
              Complete flexibility for one-off document generation needs.
            </Typography>
            <Box display="flex" justifyContent="flex-end" sx={{ mt: 2 }}>
              <Typography
                onClick={() => navigate('/custom/new')}
                sx={{
                  color: 'primary.main',
                  cursor: 'pointer',
                  '&:hover': { textDecoration: 'underline' }
                }}
              >
                Start Custom Processing →
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Box>
    </Container>
  );
};

export default DocumentProcessor;