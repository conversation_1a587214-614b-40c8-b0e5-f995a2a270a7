import { useEffect, useState } from 'react';
import { <PERSON>, Card, CardContent, Card<PERSON><PERSON><PERSON>, Button } from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';
import { v4 as uuidv4 } from 'uuid';
import Simplified<PERSON>hartEditor from './SimplifiedChartEditor';

export interface ChartData {
  id: string;
  type: 'pie' | 'line' | 'bar';
  placeholder: string;
  title: string;
  data: {
    labels: string[];
    values?: number[];
    datasets?: Array<{
      name: string;
      values: number[];
      color: string;
    }>;
    colors?: string[];
  };
}

interface ChartManagementProps {
  charts: ChartData[];
  onChartsChange: (charts: ChartData[]) => void;
}

const DEFAULT_COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff8042'];

export const ChartManagement = ({
  charts,
  onChartsChange,
}: ChartManagementProps) => {

  const [currentCharts, setCurrentCharts] = useState<ChartData[]>(charts);

  useEffect(() => {
    setCurrentCharts(charts);
  }, [charts]);

  const addChart = () => {
    const newChart: ChartData = {
      id: uuidv4(),
      type: 'pie',
      placeholder: '',
      title: '',
      data: {
        labels: ['Category 1', 'Category 2'],
        values: [50, 50],
        colors: DEFAULT_COLORS,
      },
    };
    onChartsChange([...currentCharts, newChart]);
  };

  return (
    <Box>
      {currentCharts.map((chart) => (
        <Card key={chart.id} sx={{ mb: 3 }}>
          <CardHeader
            title={chart.title || `Chart ${currentCharts.indexOf(chart) + 1}`}
          />
          <CardContent>
            <SimplifiedChartEditor
              chart={chart}
              onUpdate={(updated) => {
                onChartsChange(
                  currentCharts.map((c) => (c.id === updated.id ? updated : c))
                );
              }}
              onDelete={(chart) => {
                onChartsChange(currentCharts.filter((c) => c.id !== chart.id));
              }}
            />
          </CardContent>
        </Card>
      ))}

      <Button
        variant="outlined"
        startIcon={<AddIcon />}
        onClick={addChart}
        fullWidth
      >
        Add Chart
      </Button>
    </Box>
  );
};

export default ChartManagement;
