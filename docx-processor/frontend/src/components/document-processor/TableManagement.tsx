import { useEffect, useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { v4 as uuidv4 } from 'uuid';

interface ColumnConfig {
  placeholder: string;
  type: 'text' | 'date';
  format?: string;
}

interface ColumnSettingsDialogProps {
  open: boolean;
  onClose: () => void;
  config: ColumnConfig;
  onSave: (config: ColumnConfig) => void;
}

const DATE_FORMAT_OPTIONS = [
  { value: '%Y-%m-%d', label: 'YYYY-MM-DD' },
  { value: '%B %d, %Y', label: 'Month DD, YYYY' },
  { value: '%d/%m/%Y', label: 'DD/MM/YYYY' },
  { value: '%m/%d/%Y', label: 'MM/DD/YYYY' },
];

const ColumnSettingsDialog = ({
  open,
  onClose,
  config,
  onSave,
}: ColumnSettingsDialogProps) => {
  const [tempConfig, setTempConfig] = useState<ColumnConfig>(config);

  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>Column Settings</DialogTitle>
      <DialogContent>
        <Box sx={{ pt: 1, display: 'flex', flexDirection: 'column', gap: 2 }}>
          <TextField
            fullWidth
            label="Placeholder"
            value={tempConfig.placeholder}
            onChange={(e) =>
              setTempConfig({ ...tempConfig, placeholder: e.target.value })
            }
          />
          <FormControl fullWidth>
            <InputLabel>Type</InputLabel>
            <Select
              value={tempConfig.type}
              label="Type"
              onChange={(e) =>
                setTempConfig({
                  ...tempConfig,
                  type: e.target.value as 'text' | 'date',
                  format: e.target.value === 'date' ? '%Y-%m-%d' : undefined,
                })
              }
            >
              <MenuItem value="text">Text</MenuItem>
              <MenuItem value="date">Date</MenuItem>
            </Select>
          </FormControl>
          {tempConfig.type === 'date' && (
            <FormControl fullWidth>
              <InputLabel>Format</InputLabel>
              <Select
                value={tempConfig.format}
                label="Format"
                onChange={(e) =>
                  setTempConfig({ ...tempConfig, format: e.target.value })
                }
              >
                {DATE_FORMAT_OPTIONS.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={() => {
            onSave(tempConfig);
            onClose();
          }}
          variant="contained"
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

interface SpreadsheetTableProps {
  columns: ColumnConfig[];
  rows: (string | { type: string; value: string; format?: string })[][];
  onColumnsChange: (columns: ColumnConfig[]) => void;
  onRowsChange: (rows: any[][]) => void;
}

export type Value = (
  | string
  | { type: string; value: string; format?: string }
)[][];

export interface TableData {
  id: string;
  columns: ColumnConfig[];
  rows: (string | { type: string; value: string; format?: string })[][];
}

const SpreadsheetTable = ({
  columns,
  rows,
  onColumnsChange,
  onRowsChange,
}: SpreadsheetTableProps) => {
  const [editingColumn, setEditingColumn] = useState<number | null>(null);

  const addColumn = () => {
    onColumnsChange([...columns, { placeholder: '', type: 'text' }]);
  };

  const updateColumn = (index: number, config: ColumnConfig) => {
    const newColumns = [...columns];
    newColumns[index] = config;
    onColumnsChange(newColumns);
  };

  const addRow = () => {
    const newRow = columns.map((col) =>
      col.type === 'date'
        ? {
            type: 'date',
            value: new Date().toISOString().split('T')[0],
            format: col.format,
          }
        : ''
    );
    onRowsChange([...rows, newRow]);
  };

  const updateCell = (rowIndex: number, colIndex: number, value: string) => {
    const newRows = [...rows];
    const column = columns[colIndex];

    newRows[rowIndex] = [...newRows[rowIndex]];
    if (column.type === 'date') {
      newRows[rowIndex][colIndex] = {
        type: 'date',
        value,
        format: column.format,
      };
    } else {
      newRows[rowIndex][colIndex] = value;
    }

    onRowsChange(newRows);
  };

  const removeRow = (index: number) => {
    onRowsChange(rows.filter((_, i) => i !== index));
  };

  return (
    <Box>
      <TableContainer component={Paper} sx={{ mb: 2 }}>
        <Table size="small">
          <TableHead>
            <TableRow>
              {columns.map((col, index) => (
                <TableCell key={index}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <span>{col.placeholder || `Column ${index + 1}`}</span>
                    <IconButton
                      size="small"
                      onClick={() => setEditingColumn(index)}
                    >
                      <SettingsIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </TableCell>
              ))}
              <TableCell padding="checkbox">
                <IconButton size="small" onClick={addColumn}>
                  <AddIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {rows.map((row, rowIndex) => (
              <TableRow key={rowIndex}>
                {row.map((cell, colIndex) => {
                  const value = typeof cell === 'string' ? cell : cell.value;
                  return (
                    <TableCell key={colIndex}>
                      <TextField
                        size="small"
                        value={value}
                        type={
                          columns[colIndex].type === 'date' ? 'date' : 'text'
                        }
                        onChange={(e) =>
                          updateCell(rowIndex, colIndex, e.target.value)
                        }
                        fullWidth
                        variant="standard"
                      />
                    </TableCell>
                  );
                })}
                <TableCell padding="checkbox">
                  <IconButton size="small" onClick={() => removeRow(rowIndex)}>
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Button
        variant="outlined"
        startIcon={<AddIcon />}
        onClick={addRow}
        disabled={columns.length === 0}
      >
        Add Row
      </Button>

      {editingColumn !== null && (
        <ColumnSettingsDialog
          open={true}
          onClose={() => setEditingColumn(null)}
          config={columns[editingColumn]}
          onSave={(config) => updateColumn(editingColumn, config)}
        />
      )}
    </Box>
  );
};

export interface TableManagementProps {
  tables: TableData[];
  onTablesChange: (tables: TableData[]) => void;
}

export const TableManagement = ({
  tables,
  onTablesChange,
}: TableManagementProps) => {
  const [currentTables, setCurrentTables] = useState<Array<TableData>>(tables);

  useEffect(() => {
    setCurrentTables(tables);
  }, [tables]);

  const addTable = () => {
    onTablesChange([
      ...currentTables,
      {
        id: uuidv4(),
        columns: [],
        rows: [],
      },
    ]);
  };

  const removeTable = (id: string) => {
    onTablesChange(currentTables.filter((t) => t.id !== id));
  };

  return (
    <Box>
      {currentTables.map((table) => (
        <Card key={table.id} sx={{ mb: 3 }}>
          <CardHeader
            title={`Table ${currentTables.indexOf(table) + 1}`}
            action={
              <IconButton onClick={() => removeTable(table.id)} color="error">
                <DeleteIcon />
              </IconButton>
            }
          />
          <CardContent>
            <SpreadsheetTable
              columns={table.columns}
              rows={table.rows}
              onColumnsChange={(columns) => {
                setCurrentTables(
                  currentTables.map((t) =>
                    t.id === table.id
                      ? {
                          ...t,
                          columns,
                          rows: t.rows.map((row) =>
                            row.slice(0, columns.length)
                          ),
                        }
                      : t
                  )
                );
              }}
              onRowsChange={(rows) => {
                setCurrentTables(
                  currentTables.map((t) =>
                    t.id === table.id ? { ...t, rows } : t
                  )
                );
              }}
            />
          </CardContent>
        </Card>
      ))}

      <Button
        variant="outlined"
        startIcon={<AddIcon />}
        onClick={addTable}
        fullWidth
      >
        Add Table
      </Button>
    </Box>
  );
};
