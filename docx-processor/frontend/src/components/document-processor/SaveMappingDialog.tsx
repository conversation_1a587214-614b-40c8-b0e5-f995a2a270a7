import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  TextField,
} from '@mui/material';

interface SaveMappingDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: () => void;
  mappingName: string;
  setMappingName: (name: string) => void;
}

const SaveMappingDialog = ({
  open,
  onClose,
  onSave,
  mappingName,
  setMappingName,
}: SaveMappingDialogProps) => {
  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Save Mapping Configuration</DialogTitle>
      <DialogContent>
        <TextField
          fullWidth
          label="Mapping Name"
          value={mappingName}
          onChange={(e) => setMappingName(e.target.value)}
          margin="normal"
          placeholder="Enter mapping name"
          helperText=".json will be added automatically if not present"
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button 
          onClick={onSave} 
          variant="contained"
          disabled={!mappingName.trim()}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SaveMappingDialog;