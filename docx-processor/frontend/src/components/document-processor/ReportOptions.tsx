import axios from 'axios';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  IconButton,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  CloudDownload as DownloadIcon,
} from '@mui/icons-material';
import { downloadFile } from './utils';
import { ReportOptionsProps } from './shared/types';

const ReportOptions = ({ showSnackbar }: ReportOptionsProps) => {
  const navigate = useNavigate();
  const [template, setTemplate] = useState('');
  const [options, setOptions] = useState({
    includeCharts: true,
    includeTables: true,
  });

  // Placeholder data - would come from API in real app
  const templates = [
    { id: 'standard', name: 'Standard Report' },
    { id: 'detailed', name: 'Detailed Report' },
    { id: 'executive', name: 'Executive Summary' },
  ];

  const handleGeneration = async () => {
    try {
      const response = await axios.post(
        '/api/generate',
        {},
        {
          responseType: 'blob',
        }
      );

      downloadFile(response.data, 'generated-document.docx');
      showSnackbar('Document generated successfully', 'success');
    } catch (error) {
      console.error('Error generating document:', error);
      showSnackbar('Error generating document', 'error');
    }
  };

  return (
    <Container sx={{ py: 4 }}>
      <Box display="flex" alignItems="center" gap={2} mb={4}>
        <IconButton onClick={() => navigate('/')} size="large">
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4" component="h1" fontWeight="bold">
          Report Configuration
        </Typography>
      </Box>

      <Box maxWidth="md">
        {/* Template Selection */}
        <Card sx={{ mb: 3 }}>
          <CardHeader title="Report Template" />
          <CardContent>
            <FormControl fullWidth>
              <InputLabel id="template-select-label">
                Select Template
              </InputLabel>
              <Select
                labelId="template-select-label"
                id="template-select"
                value={template}
                label="Select Template"
                onChange={(e) => setTemplate(e.target.value)}
              >
                {templates.map((t) => (
                  <MenuItem key={t.id} value={t.id}>
                    {t.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </CardContent>
        </Card>

        {/* Report Options */}
        <Card sx={{ mb: 3 }}>
          <CardHeader title="Content Options" />
          <CardContent>
            <Box display="flex" flexDirection="column" gap={2}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={options.includeCharts}
                    onChange={(e) =>
                      setOptions((prev) => ({
                        ...prev,
                        includeCharts: e.target.checked,
                      }))
                    }
                  />
                }
                label="Include Charts and Graphs"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={options.includeTables}
                    onChange={(e) =>
                      setOptions((prev) => ({
                        ...prev,
                        includeTables: e.target.checked,
                      }))
                    }
                  />
                }
                label="Include Detailed Tables"
              />
            </Box>
          </CardContent>
        </Card>

        {/* Generate Button */}
        <Box display="flex" justifyContent="flex-end">
          <Button
            variant="contained"
            size="large"
            disabled={!template}
            startIcon={<DownloadIcon />}
            onClick={handleGeneration}
          >
            Generate Report
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default ReportOptions;
