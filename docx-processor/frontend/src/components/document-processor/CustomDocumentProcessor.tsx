import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Button,
  IconButton,
  TextField,
  Alert,
  InputAdornment,
  Grid,
  CircularProgress,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  CloudDownload as DownloadIcon,
  Add as AddIcon,
  Save as SaveIcon,
  Upload as UploadIcon,
} from '@mui/icons-material';
import AutoFixHighIcon from '@mui/icons-material/AutoFixHigh';
import { downloadFile } from './utils';
import axios from 'axios';
import SaveMappingDialog from './SaveMappingDialog';
import { TableManagement, TableData, Value } from './TableManagement';
import ChartManagement, { ChartData } from './ChartManagement';
import { v4 as uuidv4 } from 'uuid';
import ExtractPlaceholdersDialog from './ExtractPlaceholdersDialog';
import { SectionData, SectionManagement } from './SectionManagement';

interface PlaceholderValue {
  placeholder: string;
  value: string;
}

interface CustomDocumentProcessorProps {
  showSnackbar: (message: string, severity: 'success' | 'error') => void;
}

const CustomDocumentProcessor = ({
  showSnackbar,
}: CustomDocumentProcessorProps) => {
  const navigate = useNavigate();
  const [template, setTemplate] = useState<File | null>(null);
  const [placeholders, setPlaceholders] = useState<PlaceholderValue[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [saveDialogOpen, setSaveDialogOpen] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [mappingName, setMappingName] = useState('');
  const [tables, setTables] = useState<TableData[]>([]);
  const [charts, setCharts] = useState<ChartData[]>([]);
  const [sections, setSections] = useState<SectionData[]>([]);
  const [extractDialogOpen, setExtractDialogOpen] = useState(false);

  // Add downloadMapping function
  const downloadMapping = () => {
    const mapping = {
      ...placeholders.reduce(
        (acc, { placeholder, value }) => ({ ...acc, [placeholder]: value }),
        {}
      ),
      __array_data__: getTableDataForBackend(),
      __charts__: charts,
      __sections__: getSectionsDataForBackend(),
    };
    const filename = mappingName || 'mapping.json';
    const blob = new Blob([JSON.stringify(mapping, null, 2)], {
      type: 'application/json',
    });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename.endsWith('.json') ? filename : `${filename}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    setSaveDialogOpen(false);
    setMappingName('');
  };

  const handleTemplateUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setTemplate(file);
    }
  };

  const handleMappingUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const text = await file.text();
      const mapping = JSON.parse(text);

      setCharts([]);
      setTables([]);
      setPlaceholders([]);
      setSections([]);

      const tableData = mapping.__array_data__ || [];
      setTables(
        tableData.map(
          (table: { placeholders: string[]; values: Value[][] }) => ({
            id: uuidv4(),
            columns: table.placeholders.map((placeholder: string) => ({
              placeholder,
              type: 'text',
            })),
            rows: table.values,
          })
        )
      );

      const chartData = mapping.__charts__ || [];
      setCharts(
        chartData.map((chart: ChartData) => ({
          ...chart,
          id: uuidv4(),
        }))
      );

      const sectionData = mapping.__sections__ || [];
      setSections(
        sectionData.map((section: SectionData) => ({
          ...section,
          id: uuidv4()
        }))
      );

      delete mapping.__array_data__;
      delete mapping.__charts__;
      delete mapping.__sections__;

      const newPlaceholders = Object.entries(mapping).map(
        ([placeholder, value]) => ({
          placeholder,
          value: value as string,
        })
      );
      setPlaceholders(newPlaceholders);
    } catch (error) {
      console.error('Error parsing mapping file:', error);
    }
  };

  const addPlaceholder = () => {
    setPlaceholders([...placeholders, { placeholder: '', value: '' }]);
  };

  const removePlaceholder = (index: number) => {
    setPlaceholders(placeholders.filter((_, i) => i !== index));
  };

  const updatePlaceholder = (
    index: number,
    field: keyof PlaceholderValue,
    value: string
  ) => {
    setPlaceholders(
      placeholders.map((p, i) => (i === index ? { ...p, [field]: value } : p))
    );
  };

  // Check for duplicate placeholders
  const getDuplicates = () => {
    const counts = new Map<string, number>();
    placeholders.forEach(({ placeholder }) => {
      if (placeholder) {
        counts.set(placeholder, (counts.get(placeholder) || 0) + 1);
      }
    });
    return Array.from(counts.entries())
      .filter(([_, count]) => count > 1)
      .map(([placeholder]) => placeholder);
  };

  const duplicatePlaceholders = getDuplicates();

  const filteredPlaceholders = placeholders.filter(
    ({ placeholder, value }) =>
      placeholder.toLowerCase().includes(searchTerm.toLowerCase()) ||
      value.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getTableDataForBackend = () => {
    return tables.map((table) => ({
      placeholders: table.columns.map((col) => col.placeholder),
      values: table.rows,
    }));
  };

  const getSectionsDataForBackend = () => {
    return sections.map((section) => ({
      start: section.start,
      end: section.end,
      state: section.state ? section.state : undefined,
    }));
  };

  const handleGeneration = async () => {
    if (!template || placeholders.length === 0) return;

    try {
      // Create form data
      const formData = new FormData();
      formData.append('template', template);

      // Create replacements object from placeholders
      const replacements = {
        ...placeholders.reduce((acc, { placeholder, value }) => {
          if (placeholder && value) {
            acc[placeholder] = value;
          }
          return acc;
        }, {} as Record<string, string>),
        __array_data__: getTableDataForBackend(),
        __charts__: charts,
        __sections__: getSectionsDataForBackend(),
      };

      formData.append('replacements', JSON.stringify(replacements));

      const response = await axios.post('/api/process-custom', formData, {
        responseType: 'blob',
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      downloadFile(response.data, 'processed-document.docx');
      showSnackbar('Document processed successfully', 'success');
    } catch (error) {
      console.error('Error processing document:', error);
      showSnackbar('Error processing document', 'error');
    }
  };

  const handleExtractedPlaceholders = (extractedPlaceholders: string[]) => {
    const newPlaceholders = extractedPlaceholders.map((placeholder) => ({
      placeholder,
      value: '',
    }));

    setPlaceholders([...placeholders, ...newPlaceholders]);
    showSnackbar(`Added ${newPlaceholders.length} new placeholders`, 'success');
  };

  return (
    <Container sx={{ py: 4 }}>
      <Box display="flex" alignItems="center" gap={2} mb={4}>
        <IconButton onClick={() => navigate('/')} size="large">
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4" component="h1" fontWeight="bold">
          Custom Document Processing
        </Typography>
      </Box>

      <Box display="flex" flexDirection="column" gap={3}>
        {/* Template Upload */}
        <Card>
          <CardHeader title="Document Template" />
          <CardContent>
            <Box display="flex" gap={2} alignItems="center">
              <Button
                variant="outlined"
                component="label"
                startIcon={<UploadIcon />}
              >
                Upload Template
                <input
                  type="file"
                  hidden
                  accept=".docx"
                  onChange={handleTemplateUpload}
                />
              </Button>
              {template && (
                <>
                  <Typography color="success.main">
                    Template: {template.name}
                  </Typography>
                  <Button
                    variant="outlined"
                    startIcon={<AutoFixHighIcon />}
                    onClick={() => setExtractDialogOpen(true)}
                    disabled={!template}
                  >
                    Extract Placeholders
                  </Button>
                </>
              )}
            </Box>
          </CardContent>
        </Card>

        <ExtractPlaceholdersDialog
          open={extractDialogOpen}
          onClose={() => setExtractDialogOpen(false)}
          onExtracted={handleExtractedPlaceholders}
          template={template}
          existingPlaceholders={placeholders.map((p) => p.placeholder)}
        />

        {/* Values Section */}
        <Card>
          <CardHeader
            title="Placeholder Values"
            subheader="Define the values to replace placeholders in your template"
            action={
              <Box display="flex" gap={1}>
                <Button
                  variant="outlined"
                  startIcon={<SaveIcon />}
                  onClick={() => setSaveDialogOpen(true)}
                >
                  Save Values
                </Button>
                <Button
                  variant="outlined"
                  component="label"
                  startIcon={<UploadIcon />}
                >
                  Load Values
                  <input
                    type="file"
                    hidden
                    accept=".json"
                    onChange={handleMappingUpload}
                  />
                </Button>
              </Box>
            }
          />
          <CardContent>
            <Box display="flex" flexDirection="column" gap={4}>
              {/* Simple Mappings */}
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Simple Placeholders
                </Typography>
                {/* Search and placeholders list */}
                {/* Search */}
                <Box mb={3}>
                  <TextField
                    fullWidth
                    placeholder="Search placeholders..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    size="small"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Box>

                {/* Duplicate Warning */}
                {duplicatePlaceholders.length > 0 && (
                  <Alert severity="warning" sx={{ mb: 2 }}>
                    Duplicate placeholders found:{' '}
                    {duplicatePlaceholders.join(', ')}. Last value will be used
                    during generation.
                  </Alert>
                )}

                {/* Placeholders List */}
                <Box display="flex" flexDirection="column" gap={2}>
                  {filteredPlaceholders.map((item, index) => (
                    <Grid container key={index} spacing={2} alignItems="center">
                      <Grid item xs={5}>
                        <TextField
                          fullWidth
                          label="Placeholder"
                          value={item.placeholder}
                          onChange={(e) =>
                            updatePlaceholder(
                              index,
                              'placeholder',
                              e.target.value
                            )
                          }
                          error={duplicatePlaceholders.includes(
                            item.placeholder
                          )}
                          helperText={
                            duplicatePlaceholders.includes(item.placeholder)
                              ? 'Duplicate placeholder'
                              : ''
                          }
                          size="small"
                        />
                      </Grid>
                      <Grid item xs={5}>
                        <TextField
                          fullWidth
                          label="Value"
                          value={item.value}
                          onChange={(e) =>
                            updatePlaceholder(index, 'value', e.target.value)
                          }
                          size="small"
                        />
                      </Grid>
                      <Grid item xs={2}>
                        <IconButton
                          onClick={() => removePlaceholder(index)}
                          color="error"
                          size="small"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Grid>
                    </Grid>
                  ))}

                  <Button
                    variant="outlined"
                    onClick={addPlaceholder}
                    startIcon={<AddIcon />}
                    sx={{ mt: 1 }}
                  >
                    Add Placeholder
                  </Button>
                </Box>
              </Box>

              {/* Table Data */}
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Table Placeholders
                </Typography>
                <TableManagement tables={tables} onTablesChange={setTables} />
              </Box>

              {/* Sections Data */}
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Sections Placeholders
                </Typography>
                <SectionManagement
                  sections={sections}
                  onSectionsChange={setSections}
                />
              </Box>

              {/* Chart Data */}
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Chart Placeholders
                </Typography>
                <ChartManagement charts={charts} onChartsChange={setCharts} />
              </Box>
            </Box>
          </CardContent>
        </Card>

        {/* Add SaveMappingDialog */}
        <SaveMappingDialog
          open={saveDialogOpen}
          onClose={() => {
            setSaveDialogOpen(false);
            setMappingName('');
          }}
          onSave={downloadMapping}
          mappingName={mappingName}
          setMappingName={setMappingName}
        />

        {/* Generate Button */}
        <Box display="flex" justifyContent="flex-end">
          <Button
            variant="contained"
            size="large"
            disabled={!template || placeholders.length === 0 || generating}
            startIcon={
              generating ? <CircularProgress size={24} /> : <DownloadIcon />
            }
            onClick={async () => {
              setGenerating(true);
              try {
                await handleGeneration();
              } finally {
                setGenerating(false);
              }
            }}
          >
            Generate Document
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default CustomDocumentProcessor;
