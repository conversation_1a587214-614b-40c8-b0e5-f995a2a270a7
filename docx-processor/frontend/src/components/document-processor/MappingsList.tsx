import {
  Box,
  TextField,
  IconButton,
  Grid,
  InputAdornment,
} from '@mui/material';
import { Delete as DeleteIcon } from '@mui/icons-material';
import { CategoryCircle } from './shared/CategoryStyles';
import { CategoryDefinition } from './shared/types';

interface Mapping {
  logicalKey: string;
  templateKey: string;
  category: string;
}

interface MappingsListProps {
  mappings: Mapping[];
  categories: Record<string, CategoryDefinition>;
  onUpdateMapping: (logicalKey: string, templateKey: string) => void;
  onRemoveMapping: (logicalKey: string) => void;
}

const MappingsList = ({
  mappings,
  categories,
  onUpdateMapping,
  onRemoveMapping,
}: MappingsListProps) => {
  // Create pairs of mappings for two-column layout
  const mappingPairs: Mapping[][] = [];
  for (let i = 0; i < mappings.length; i += 2) {
    mappingPairs.push(mappings.slice(i, i + 2) as Mapping[]);
  }

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      {mappingPairs.map((pair, rowIndex) => (
        <Grid container spacing={2} key={rowIndex}>
          {pair.map((mapping) => {
            const category = categories[mapping.category];
            return (
              <Grid item xs={12} sm={6} key={mapping.logicalKey}>
                <Box display="flex" gap={1}>
                  <TextField
                    fullWidth
                    size="small"
                    label={mapping.logicalKey}
                    value={mapping.templateKey}
                    onChange={(e) => onUpdateMapping(mapping.logicalKey, e.target.value)}
                    placeholder="Enter placeholder as it appears in template"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <CategoryCircle sx={{ bgcolor: category?.color || '#ccc' }} />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': {
                          borderColor: category?.color || '#ccc',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: category?.color || '#ccc',
                          borderWidth: '2px',
                        },
                      },
                    }}
                  />
                  <IconButton
                    onClick={() => onRemoveMapping(mapping.logicalKey)}
                    color="error"
                    size="small"
                    sx={{ mt: 1 }}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              </Grid>
            );
          })}
        </Grid>
      ))}
    </Box>
  );
};

export default MappingsList;