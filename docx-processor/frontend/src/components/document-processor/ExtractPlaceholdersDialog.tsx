import { useState } from 'react';
import {
  <PERSON>alog,
  DialogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  CircularProgress,
  Alert,
  Box,
  Typography,
} from '@mui/material';
import axios from 'axios';

interface ExtractPlaceholdersDialogProps {
  open: boolean;
  onClose: () => void;
  onExtracted: (placeholders: string[]) => void;
  template: File | null;
  existingPlaceholders: string[];
}

export const ExtractPlaceholdersDialog = ({
  open,
  onClose,
  onExtracted,
  template,
}: ExtractPlaceholdersDialogProps) => {
  const [pattern, setPattern] = useState<string>('\\[(.*?)\\]');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleExtract = async () => {
    if (!template) return;

    setLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('template', template);
      formData.append('pattern', pattern);

      const response = await axios.post('/api/extract-placeholders', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      onExtracted(response.data.placeholders);
      onClose();
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to extract placeholders'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Extract Placeholders</DialogTitle>
      <DialogContent>
        <Box sx={{ pt: 1 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Enter a regular expression pattern to match placeholders in your
            template. The default pattern matches text within square brackets.
          </Typography>

          <TextField
            fullWidth
            label="Placeholder Pattern"
            value={pattern}
            onChange={(e) => setPattern(e.target.value)}
            placeholder="Enter regex pattern"
            helperText="Example: \[(.*?)\] matches [placeholder]"
            sx={{ mb: 2 }}
          />

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
        </Box>
      </DialogContent>
      <DialogActions sx={{ p: 2 }}>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleExtract}
          variant="contained"
          disabled={loading || !pattern.trim()}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          Extract Placeholders
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ExtractPlaceholdersDialog;
