import {
  Grid,
  TextField,
  MenuItem,
  InputAdornment,
} from '@mui/material';
import { Search as SearchIcon } from '@mui/icons-material';
import { CategoryCircle } from './shared/CategoryStyles';
import { CategoryDefinition } from './shared/types';

interface PlaceholderFiltersProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  categories: Record<string, CategoryDefinition>;
}

const PlaceholderFilters = ({
  searchTerm,
  onSearchChange,
  selectedCategory,
  onCategoryChange,
  categories,
}: PlaceholderFiltersProps) => {
  return (
    <Grid container spacing={2} sx={{ mb: 3 }}>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          placeholder="Search placeholders..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          size="small"
        />
      </Grid>
      <Grid item xs={12} md={3}>
        <TextField
          select
          fullWidth
          value={selectedCategory}
          onChange={(e) => onCategoryChange(e.target.value)}
          size="small"
        >
          <MenuItem value="all">
            <CategoryCircle sx={{ bgcolor: 'grey.400' }} />
            All Categories
          </MenuItem>
          {Object.entries(categories).map(([name, category]) => (
            <MenuItem key={name} value={name}>
              <CategoryCircle sx={{ bgcolor: category.color }} />
              {category.label} ({category.keys.length})
            </MenuItem>
          ))}
        </TextField>
      </Grid>
    </Grid>
  );
};

export default PlaceholderFilters;