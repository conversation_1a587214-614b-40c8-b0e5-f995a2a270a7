import {
  Box,
  Typography,
  Paper,
  Button,
  IconButton,
  Fade,
} from '@mui/material';
import { Description, ArrowBack } from '@mui/icons-material';
import axios from 'axios';
import { ReportOptionsProps } from './shared/types';
import { downloadFile } from './utils';

const DefaultGeneration = ({ onBack, showSnackbar }: ReportOptionsProps) => {
  const handleGeneration = async () => {
    try {
      const response = await axios.post(
        '/api/generate',
        {},
        {
          responseType: 'blob',
        }
      );

      downloadFile(response.data, 'generated-document.docx');
      showSnackbar('Document generated successfully', 'success');
    } catch (error) {
      console.error('Error generating document:', error);
      showSnackbar('Error generating document', 'error');
    }
  };

  return (
    <Fade in>
      <Box>
        <Box mb={3} display="flex" alignItems="center" gap={1}>
          <IconButton onClick={onBack} color="primary">
            <ArrowBack />
          </IconButton>
          <Typography variant="h6">Quick Generation</Typography>
        </Box>

        <Paper sx={{ p: 3 }}>
          <Typography variant="body1" paragraph>
            Generate a document using our default template with predefined
            mappings.
          </Typography>
          <Button
            variant="contained"
            startIcon={<Description />}
            onClick={handleGeneration}
            size="large"
          >
            Generate Document
          </Button>
        </Paper>
      </Box>
    </Fade>
  );
};

export default DefaultGeneration;
