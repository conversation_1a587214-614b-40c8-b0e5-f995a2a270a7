import { Routes, Route, Navigate } from 'react-router-dom';
import DocumentProcessor from '../components/document-processor/DocumentProcessor';
import CustomTemplateReport from '../components/document-processor/CustomTemplateReport';
import ReportOptions from '../components/document-processor/ReportOptions';
import CustomDocumentProcessor from '../components/document-processor/CustomDocumentProcessor';
import { Alert, Snackbar } from '@mui/material';
import { useState } from 'react';
import { SnackbarState } from '../components/document-processor/shared/types';

export function App() {
  const [snackbar, setSnackbar] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'success',
  });

  const showSnackbar = (message: string, severity: 'success' | 'error') => {
    setSnackbar({ open: true, message, severity });
  };

  return (
    <div>
      <Routes>
        {/* Redirect root to document processor */}
        <Route path="/" element={<DocumentProcessor />} />
        {/* Document Processing Routes */}
        <Route
          path="/custom/new"
          element={<CustomDocumentProcessor showSnackbar={showSnackbar} />}
        />
        <Route
          path="/custom-template/new"
          element={<CustomTemplateReport showSnackbar={showSnackbar} />}
        />
        <Route
          path="/report/options"
          element={<ReportOptions showSnackbar={showSnackbar} />}
        />
        {/* Catch all route - redirect to home */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar((prev) => ({ ...prev, open: false }))}
      >
        <Alert
          onClose={() => setSnackbar((prev) => ({ ...prev, open: false }))}
          severity={snackbar.severity}
          variant="filled"
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </div>
  );
}

export default App;
