{"migrations": [{"version": "20.3.0-beta.2", "description": "Add gitignore entry for temporary vitest config files.", "implementation": "./src/migrations/update-20-3-0/add-vitest-temp-files-to-git-ignore", "package": "@nx/vite", "name": "update-20-3-0"}, {"version": "20.3.0-beta.1", "description": "Update ESLint flat config to include .cjs, .mjs, .cts, and .mts files in overrides (if needed)", "implementation": "./src/migrations/update-20-3-0/add-file-extensions-to-overrides", "package": "@nx/eslint", "name": "add-file-extensions-to-overrides"}, {"cli": "nx", "version": "20.3.0-beta.2", "description": "If workspace includes Module Federation projects, ensure the new @nx/module-federation package is installed.", "factory": "./src/migrations/update-20-3-0/ensure-nx-module-federation-package", "package": "@nx/react", "name": "ensure-nx-module-federation-package"}]}