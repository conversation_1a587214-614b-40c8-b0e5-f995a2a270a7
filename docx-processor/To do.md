# To do

User guide
Bar generated chart is not the same as the preview,

<img src="charts-diff-bar.png"> 

Pie is also different

<img src="charts-diff-pie.png"> 

Convert to Angular

## Any

Refactor python code to use Path instead of strings
Template uploading and management
Visual placeholder mapping configuration
Replacement value management
Document preview
Batch processing
API endpoints for integration with other systems

## Frontend

Some animations when switching between modes
More detailed explanations or tooltips
A "recently generated documents" list
Save/load mapping presets

## Document generation

Add support for additional types (like percentages)?
Add more specific validation messages?
Add examples of valid data structures in the schema documentation?
More chart types (scatter plots, bubble charts, etc.)
Custom color themes for consistent report styling (charts)
Automatic chart scaling based on content

Chart data processing (like aggregating data from tables)
Interactive charts (though this would require a different output format than DOCX)