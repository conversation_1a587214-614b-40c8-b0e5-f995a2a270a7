{"compilerOptions": {"allowJs": false, "allowSyntheticDefaultImports": true, "composite": true, "declaration": true, "declarationMap": true, "emitDeclarationOnly": true, "emitDecoratorMetadata": false, "esModuleInterop": true, "experimentalDecorators": false, "forceConsistentCasingInFileNames": true, "importHelpers": true, "incremental": true, "isolatedModules": true, "lib": ["es2022"], "module": "NodeNext", "moduleResolution": "NodeNext", "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": true, "pretty": true, "removeComments": false, "resolveJsonModule": false, "skipDefaultLibCheck": false, "skipLibCheck": true, "sourceMap": false, "strict": true, "target": "es2022", "verbatimModuleSyntax": false}}