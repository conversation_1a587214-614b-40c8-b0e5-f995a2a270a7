[tool.coverage.run]
branch = true
source = [ "cli" ]

[tool.coverage.report]
exclude_lines = ['if TYPE_CHECKING:']
show_missing = true

[tool.pytest.ini_options]
addopts = "--cov --cov-fail-under=100 --cov-report html:'../../coverage/packages/cli/html' --cov-report xml:'../../coverage/packages/cli/coverage.xml' --junitxml='../../reports/packages/cli/unittests/junit.xml' --html='../../reports/packages/cli/unittests/html/index.html'"

[tool.poetry]
name = "docx_processor_cli"
version = "1.0.0"
description = "CLI for Word template processing"
authors = [ ]
license = 'Proprietary'
readme = 'README.md'

  [[tool.poetry.packages]]
  include = "cli"

  [tool.poetry.dependencies]
  python = ">=3.8,<3.11"

  

[build-system]
requires = ["poetry-core==1.1.0"]
build-backend = "poetry.core.masonry.api"
