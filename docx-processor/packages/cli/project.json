{"name": "cli", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "packages/cli/cli", "targets": {"docs": {"executor": "@nxlv/python:run-commands", "options": {"command": "pydoc-markdown -p cli --render-toc > docs/source/api.md", "cwd": "packages/cli"}}, "lock": {"executor": "@nxlv/python:run-commands", "options": {"command": "poetry lock --no-update", "cwd": "packages/cli"}}, "add": {"executor": "@nxlv/python:add", "options": {}}, "update": {"executor": "@nxlv/python:update", "options": {}}, "remove": {"executor": "@nxlv/python:remove", "options": {}}, "build": {"executor": "@nxlv/python:build", "outputs": ["{projectRoot}/dist"], "options": {"outputPath": "packages/cli/dist", "publish": true, "lockedVersions": true, "bundleLocalDependencies": true}}, "install": {"executor": "@nxlv/python:install", "options": {"silent": false, "args": "", "cacheDir": ".cache/pypoetry", "verbose": false, "debug": false}}, "lint": {"executor": "@nxlv/python:flake8", "outputs": ["{workspaceRoot}/reports/packages/cli/pylint.txt"], "options": {"outputFile": "reports/packages/cli/pylint.txt"}}, "test": {"executor": "@nxlv/python:run-commands", "outputs": ["{workspaceRoot}/reports/packages/cli/unittests", "{workspaceRoot}/coverage/packages/cli"], "options": {"command": "poetry run pytest tests/", "cwd": "packages/cli"}}, "tox": {"executor": "@nxlv/python:tox", "outputs": ["{workspaceRoot}/reports/packages/cli/unittests", "{workspaceRoot}/coverage/packages/cli"], "options": {"silent": false, "args": ""}}}, "tags": []}