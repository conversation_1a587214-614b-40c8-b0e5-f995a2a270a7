[tool.coverage.run]
branch = true
source = ["docx_processor"]

[tool.coverage.report]
exclude_lines = ['if TYPE_CHECKING:']
show_missing = true

[tool.pytest.ini_options]
addopts = "--cov --cov-fail-under=100 --cov-report html:'../../coverage/packages/docx-processor/html' --cov-report xml:'../../coverage/packages/docx-processor/coverage.xml' --junitxml='../../reports/packages/docx-processor/unittests/junit.xml' --html='../../reports/packages/docx-processor/unittests/html/index.html'"

[tool.poetry]
name = "docx_processor"
version = "1.0.0"
description = "A tool for processing Word document templates with placeholder replacements"
authors = ["<PERSON> <<EMAIL>>"]
license = 'Proprietary'
readme = 'README.md'

[[tool.poetry.packages]]
include = "docx_processor"

[tool.poetry.dependencies]
python = ">=3.8.1"
python-docx = "^1.1.2"
jsonschema = ">=4.17.3"
matplotlib = ">=3.0.0"
numpy = ">=1.19.0"
beautifulsoup4 = "^4.13.3"

[tool.poetry.group.dev.dependencies]
pytest = ">=7.0"
black = ">=22.0"
isort = ">=5.0"
mypy = ">=1.0"
pylint = ">=2.17.0,<3.0.0"
flake8 = ">=6.0.0,<7.0.0"

[tool.poetry.scripts]
docx-processor = "docx_processor.cli:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
