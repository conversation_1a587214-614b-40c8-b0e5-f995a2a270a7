"""Document processor package."""

from .processor import DocxTemplateProcessor
from .settings import Settings, load_replacements
from .placeholder_mapping import PlaceholderMapper
from .table_processor import TableAwareDocxTemplateProcessor
from .chart_processor import ChartProcessor
from .html_handler import HtmlHandler, contains_html
from .style_manager import ensure_required_styles, ensure_style, copy_styles_from_template

__version__ = "1.0.0"

__all__ = [
    "ChartProcessor",
    "DocxTemplateProcessor",
    "TableAwareDocxTemplateProcessor", 
    "Settings", 
    "load_replacements", 
    "PlaceholderMapper",
    "HtmlHandler",
    "contains_html",
    "ensure_required_styles",
    "ensure_style",
    "copy_styles_from_template"
]