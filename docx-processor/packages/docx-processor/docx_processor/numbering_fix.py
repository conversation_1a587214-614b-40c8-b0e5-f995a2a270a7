"""Fix for the numbering.xml issue.

This module provides functions to ensure proper numbering in Word documents,
working around limitations in the python-docx library.
"""

import os
import tempfile
from zipfile import ZipFile
from lxml import etree


def ensure_numbering_part(doc):
    """
    Ensure that the document has a numbering part.

    Since python-docx does not fully implement numbering part creation,
    this function saves the document, adds numbering.xml to it directly,
    and then loads it back.

    Args:
        doc: The document to modify

    Returns:
        The modified document with numbering support
    """
    # Check if document already has numbering relationships
    if hasattr(doc.part, "rels"):
        for rel in doc.part.rels.values():
            if "numbering" in rel.reltype:
                # Document already has numbering
                return doc

    # We need to add numbering.xml manually through zip manipulation
    # First, save the document to a temporary file
    temp_path = tempfile.NamedTemporaryFile(delete=False, suffix=".docx").name
    doc.save(temp_path)

    # Create a new temporary file for the modified document
    output_path = tempfile.NamedTemporaryFile(delete=False, suffix=".docx").name

    try:
        # Open the saved document as a zip
        with ZipFile(temp_path, "r") as docx_zip:
            # Create a new zip file for the modified document
            with ZipFile(output_path, "w") as new_zip:
                # Copy all existing files
                for item in docx_zip.infolist():
                    new_zip.writestr(item, docx_zip.read(item.filename))

                # Add the numbering.xml file
                new_zip.writestr("word/numbering.xml", create_basic_numbering_xml())

                # Update the content types to include numbering
                if "[Content_Types].xml" in docx_zip.namelist():
                    content_types = docx_zip.read("[Content_Types].xml")
                    updated_content_types = update_content_types(content_types)
                    new_zip.writestr("[Content_Types].xml", updated_content_types)

                # Update the document relationships to include numbering
                if "word/_rels/document.xml.rels" in docx_zip.namelist():
                    rels = docx_zip.read("word/_rels/document.xml.rels")
                    updated_rels = update_document_rels(rels)
                    new_zip.writestr("word/_rels/document.xml.rels", updated_rels)

        # Load the modified document
        from docx import Document

        modified_doc = Document(output_path)

        # Clean up temporary files
        os.unlink(temp_path)
        os.unlink(output_path)

        return modified_doc

    except Exception as e:
        # Clean up on error
        if os.path.exists(temp_path):
            os.unlink(temp_path)
        if os.path.exists(output_path):
            os.unlink(output_path)
        # If we couldn't add numbering, return the original document
        print(f"Error adding numbering: {str(e)}")
        return doc


def create_basic_numbering_xml():
    """Create a basic numbering.xml file with bullet and number list definitions."""
    numbering_xml = """<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:numbering xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
  <w:abstractNum w:abstractNumId="0">
    <w:nsid w:val="FFFFFF89"/>
    <w:multiLevelType w:val="hybridMultilevel"/>
    <w:tmpl w:val="29761A62"/>
    <w:lvl w:ilvl="0">
      <w:start w:val="1"/>
      <w:numFmt w:val="bullet"/>
      <w:pStyle w:val="ListBullet"/>
      <w:lvlText w:val="•"/>
      <w:lvlJc w:val="left"/>
      <w:pPr>
        <w:tabs>
          <w:tab w:val="num" w:pos="720"/>
        </w:tabs>
        <w:ind w:left="720" w:hanging="360"/>
      </w:pPr>
      <w:rPr>
        <w:rFonts w:ascii="Symbol" w:hAnsi="Symbol" w:hint="default"/>
      </w:rPr>
    </w:lvl>
  </w:abstractNum>
  <w:abstractNum w:abstractNumId="1">
    <w:nsid w:val="FFFFFF88"/>
    <w:multiLevelType w:val="hybridMultilevel"/>
    <w:tmpl w:val="D0A62B40"/>
    <w:lvl w:ilvl="0">
      <w:start w:val="1"/>
      <w:numFmt w:val="decimal"/>
      <w:pStyle w:val="ListNumber"/>
      <w:lvlText w:val="%1."/>
      <w:lvlJc w:val="left"/>
      <w:pPr>
        <w:tabs>
          <w:tab w:val="num" w:pos="720"/>
        </w:tabs>
        <w:ind w:left="720" w:hanging="360"/>
      </w:pPr>
    </w:lvl>
  </w:abstractNum>
  <w:num w:numId="1">
    <w:abstractNumId w:val="0"/>
  </w:num>
  <w:num w:numId="2">
    <w:abstractNumId w:val="1"/>
  </w:num>
</w:numbering>
"""
    return numbering_xml.encode("utf-8")


def update_content_types(content_types):
    """
    Update the content types XML to include the numbering part.

    Args:
        content_types: The existing content types XML as bytes

    Returns:
        The updated content types XML as bytes
    """
    root = etree.fromstring(content_types)

    # Check if the numbering content type already exists
    numbering_type_exists = False
    for tag in root.findall(".//{*}Override"):
        if tag.get("PartName") == "/word/numbering.xml":
            numbering_type_exists = True
            break

    # Add numbering content type if it doesn't exist
    if not numbering_type_exists:
        override = etree.Element(
            "Override",
            PartName="/word/numbering.xml",
            ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml",
        )
        root.append(override)

    return etree.tostring(root, xml_declaration=True, encoding="UTF-8", standalone=True)


def update_document_rels(rels):
    """
    Update the document relationships XML to include the numbering part.

    Args:
        rels: The existing relationships XML as bytes

    Returns:
        The updated relationships XML as bytes
    """
    root = etree.fromstring(rels)

    # Check if the numbering relationship already exists
    numbering_rel_exists = False
    highest_id = 0

    for rel in root.findall(".//{*}Relationship"):
        rel_id = rel.get("Id", "")
        if rel_id.startswith("rId"):
            try:
                id_num = int(rel_id[3:])
                highest_id = max(highest_id, id_num)
            except ValueError:
                pass

        if rel.get("Type", "").endswith("/numbering"):
            numbering_rel_exists = True
            break

    # Add numbering relationship if it doesn't exist
    if not numbering_rel_exists:
        new_id = f"rId{highest_id + 1}"
        rel = etree.Element(
            "Relationship",
            Id=new_id,
            Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/numbering",
            Target="numbering.xml",
        )
        root.append(rel)

    return etree.tostring(root, xml_declaration=True, encoding="UTF-8", standalone=True)


def apply_list_formatting(paragraph, is_bullet=True, num_id=None):
    """
    Apply list formatting to a paragraph.

    Args:
        paragraph: The paragraph to format
        is_bullet: Whether to use bullet (True) or numbering (False)
        num_id: Optional specific numbering ID to use
    """
    from docx.oxml.shared import OxmlElement, qn

    # Use default IDs if none provided
    if num_id is None:
        num_id = 1 if is_bullet else 2

    # Apply to paragraph
    p_element = paragraph._p
    p_props = p_element.get_or_add_pPr()

    # Create numbering properties
    num_pr = OxmlElement("w:numPr")

    # Set level (always 0 for simple lists)
    ilvl = OxmlElement("w:ilvl")
    ilvl.set(qn("w:val"), "0")
    num_pr.append(ilvl)

    # Set numbering ID
    num_id_element = OxmlElement("w:numId")
    num_id_element.set(qn("w:val"), str(num_id))
    num_pr.append(num_id_element)

    # Add to paragraph properties
    p_props.append(num_pr)
