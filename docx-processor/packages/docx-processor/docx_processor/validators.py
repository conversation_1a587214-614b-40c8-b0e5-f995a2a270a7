"""Validation schemas and functions for configuration files."""

from typing import Dict, Any
from jsonschema import validate, ValidationError

SETTINGS_SCHEMA = {
    "type": "object",
    "properties": {
        "locale": {"type": "string", "description": "Locale code (e.g., en_US, fr_FR)"},
        "date_format": {
            "type": "string",
            "description": "Python strftime format for dates",
        },
        "time_format": {
            "type": "string",
            "description": "Python strftime format for times",
        },
        "long_date_format": {
            "type": "string",
            "description": "Python strftime format for long dates",
        },
        "decimal_separator": {
            "type": "string",
            "maxLength": 1,
            "description": "Character used as decimal separator",
        },
        "thousands_separator": {
            "type": "string",
            "maxLength": 1,
            "description": "Character used as thousands separator",
        },
        "currency_symbol": {"type": "string", "description": "Currency symbol"},
        "currency_position": {
            "type": "string",
            "enum": ["before", "after"],
            "description": "Position of currency symbol relative to amount",
        },
    },
    "additionalProperties": False,
}

# Schema for typed values (dates, currency, etc.)
TYPED_VALUE_SCHEMA = {
    "oneOf": [
        {"type": "string"},
        {"type": "number"},
        {"type": "boolean"},
        {
            "type": "object",
            "properties": {
                "type": {"type": "string", "enum": ["date", "currency"]},
                "value": {"type": ["string", "number"]},
                "format": {"type": "string"},
                "amount": {"type": "number"}
            },
            "required": ["type"],
            "allOf": [
                {
                    "if": {"properties": {"type": {"const": "date"}}},
                    "then": {"required": ["value"]}
                },
                {
                    "if": {"properties": {"type": {"const": "currency"}}},
                    "then": {"required": ["amount"]}
                }
            ]
        }
    ]
}

COLLECTIONS_SCHEMA = {
    "type": "array",
    "items": {
        "type": "object",
        "properties": {
            "placeholders": {
                "type": "array",
                "items": {"type": "string"}
            },
            "values": {
                "type": "array",
                "items": {
                    "type": "array",
                    "items": TYPED_VALUE_SCHEMA
                }
            }
        },
        "required": ["placeholders", "values"]
    }
}

CHARTS_SCHEMA = {
    "type": "array",
    "items": {
        "type": "object",
        "properties": {
            "placeholder": {
                "type": "string",
                "description": "The placeholder text to look for in the template"
            },
            "type": {
                "type": "string",
                "enum": ["bar", "column", "line", "pie", "scatter"]
            },
            "title": {"type": "string"},
            "data": {
                "oneOf": [
                    {
                        # Series data (bar, line, column)
                        "type": "object",
                        "properties": {
                            "labels": {
                                "type": "array",
                                "items": {"type": "string"}
                            },
                            "datasets": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "name": {"type": "string"},
                                        "values": {
                                            "type": "array",
                                            "items": {"type": "number"}
                                        },
                                        "color": {"type": "string"}
                                    },
                                    "required": ["name", "values"]
                                }
                            }
                        },
                        "required": ["labels", "datasets"]
                    },
                    {
                        # Single series data (pie)
                        "type": "object",
                        "properties": {
                            "labels": {
                                "type": "array",
                                "items": {"type": "string"}
                            },
                            "values": {
                                "type": "array",
                                "items": {"type": "number"}
                            },
                            "colors": {
                                "type": "array",
                                "items": {"type": "string"}
                            }
                        },
                        "required": ["labels", "values"]
                    }
                ]
            },
            "options": {
                "type": "object",
                "properties": {
                    "width": {"type": "number"},
                    "height": {"type": "number"},
                    "stacked": {"type": "boolean"},
                    "showPercentages": {"type": "boolean"},
                    "axis": {
                        "type": "object",
                        "properties": {
                            "x": {
                                "type": "object",
                                "properties": {
                                    "title": {"type": "string"}
                                }
                            },
                            "y": {
                                "type": "object",
                                "properties": {
                                    "title": {"type": "string"},
                                    "min": {"type": "number"},
                                    "max": {"type": "number"}
                                }
                            }
                        }
                    },
                    "legend": {
                        "type": "object",
                        "properties": {
                            "position": {
                                "type": "string",
                                "enum": ["top", "bottom", "left", "right"]
                            }
                        }
                    }
                }
            }
        },
        "required": ["placeholder", "type", "data"]
    }
}

SECTIONS_SCHEMA = {
    "type": "array",
    "items": {
        "type": "object",
        "properties": {
            "placeholders": {
                "start": {"type": "string"},
                "end": {"type": "string"},
                "state": {
                    "type": "string",
                    "enum": ["visible", "hidden"]
                }
            }
        },
        "required": ["start", "end"]
    }
}

REPLACEMENTS_SCHEMA = {
    "type": "object",
    "properties": {
        "__array_data__": COLLECTIONS_SCHEMA,
        "__charts__": CHARTS_SCHEMA,
        "__sections__": SECTIONS_SCHEMA
    },
    "additionalProperties": {
        "type": "string"
    }
}

def validate_settings(settings_data: Dict[str, Any]) -> list:
    """
    Validate settings configuration.
    Returns list of validation errors, empty list if valid.
    """
    try:
        validate(instance=settings_data, schema=SETTINGS_SCHEMA)
        return []
    except ValidationError as e:
        return [str(e)]

def validate_replacements(replacements_data: Dict[str, Any]) -> list:
    """
    Validate replacements configuration.
    Returns list of validation errors, empty list if valid.
    """
    try:
        validate(instance=replacements_data, schema=REPLACEMENTS_SCHEMA)
        return []
    except ValidationError as e:
        return [str(e)]