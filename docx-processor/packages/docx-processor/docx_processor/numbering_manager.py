"""Numbering manager for proper list handling in Word documents.

This module provides functionality to merge numbering definitions into Word documents
for proper bullet and numbered list handling.
"""

import os
import tempfile
import logging
from zipfile import ZipFile
from docx import Document
from lxml import etree

logger = logging.getLogger(__name__)

def merge_numbering_xml(doc):
    """
    Merge needed bullet and numbering definitions into the document's numbering.xml.
    
    Args:
        doc: The document to process
        
    Returns:
        A new document with proper numbering
    """
    # First save the document to a temporary file
    temp_path = tempfile.NamedTemporaryFile(suffix='.docx', delete=False).name
    doc.save(temp_path)
    
    # Process the docx file
    try:
        # Open the docx as a zip file
        with ZipFile(temp_path, 'r') as docx_zip:
            # Get all files from the docx
            file_list = docx_zip.namelist()
            
            # Check if numbering.xml exists
            numbering_path = 'word/numbering.xml'
            has_numbering = numbering_path in file_list
            
            # Read all files content
            file_content = {name: docx_zip.read(name) for name in file_list}
            
            # Read the numbering content if it exists
            if has_numbering:
                numbering_content = file_content[numbering_path]
                # Parse as bytes to avoid encoding declaration issues
                numbering_root = etree.fromstring(numbering_content)
            else:
                # Create a basic numbering.xml structure without XML declaration
                numbering_root = etree.Element(
                    "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}numbering"
                )
                
        # Define the namespace
        ns = {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}
        
        # Find the highest abstract numbering ID
        abstract_nums = numbering_root.findall('.//w:abstractNum', ns)
        max_abstract_id = 0
        for abstract_num in abstract_nums:
            id_attr = abstract_num.get('{%s}abstractNumId' % ns['w'])
            if id_attr and int(id_attr) > max_abstract_id:
                max_abstract_id = int(id_attr)
        
        # Find the highest numbering ID
        nums = numbering_root.findall('.//w:num', ns)
        max_num_id = 0
        for num in nums:
            id_attr = num.get('{%s}numId' % ns['w'])
            if id_attr and int(id_attr) > max_num_id:
                max_num_id = int(id_attr)
        
        # Create abstract numbering definitions as byte strings
        bullet_abstract_id = max_abstract_id + 1
        bullet_abstract = etree.fromstring(f'''
        <w:abstractNum xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" w:abstractNumId="{bullet_abstract_id}">
            <w:nsid w:val="FFFFFF89" />
            <w:multiLevelType w:val="hybridMultilevel" />
            <w:tmpl w:val="29761A62" />
            <w:lvl w:ilvl="0">
                <w:start w:val="1" />
                <w:numFmt w:val="bullet" />
                <w:pStyle w:val="ListBullet" />
                <w:lvlText w:val="•" />
                <w:lvlJc w:val="left" />
                <w:pPr>
                    <w:tabs>
                        <w:tab w:val="num" w:pos="720" />
                    </w:tabs>
                    <w:ind w:left="720" w:hanging="360" />
                </w:pPr>
                <w:rPr>
                    <w:rFonts w:ascii="Symbol" w:hAnsi="Symbol" w:hint="default" />
                </w:rPr>
            </w:lvl>
        </w:abstractNum>
        '''.encode('utf-8'))
        
        number_abstract_id = max_abstract_id + 2
        number_abstract = etree.fromstring(f'''
        <w:abstractNum xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" w:abstractNumId="{number_abstract_id}">
            <w:nsid w:val="FFFFFF88" />
            <w:multiLevelType w:val="hybridMultilevel" />
            <w:tmpl w:val="D0A62B40" />
            <w:lvl w:ilvl="0">
                <w:start w:val="1" />
                <w:numFmt w:val="decimal" />
                <w:pStyle w:val="ListNumber" />
                <w:lvlText w:val="%1." />
                <w:lvlJc w:val="left" />
                <w:pPr>
                    <w:tabs>
                        <w:tab w:val="num" w:pos="720" />
                    </w:tabs>
                    <w:ind w:left="720" w:hanging="360" />
                </w:pPr>
            </w:lvl>
        </w:abstractNum>
        '''.encode('utf-8'))
        
        # Add abstract numbering definitions to the document
        numbering_root.append(bullet_abstract)
        numbering_root.append(number_abstract)
        
        # Create numbering instances that reference the abstract definitions
        bullet_num_id = max_num_id + 1
        bullet_num = etree.fromstring(f'''
        <w:num xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" w:numId="{bullet_num_id}">
            <w:abstractNumId w:val="{bullet_abstract_id}" />
        </w:num>
        '''.encode('utf-8'))
        
        number_num_id = max_num_id + 2
        number_num = etree.fromstring(f'''
        <w:num xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" w:numId="{number_num_id}">
            <w:abstractNumId w:val="{number_abstract_id}" />
        </w:num>
        '''.encode('utf-8'))
        
        # Add numbering instances to the document
        numbering_root.append(bullet_num)
        numbering_root.append(number_num)
        
        # Create the XML declaration and convert to bytes
        xml_declaration = b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n'
        numbering_xml = etree.tostring(numbering_root, encoding='UTF-8')
        
        # Combine XML declaration with content
        complete_xml = xml_declaration + numbering_xml
        
        # Update the file_content dictionary
        file_content[numbering_path] = complete_xml
        
        # Write the updated content back to a new docx file
        temp_output = tempfile.NamedTemporaryFile(suffix='.docx', delete=False).name
        with ZipFile(temp_output, 'w') as docx_zip:
            for name, content in file_content.items():
                docx_zip.writestr(name, content)
        
        # Load the fixed document
        fixed_doc = Document(temp_output)
        
        # Store the bullet and number IDs for reference when creating lists
        fixed_doc._bullet_num_id = bullet_num_id
        fixed_doc._number_num_id = number_num_id
        
        # Clean up the input temp file
        os.unlink(temp_path)
        
        # Clean up the output temp file (after the document is loaded)
        os.unlink(temp_output)
        
        logger.info("Successfully merged numbering definitions")
        
        return fixed_doc
    
    except Exception as e:
        logger.error(f"Error merging numbering: {str(e)}")
        # Clean up on error
        try:
            os.unlink(temp_path)
        except:
            pass
        # Return the original document if we failed
        return doc

def add_numbering_to_paragraph(paragraph, is_bullet=True, doc=None):
    """
    Add bullet or numbering to paragraph using the document's numbering definitions.
    
    Args:
        paragraph: The paragraph to modify
        is_bullet: Whether this is a bullet (True) or numbered (False) list item
        doc: The document containing the paragraph (optional)
    """
    from docx.oxml.shared import OxmlElement, qn
    
    # Get the correct numId based on bullet or number
    if doc is not None:
        num_id = getattr(doc, '_bullet_num_id', 1) if is_bullet else getattr(doc, '_number_num_id', 2)
    else:
        # Default IDs if doc is not provided
        num_id = 1 if is_bullet else 2
    
    p = paragraph._p
    pPr = p.get_or_add_pPr()
    numPr = OxmlElement('w:numPr')
    
    # Set level to 0 (first level)
    ilvl = OxmlElement('w:ilvl')
    ilvl.set(qn('w:val'), '0')
    numPr.append(ilvl)
    
    # Set numId to the correct value
    numId = OxmlElement('w:numId')
    numId.set(qn('w:val'), str(num_id))
    numPr.append(numId)
    
    pPr.append(numPr)