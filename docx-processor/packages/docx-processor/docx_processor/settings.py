"""Settings management for the document processor."""

import logging
import os
import json
import locale
from typing import Dict, Any, Optional
from .validators import validate_settings, validate_replacements


class Settings:
    """Handle global settings with environment variable override support."""

    DEFAULT_SETTINGS = {
        "locale": "en_US",
        "date_format": "%Y-%m-%d",
        "time_format": "%H:%M:%S",
        "long_date_format": "%B %d, %Y",
        "decimal_separator": ".",
        "thousands_separator": ",",
        "currency_symbol": "$",
        "currency_position": "before",
    }

    def __init__(self, settings_path: Optional[str] = None):
        self.settings = self.DEFAULT_SETTINGS.copy()
        self.logger = logging.getLogger("docx_processor")

        if settings_path:
            self._load_settings(settings_path)

        self._load_env_variables()
        self._apply_settings()

    def _load_settings(self, settings_path: str):
        """Load settings from file with validation."""
        try:
            with open(settings_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            # Import here to avoid circular import
            errors = validate_settings(data)

            if errors:
                self.logger.warning("Settings validation errors:")
                for error in errors:
                    self.logger.warning("  - %s", error)
                self.logger.info("Using default settings")
            else:
                self.settings.update(data)
                self.logger.info("Loaded settings from %s", settings_path)

        except FileNotFoundError:
            self.logger.warning(
                "Settings file %s not found, using defaults", settings_path
            )
        except json.JSONDecodeError as e:
            self.logger.error(
                "Invalid JSON in settings file %s: %s", settings_path, str(e)
            )
            self.logger.info("Using default settings")

    def _load_env_variables(self):
        """Override settings with environment variables."""
        env_prefix = "DOCX_PROCESSOR_"
        for key in self.settings.keys():
            env_key = f"{env_prefix}{key.upper()}"
            if env_key in os.environ:
                self.settings[key] = os.environ[env_key]
                self.logger.debug(
                    "Override setting %s from environment variable %s", key, env_key
                )

    def _apply_settings(self):
        """Apply settings like locale."""
        try:
            locale.setlocale(locale.LC_ALL, f"{self.settings['locale']}.UTF-8")
            self.logger.debug("Set locale to %s", self.settings['locale'])
        except locale.Error:
            self.logger.warning(
                "Failed to set locale %s, using system default", self.settings['locale']
            )

    def get(self, key: str, default: Any = None) -> Any:
        """Get a setting value."""
        return self.settings.get(key, default)


def load_replacements(path: str, verbose: bool = False) -> Dict[str, str]:
    """Load and validate replacements configuration."""
    try:
        with open(path, "r", encoding="utf-8") as f:
            data = json.load(f)

        # Import here to avoid circular import
        errors = validate_replacements(data)

        if errors:
            print("Replacements validation errors:")
            for error in errors:
                print(f"  - {error}")
            raise ValueError("Invalid replacements configuration")

        return data

    except FileNotFoundError as e:
        raise FileNotFoundError(f"Replacements file {path} not found") from e
    except json.JSONDecodeError as e:
        raise ValueError("Invalid JSON in replacements file") from e
