"""HTML handler with proper Word list support.

This module provides functionality to handle HTML content in document placeholders.
Uses the numbering.xml approach for proper list handling.
"""

import re
import logging
import copy
from bs4 import BeautifulSoup
from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH
from .style_manager import ensure_required_styles
from .numbering_fix import ensure_numbering_part, apply_list_formatting

logger = logging.getLogger(__name__)

def contains_html(text):
    """
    Check if a string contains HTML tags.
    
    Args:
        text: String to check
    
    Returns:
        bool: True if the string contains HTML tags, False otherwise
    """
    if not isinstance(text, str):
        return False
        
    # Simple regex to detect HTML tags
    pattern = r'<[a-zA-Z][^>]*>|</[a-zA-Z][^>]*>'
    return bool(re.search(pattern, text))

def prepare_document_for_html(doc):
    """
    Prepare document for HTML content, ensuring required styles exist.
    
    Args:
        doc: The document to process
        
    Returns:
        The processed document
    """
    # Ensure required styles exist
    doc = ensure_required_styles(doc)
    
    # Ensure the document has numbering support
    doc = ensure_numbering_part(doc)
    
    return doc

def import_missing_styles(doc, template_doc=None):
    """
    Import all missing styles from a source document (or a new default document)
    into the document.
    
    Args:
        doc: The document to add styles to
        template_doc: Optional template document to copy styles from
        
    Returns:
        The document with added styles
    """
    # If no template document is provided, create a new one
    if template_doc is None:
        template_doc = Document()

    # Get the existing style IDs
    existing_style_ids = [s.style_id for s in doc.styles]
    
    # Loop through template styles and add any missing ones
    added_styles = []
    
    for style in template_doc.styles:
        if style.style_id not in existing_style_ids:
            try:
                # Deep copy the style element
                new_element = copy.deepcopy(style.element)
                
                # Add it to the document's styles
                doc.styles.element.append(new_element)
                
                added_styles.append(style.style_id)
                logger.debug(f"Added style: {style.style_id}")
            except (KeyError, AttributeError) as e:
                logger.warning(f"Error adding style {style.style_id}: {e}")
    
    if added_styles:
        logger.info(f"Added {len(added_styles)} styles from template")
        
    return doc

class HtmlHandler:
    """
    HTML handler for document placeholders.
    
    This class handles the conversion of HTML content to Word document elements.
    """
    
    def __init__(self, doc):
        """
        Initialize HTML handler.
        
        Args:
            doc: The Word document to modify
        """
        # Ensure document has the required styles
        self.doc = import_missing_styles(doc)
        self.bullet_style = "ListBullet"
        self.number_style = "ListNumber"
        self.logger = logging.getLogger(__name__ + ".HtmlHandler")
        self.list_paragraphs = []  # Track paragraphs that should be lists
        
    def mark_paragraph_as_list(self, paragraph, is_bullet=True):
        """
        Mark a paragraph to be processed as a list item.
        
        Args:
            paragraph: The paragraph to be marked
            is_bullet: Whether it's a bullet (True) or numbered (False) list item
        """
        # Apply list style to the paragraph
        try:
            paragraph.style = self.bullet_style if is_bullet else self.number_style
        except KeyError:
            self.logger.warning(f"List style {'ListBullet' if is_bullet else 'ListNumber'} not found")
        
        # Apply list formatting directly
        apply_list_formatting(paragraph, is_bullet)
        
        # Add paragraph to the list for reference
        self.list_paragraphs.append((paragraph, is_bullet))
        
    def replace_with_html(self, paragraph, html_content):
        """
        Replace a paragraph with HTML content.
        
        Args:
            paragraph: The paragraph to replace
            html_content: The HTML content to convert
            
        Returns:
            List of new paragraphs created
        """
        # Store the position of this paragraph in the document
        parent = paragraph._p.getparent()
        idx = parent.index(paragraph._p)
        
        # Parse HTML content
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Track new paragraphs
        new_paragraphs = []
        
        # Process all top-level elements
        for element in soup.children:
            # Skip text nodes directly at the top level
            if not hasattr(element, 'name') or element.name is None:
                if str(element).strip():  # If non-empty text node
                    p = self.doc.add_paragraph()
                    p.add_run(str(element))
                    parent.insert(idx + len(new_paragraphs), p._element)
                    new_paragraphs.append(p)
                continue
                
            if element.name == 'p':
                # Create a new paragraph at the same position
                p = self.doc.add_paragraph()
                parent.insert(idx + len(new_paragraphs), p._element)
                new_paragraphs.append(p)
                
                for child in element.children:
                    if isinstance(child, str):
                        p.add_run(child)
                    else:
                        self._process_inline_element(child, p)
                    
            elif element.name == 'h1':
                # Ensure style exists
                try:
                    h = self.doc.add_paragraph(style='Heading1')
                    parent.insert(idx + len(new_paragraphs), h._element)
                    new_paragraphs.append(h)
                    h.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    h.add_run(element.text)
                except KeyError:
                    self.logger.warning("Style 'Heading1' not found, using Normal")
                    h = self.doc.add_paragraph(element.text)
                    parent.insert(idx + len(new_paragraphs), h._element)
                    new_paragraphs.append(h)
                    h.alignment = WD_ALIGN_PARAGRAPH.CENTER
                
            elif element.name == 'h2':
                # Ensure style exists
                try:
                    h = self.doc.add_paragraph(style='Heading2')
                    parent.insert(idx + len(new_paragraphs), h._element)
                    new_paragraphs.append(h)
                    h.add_run(element.text)
                except KeyError:
                    self.logger.warning("Style 'Heading2' not found, using Normal")
                    h = self.doc.add_paragraph(element.text)
                    h.runs[0].bold = True
                    parent.insert(idx + len(new_paragraphs), h._element)
                    new_paragraphs.append(h)
                
            elif element.name == 'h3':
                # Ensure style exists
                try:
                    h = self.doc.add_paragraph(style='Heading3')
                    parent.insert(idx + len(new_paragraphs), h._element)
                    new_paragraphs.append(h)
                    h.add_run(element.text)
                except KeyError:
                    self.logger.warning("Style 'Heading3' not found, using Normal")
                    h = self.doc.add_paragraph(element.text)
                    h.runs[0].bold = True
                    h.runs[0].italic = True
                    parent.insert(idx + len(new_paragraphs), h._element)
                    new_paragraphs.append(h)
                
            elif element.name == 'ul':
                # Process unordered list (bullet points)
                for li in element.find_all('li', recursive=False):
                    try:
                        # Try to use the ListBullet style
                        list_para = self.doc.add_paragraph(style=self.bullet_style)
                    except KeyError:
                        # Fall back to normal paragraph
                        list_para = self.doc.add_paragraph()
                    
                    # Process all content in the list item
                    for content in li.contents:
                        if isinstance(content, str):
                            list_para.add_run(content)
                        elif hasattr(content, 'name'):
                            self._process_inline_element(content, list_para)
                    
                    # Mark as a list item
                    self.mark_paragraph_as_list(list_para, is_bullet=True)
                    
                    # Add to document
                    parent.insert(idx + len(new_paragraphs), list_para._element)
                    new_paragraphs.append(list_para)
                    
            elif element.name == 'ol':
                # Process ordered list (numbers)
                for i, li in enumerate(element.find_all('li', recursive=False)):
                    try:
                        # Try to use the ListNumber style
                        list_para = self.doc.add_paragraph(style=self.number_style)
                    except KeyError:
                        # Fall back to normal paragraph
                        list_para = self.doc.add_paragraph()
                    
                    # Process all content in the list item
                    for content in li.contents:
                        if isinstance(content, str):
                            list_para.add_run(content)
                        elif hasattr(content, 'name'):
                            self._process_inline_element(content, list_para)
                    
                    # Mark as a list item
                    self.mark_paragraph_as_list(list_para, is_bullet=False)
                    
                    # Add to document
                    parent.insert(idx + len(new_paragraphs), list_para._element)
                    new_paragraphs.append(list_para)
        
        # If no elements were processed, create at least one paragraph with text content
        if not new_paragraphs:
            plain_text = soup.get_text()
            if plain_text.strip():
                p = self.doc.add_paragraph(plain_text)
                parent.insert(idx, p._element)
                new_paragraphs.append(p)
        
        # Remove the original paragraph
        parent.remove(paragraph._p)
        
        return new_paragraphs
    
    def _process_inline_element(self, element, paragraph):
        """
        Process an inline HTML element and add it to a paragraph.
        
        Args:
            element: The HTML element to process
            paragraph: The paragraph to add the element to
        """
        if element.name == 'strong' or element.name == 'b':
            run = paragraph.add_run(element.text)
            run.bold = True
            
        elif element.name == 'em' or element.name == 'i':
            run = paragraph.add_run(element.text)
            run.italic = True
            
        elif element.name == 'u':
            run = paragraph.add_run(element.text)
            run.underline = True
            
        elif element.name == 'span':
            run = paragraph.add_run(element.text)
            # TODO: Add style handling for spans if needed
            
        elif element.name == 'a':
            # Try to create a hyperlink
            text = element.text
            href = element.get('href', '')
            if href:
                # Add as hyperlink
                from docx.oxml.shared import qn, OxmlElement
                
                # This is a simplification - proper hyperlinks require more complex XML manipulation
                run = paragraph.add_run(text)
                r = run._r
                rPr = r.get_or_add_rPr()
                
                # Add hyperlink style - usually blue and underlined
                color = OxmlElement('w:color')
                color.set(qn('w:val'), '0000FF')
                rPr.append(color)
                
                u = OxmlElement('w:u')
                u.set(qn('w:val'), 'single')
                rPr.append(u)
            else:
                # Just add as text if no href
                run = paragraph.add_run(text)
                
        elif element.name == 'br':
            # Add line break in the paragraph
            paragraph.add_run("\n")
            
        elif element.name == 'ul' or element.name == 'ol':
            # Handle nested lists - this is a basic implementation
            # For more complex scenarios, you might need deeper recursion
            is_bullet = element.name == 'ul'
            for li in element.find_all('li', recursive=False):
                paragraph.add_run(" • " if is_bullet else " 1. ")
                paragraph.add_run(li.get_text())
                paragraph.add_run("\n")
            
        else:
            # Default case - just add the text
            if element.text:
                paragraph.add_run(element.text)