"""Command line interface for the document processor."""

import argparse
import sys
from typing import List

from docx_processor.table_processor import TableAwareDocxTemplateProcessor
from docx_processor.placeholder_mapping import PlaceholderMapper
from .settings import Settings, load_replacements
from .logger import setup_logger, LogLevel


def parse_args(args: List[str] = None) -> argparse.Namespace: # type: ignore
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Process a Word document template and replace placeholders."
    )

    parser.add_argument("template", help="Input template document path")

    parser.add_argument("output", help="Output document path")

    parser.add_argument(
        "-s", "--settings", help="Settings configuration file path (JSON)"
    )

    parser.add_argument(
        "-r",
        "--replacements",
        required=True,
        help="Replacements configuration file path (JSON)",
    )

    # Enhanced logging options
    log_group = parser.add_argument_group("Logging Options")
    verbosity = log_group.add_mutually_exclusive_group()

    verbosity.add_argument(
        "-v",
        "--verbose",
        action="store_true",
        help="Set log level to INFO (default is WARNING)",
    )

    verbosity.add_argument(
        "--log-level",
        type=str,
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help="Set specific logging level",
    )

    log_group.add_argument("--log-file", help="Write logs to specified file")

    log_group.add_argument(
        "--max-log-size",
        type=int,
        default=5,
        help="Maximum size of log file in MB before rotation (default: 5)",
    )

    log_group.add_argument(
        "--max-log-files",
        type=int,
        default=5,
        help="Number of rotated log files to keep (default: 5)",
    )
    
    parser.add_argument(
        "-m",
        "--mapping",
        help="Placeholder mapping configuration file path (JSON)",
    )

    return parser.parse_args(args)


def get_log_level(args: argparse.Namespace) -> LogLevel:
    """Determine the log level based on command line arguments."""
    if args.log_level:
        return args.log_level
    elif args.verbose:
        return "INFO"
    return "WARNING"

def main(args: List[str] = None): # type: ignore
    """Main entry point for the command line interface."""
    try:
        parsed_args = parse_args(args)

        # Setup logging first
        log_level = get_log_level(parsed_args)
        logger = setup_logger(
            level=log_level,
            log_file=parsed_args.log_file,
            max_bytes=parsed_args.max_log_size * 1024 * 1024,
            backup_count=parsed_args.max_log_files,
        )

        logger.debug("Starting document processing")
        logger.debug("Log level set to: %s", log_level)

        # Initialize settings
        settings = Settings(parsed_args.settings)

        # Initialize placeholder mapper if mapping file provided
        mapper = None
        if parsed_args.mapping:
            mapper = PlaceholderMapper(parsed_args.mapping)

        # Load replacements
        replacements = load_replacements(parsed_args.replacements)
        logger.debug("Loaded %d replacements", len(replacements))

        # Translate replacements if mapper is available
        if mapper:
            replacements = mapper.translate_replacements(replacements)
            logger.debug("Translated %d replacements", len(replacements))

        # Process the template
        processor = TableAwareDocxTemplateProcessor(parsed_args.template, settings)

        # Check for unmapped placeholders
        if mapper:
            placeholders = processor.find_all_placeholders()
            all_placeholders = set(sum(placeholders.values(), []))
            unmapped = mapper.get_unmapped_placeholders(list(all_placeholders))
            if unmapped:
                logger.warning("Found unmapped placeholders: %s", unmapped)

        processor.process_document(replacements)
        processor.save(parsed_args.output)

        logger.info("Document processing completed successfully")

    except FileNotFoundError as e:
        logger.error(f"File not found: {e.filename}")
        sys.exit(1)
    except ValueError as e:
        logger.error(str(e))
        sys.exit(1)
    # pylint: disable=W0718
    except Exception as e:
        logger.error("An unexpected error occurred: %s", str(e))
        sys.exit(1)


if __name__ == "__main__":
    main()
