"""Support for processing charts."""

import io
import logging
from pathlib import Path
from typing import Optional
import matplotlib.pyplot as plt
import matplotlib as mpl
import numpy as np
from docx.shared import Inches

class ChartProcessor:
    """Creates and inserts charts as images using matplotlib."""

    def __init__(self, document, output_dir: Optional[str] = None):
        """Initialize the chart processor."""
        self.document = document
        self.charts = {}
        self.logger = logging.getLogger("docx_processor.charts")
        # Directory to save chart images if needed
        self.output_dir = Path(output_dir) if output_dir else None
        
        # Set up consistent style settings directly
        plt.style.use('default')
        # Apply custom styling for better visuals
        mpl.rcParams.update({
            'figure.facecolor': 'white',
            'axes.facecolor': 'white',
            'axes.grid': True,
            'grid.alpha': 0.3,
            'axes.labelsize': 10,
            'axes.titlesize': 12,
            'lines.linewidth': 2,
            'lines.markersize': 6,
            'xtick.labelsize': 9,
            'ytick.labelsize': 9,
            'legend.fontsize': 9,
            'legend.frameon': True,
            'legend.framealpha': 0.8,
            'legend.edgecolor': '0.8',
            'font.family': ['sans-serif'],
            'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans', 'sans-serif']
        })

    def create_pie_chart(self, chart_def: dict) -> io.BytesIO:
        """Create a pie chart."""
        # Get figure size in inches
        width = chart_def['options'].get('width', 6)
        height = chart_def['options'].get('height', 4)
        
        # Create figure with specified size
        fig = plt.figure(figsize=(width, height))
        ax = fig.add_subplot(111)

        data = chart_def['data']
        colors = data.get('colors', plt.cm.Set3.colors)
        
        # Create pie chart
        wedges, texts, autotexts = ax.pie(
            data['values'],
            labels=data['labels'],
            colors=colors,
            autopct='%1.1f%%' if chart_def['options'].get('showPercentages') else None,
            labeldistance=1.1,
            pctdistance=0.6
        )

        # Style percentage labels
        plt.setp(autotexts, weight='bold')

        # Map our position values to matplotlib positions
        position_mapping = {
            'right': 'center right',
            'left': 'center left',
            'top': 'upper center',
            'bottom': 'lower center'
        }

        # Get legend position with fallback
        legend_pos = chart_def['options'].get('legend', {}).get('position', 'right')
        mpl_position = position_mapping.get(legend_pos, 'center right')

        # Add legend with appropriate positioning
        if mpl_position in ['center right', 'center left']:
            # For right/left placement
            bbox_x = 1.3 if mpl_position == 'center right' else -0.3
            ax.legend(
                wedges,
                data['labels'],
                title="Categories",
                loc=mpl_position,
                bbox_to_anchor=(bbox_x, 0.5),
                frameon=False
            )
        else:
            # For top/bottom placement
            bbox_y = 1.2 if mpl_position == 'upper center' else -0.2
            ax.legend(
                wedges,
                data['labels'],
                title="Categories",
                loc=mpl_position,
                bbox_to_anchor=(0.5, bbox_y),
                frameon=False,
                ncol=2
            )

        ax.set_title(chart_def['title'])

        # Save to bytes buffer with tight layout and specified figure size
        buf = io.BytesIO()
        plt.savefig(buf, format='png', bbox_inches='tight', dpi=300)
        plt.close(fig)  # Explicitly close the figure

        return buf

    def create_bar_chart(self, chart_def: dict) -> io.BytesIO:
        """Create a bar chart."""
        _, ax = plt.subplots(figsize=(
            chart_def['options'].get('width', 6),
            chart_def['options'].get('height', 4)
        ))

        data = chart_def['data']
        x = np.arange(len(data['labels']))
        width = 0.35
        
        if chart_def['options'].get('stacked', False):
            bottom = np.zeros(len(data['labels']))
            for dataset in data['datasets']:
                ax.bar(
                    x,
                    dataset['values'],
                    width,
                    bottom=bottom,
                    label=dataset['name'],
                    color=dataset.get('color')
                )
                bottom += np.array(dataset['values'])
        else:
            for i, dataset in enumerate(data['datasets']):
                ax.bar(
                    x + i * width,
                    dataset['values'],
                    width,
                    label=dataset['name'],
                    color=dataset.get('color')
                )

        ax.set_title(chart_def['title'])
        ax.set_xticks(x)
        ax.set_xticklabels(data['labels'])

        # Set axis labels
        if 'axis' in chart_def['options']:
            if 'x' in chart_def['options']['axis']:
                ax.set_xlabel(chart_def['options']['axis']['x'].get('title', ''))
            if 'y' in chart_def['options']['axis']:
                ax.set_ylabel(chart_def['options']['axis']['y'].get('title', ''))
                if 'min' in chart_def['options']['axis']['y']:
                    ax.set_ylim(bottom=chart_def['options']['axis']['y']['min'])

        ax.legend()

        # Rotate x labels if they're long
        plt.xticks(rotation=45, ha='right')

        # Save to bytes buffer
        buf = io.BytesIO()
        plt.savefig(buf, format='png', bbox_inches='tight', dpi=300)
        plt.close()

        return buf

    def create_line_chart(self, chart_def: dict) -> io.BytesIO:
        """Create a line chart."""
        _, ax = plt.subplots(figsize=(
            chart_def['options'].get('width', 6),
            chart_def['options'].get('height', 4)
        ))

        data = chart_def['data']
        
        for dataset in data['datasets']:
            ax.plot(
                data['labels'],
                dataset['values'],
                label=dataset['name'],
                marker='o',
                color=dataset.get('color')
            )

        ax.set_title(chart_def['title'])

        # Set axis labels
        if 'axis' in chart_def['options']:
            if 'x' in chart_def['options']['axis']:
                ax.set_xlabel(chart_def['options']['axis']['x'].get('title', ''))
            if 'y' in chart_def['options']['axis']:
                ax.set_ylabel(chart_def['options']['axis']['y'].get('title', ''))
                if 'min' in chart_def['options']['axis']['y']:
                    ax.set_ylim(bottom=chart_def['options']['axis']['y']['min'])

        ax.legend()
        ax.grid(True)

        # Save to bytes buffer
        buf = io.BytesIO()
        plt.savefig(buf, format='png', bbox_inches='tight', dpi=300)
        plt.close()

        return buf

    def process_charts(self, charts_data: list):
        """Process all chart definitions."""
        for chart_def in charts_data:
            chart_type = chart_def['type']
            if chart_type == 'pie':
                buf = self.create_pie_chart(chart_def)
            elif chart_type == 'bar':
                buf = self.create_bar_chart(chart_def)
            elif chart_type == 'line':
                buf = self.create_line_chart(chart_def)
            else:
                self.logger.warning("Unsupported chart type: %s", chart_type)
                continue

            # Store the buffer for this placeholder
            self.charts[chart_def['placeholder']] = buf

    def find_placeholder(self, paragraph_text: str) -> Optional[str]:
        """Find if any of our chart placeholders exist in this text."""
        for placeholder in self.charts.keys():
            if placeholder in paragraph_text:
                return placeholder
        return None

    def process_paragraph(self, paragraph):
        """Process a paragraph for chart placeholders."""
        placeholder = self.find_placeholder(paragraph.text)
        if placeholder:
            self.insert_chart(paragraph, placeholder)

    def insert_chart(self, paragraph, placeholder: str):
        """Insert a chart at the given paragraph."""
        if placeholder not in self.charts:
            self.logger.warning("Chart not found for placeholder: %s", placeholder)
            return

        # Get the image buffer
        buf = self.charts[placeholder]
        buf.seek(0)

        # Clear the placeholder text
        paragraph.text = paragraph.text.replace(placeholder, '')

        # Insert the image
        run = paragraph.add_run()
        run.add_picture(buf, width=Inches(6))  # Default width, can be customized