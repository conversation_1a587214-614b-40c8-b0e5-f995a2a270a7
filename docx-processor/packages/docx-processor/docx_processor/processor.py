"""Main document processor implementation."""

import logging
import re
from datetime import datetime
from typing import Dict, List, Tuple, Any
from docx import Document
from .utils import process_replacements
from .html_handler import contains_html, HtmlHandler, prepare_document_for_html
from .numbering_fix import ensure_numbering_part


class DocxTemplateProcessor:
    """Document template processor."""

    def __init__(self, template_path: str, settings):
        """Initialize the processor with a template document and settings."""
        # Configure root logger
        logging.basicConfig(
            level=logging.WARNING,
            format='%(levelname)s - %(message)s',
            force=True
        )
        self.doc = Document(template_path)
        self.settings = settings
        self.placeholder_pattern = r"\[(.*?)\]"
        self.logger = logging.getLogger(__name__)
        self.contains_html_content = False
        self.html_handler = None
        self.list_paragraphs = []  # Track paragraphs that should be lists

    def _get_placeholder_text(self, paragraph) -> List[Tuple[str, str]]:
        """
        Extract placeholders from a paragraph.
        Returns list of tuples (full_placeholder, inner_text)
        """
        text = paragraph.text
        matches = re.finditer(self.placeholder_pattern, text)
        return [(m.group(0), m.group(1)) for m in matches]

    def find_all_placeholders(self) -> Dict[str, List[str]]:
        """Scan the document and return all found placeholders."""
        placeholders = {}

        self.logger.info("Scanning document for placeholders...")

        # Search in headers
        for section in self.doc.sections:
            header = section.header
            if header:
                for paragraph in header.paragraphs:
                    found = self._get_placeholder_text(paragraph)
                    if found:
                        placeholders[paragraph.text] = [f[0] for f in found]
                        self.logger.debug("Found in header: %s", [f[0] for f in found])

                # Search in header tables
                for table in header.tables:
                    for row in table.rows:
                        for cell in row.cells:
                            for paragraph in cell.paragraphs:
                                found = self._get_placeholder_text(paragraph)
                                if found:
                                    placeholders[paragraph.text] = [f[0] for f in found]
                                    self.logger.debug(
                                        "Found in header table: %s", [f[0] for f in found]
                                    )

        # Search in main document paragraphs
        for paragraph in self.doc.paragraphs:
            found = self._get_placeholder_text(paragraph)
            if found:
                placeholders[paragraph.text] = [f[0] for f in found]
                self.logger.debug("Found in main document: %s", [f[0] for f in found])

        # Search in tables
        for table in self.doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        found = self._get_placeholder_text(paragraph)
                        if found:
                            placeholders[paragraph.text] = [f[0] for f in found]
                            self.logger.debug(
                                "Found in table: %s", [f[0] for f in found]
                            )

        self.logger.debug(
            "All unique placeholders found: %s",
            sorted(set(sum(placeholders.values(), []))),
        )

        return placeholders

    def _find_section_boundaries(
            self, container, start_of_section: str, end_of_section: str
        ) -> List[Tuple[int, int, List[str]]]:
        """Find section boundaries and their placeholders in a container."""
        self.logger.debug("Searching for section boundaries...")
        sections = []
        start_idx = None
        current_placeholders = []

        for idx, paragraph in enumerate(container.paragraphs):
            self.logger.debug(
                "Examining paragraph %d: '%s'", idx, paragraph.text.strip()
            )

            # Check for section start and end in the same paragraph
            if start_of_section in paragraph.text and end_of_section in paragraph.text:
                self.logger.debug(
                    "Found section start and end markers in the same paragraph '%s' at index %d",
                    paragraph.text.strip(),
                    idx,
                )
                start_idx = idx
                current_placeholders = []

                self._append_placeholder_in_paragraph(current_placeholders, paragraph)

                self.logger.debug(
                    "Found section end marker '%s' at index %d with placeholders: %s",
                    paragraph.text.strip(),
                    idx,
                    current_placeholders,
                )
                sections.append((start_idx, idx, current_placeholders))
                start_idx = None

            # Check for section start
            elif start_of_section in paragraph.text:
                self.logger.debug(
                    "Found section start marker '%s' at index %d",
                    paragraph.text.strip(),
                    idx,
                )
                start_idx = idx
                current_placeholders = []

                self._append_placeholder_in_paragraph(current_placeholders, paragraph)

            # Check for section end
            elif end_of_section in paragraph.text and start_idx is not None:
                # Also collect placeholders from the end marker paragraph
                self._append_placeholder_in_paragraph(current_placeholders, paragraph)

                self.logger.debug(
                    "Found section end marker '%s' at index %d with placeholders: %s",
                    paragraph.text.strip(),
                    idx,
                    current_placeholders,
                )
                sections.append((start_idx, idx, current_placeholders))
                start_idx = None
            elif start_idx is not None:
                # Collect placeholders within section
                self._append_placeholder_in_paragraph(current_placeholders, paragraph)
                # Also check the start marker paragraph if we haven't yet
                if idx == start_idx:
                    matches = re.finditer(self.placeholder_pattern, paragraph.text)
                    found = [m.group(0) for m in matches]
                    if found:
                        self.logger.debug("Found placeholders in start marker: %s", found)
                        for ph in found:
                            if ph not in current_placeholders:
                                current_placeholders.append(ph)

        return sections

    def _append_placeholder_in_paragraph(self, current_placeholders, paragraph):
        """Append placeholders found in a paragraph to the current list."""
        matches = re.finditer(self.placeholder_pattern, paragraph.text)
        found = [m.group(0) for m in matches]
        if found:
            for ph in found:
                if ph not in current_placeholders:
                    current_placeholders.append(ph)

    def _process_sections(self, container, replacements: Dict[str, Any]):
        """Process sections that need to be repeated."""
        self.logger.debug("Processing sections with replacements")
        self.logger.debug("Container type: %s", type(container).__name__)
        self.logger.debug("Available placeholders: %s", list(replacements.keys()))

        sections_config = replacements.get("__sections__", [])
        for section_config in sections_config:
            start_of_section = section_config.get("start", "[start-of-section]")
            end_of_section = section_config.get("end", "[end-of-section]")
            state = section_config.get("state", "visible")

            sections = self._find_section_boundaries(container, start_of_section, end_of_section)
            if not sections:
                continue

            for start_idx, end_idx, section_placeholders in reversed(sections):
                if state == "hidden":
                    self._remove_section(container, start_idx, end_idx)
                else:
                    self._process_section(container, start_idx, end_idx, section_placeholders, replacements, start_of_section, end_of_section)

    def _remove_section(self, container, start_idx, end_idx):
        """Remove a section from the document."""
        section_paragraphs = container.paragraphs[start_idx:end_idx + 1]
        for paragraph in section_paragraphs:
            if paragraph._element is not None and paragraph._element.getparent() is not None:
                paragraph._element.getparent().remove(paragraph._element)

    def _process_section(self, container, start_idx, end_idx, section_placeholders, replacements, start_of_section, end_of_section):
        """Process a single section."""
        section_processed = False
        for collection in replacements.get("__array_data__", []):
            collection_placeholders = set(collection.get("placeholders", []))
            matching_placeholders = [p for p in section_placeholders if p in collection_placeholders]
            if matching_placeholders:
                values = collection.get("values", [])
                if not values:
                    continue

                section_paragraphs = container.paragraphs[start_idx:end_idx + 1]
                template_paragraphs, first_non_marker = self._get_template_paragraphs(section_paragraphs, start_of_section, end_of_section)
                if not first_non_marker or not template_paragraphs:
                    continue

                parent = first_non_marker._element.getparent()
                insert_idx = parent.index(first_non_marker._element)

                for value_set in values:
                    value_mapping = {ph: val for ph, val in zip(collection["placeholders"], value_set)}
                    self._insert_value_set(container, template_paragraphs, insert_idx, section_placeholders, value_mapping, start_of_section, end_of_section)

                self._remove_original_section(section_paragraphs)
                section_processed = True

        if not section_processed:
            # Remove start and end tags if section is not processed
            section_paragraphs = container.paragraphs[start_idx:end_idx + 1]
            for paragraph in section_paragraphs:
                paragraph.text = paragraph.text.replace(start_of_section, "").replace(end_of_section, "")

    def _get_template_paragraphs(self, section_paragraphs, start_of_section, end_of_section):
        """Get template paragraphs and the first non-marker paragraph."""
        template_paragraphs = []
        first_non_marker = None
        for p in section_paragraphs:
            text = p.text.strip()
            template_paragraphs.append(p)
            if text not in [start_of_section, end_of_section]:
                if first_non_marker is None:
                    first_non_marker = p
        return template_paragraphs, first_non_marker
            
    def _insert_value_set(self, container, template_paragraphs, insert_idx, section_placeholders, value_mapping, start_of_section, end_of_section):
        """Insert a set of values into the template paragraphs."""
        parent = template_paragraphs[0]._element.getparent()
        for template_p in template_paragraphs:
            new_p = container.add_paragraph()
            parent.insert(insert_idx, new_p._element)
            insert_idx += 1

            self._copy_paragraph_style(template_p, new_p)
            self._copy_paragraph_runs(template_p, new_p, start_of_section, end_of_section)
            self._replace_placeholders_in_paragraph(new_p, section_placeholders, value_mapping)

    def _copy_paragraph_style(self, template_p, new_p):
        """Copy the style from the template paragraph to the new paragraph."""
        if template_p.style:
            new_p.style = template_p.style
        new_p.paragraph_format.alignment = template_p.paragraph_format.alignment

    def _copy_paragraph_runs(self, template_p, new_p, start_of_section, end_of_section):
        """Copy the runs from the template paragraph to the new paragraph."""
        for run in template_p.runs:
            # Replace section markers with empty string
            run_text = run.text.replace(start_of_section, "").replace(end_of_section, "")
            new_run = new_p.add_run(run_text)
            if hasattr(run, 'bold'):
                new_run.bold = run.bold
            if hasattr(run, 'italic'):
                new_run.italic = run.italic
            if hasattr(run, 'underline'):
                new_run.underline = run.underline
            if hasattr(run, 'font'):
                if hasattr(run.font, 'name'):
                    new_run.font.name = run.font.name
                if hasattr(run.font, 'size'):
                    new_run.font.size = run.font.size
                if hasattr(run.font, 'color'):
                    new_run.font.color.rgb = run.font.color.rgb

    def _replace_placeholders_in_paragraph(self, paragraph, section_placeholders, value_mapping):
        """Replace placeholders in a paragraph with values from the value mapping."""
        text = paragraph.text
        for ph in section_placeholders:
            if ph in text and ph in value_mapping:
                val = value_mapping[ph]
                
                # Check if value contains HTML
                if isinstance(val, str) and contains_html(val) and self.html_handler:
                    # If paragraph only contains this placeholder, replace with HTML
                    if text.strip() == ph:
                        self.html_handler.replace_with_html(paragraph, val)
                    else:
                        # Just replace the placeholder text with plain text
                        from bs4 import BeautifulSoup
                        soup = BeautifulSoup(val, 'html.parser')
                        plain_text = soup.get_text()
                        self._replace_in_paragraph(paragraph, ph, plain_text)
                elif isinstance(val, dict) and val.get('type') == 'date':
                    date_format = val.get('format', '%Y-%m-%d')
                    date_str = datetime.strptime(val['value'], '%Y-%m-%d').strftime(date_format)
                    self._replace_in_paragraph(paragraph, ph, date_str)
                else:
                    self._replace_in_paragraph(paragraph, ph, str(val))

    def _remove_original_section(self, section_paragraphs):
        """Remove the original section content."""
        try:
            for p in section_paragraphs:
                if p._element is not None and p._element.getparent() is not None:
                    p._element.getparent().remove(p._element)
        except Exception as e:
            self.logger.error("Error removing section content: %s", str(e))

    def process_document(self, replacements: Dict[str, Any]):
        """Process the document with given replacements."""
        self.logger.debug("Raw replacements: %s", replacements)

        # Process special values in replacements
        processed_replacements = process_replacements(replacements, self.settings)
        self.logger.debug("Processed replacements: %s", processed_replacements)
        
        # Check if any replacement contains HTML
        for value in processed_replacements.values():
            if isinstance(value, str) and contains_html(value):
                self.contains_html_content = True
                self.logger.info("HTML content detected in replacements")
                # Prepare document for HTML content by initializing HTML handler
                self.doc = prepare_document_for_html(self.doc)
                self.html_handler = HtmlHandler(self.doc)
                break
        
        # Process sections first
        self._process_sections(self.doc, replacements)

        # Process headers and footers
        for section in self.doc.sections:
            if section.header:
                self._process_sections(section.header, replacements)
            if section.footer:
                self._process_sections(section.footer, replacements)

        # Perform regular replacements
        for placeholder, new_text in processed_replacements.items():
            if isinstance(placeholder, str):
                self.logger.debug("Replacing '%s' with '%s'", placeholder, new_text)
                if isinstance(new_text, str) and contains_html(new_text) and self.contains_html_content:
                    self.replace_html_placeholder(placeholder, new_text)
                else:
                    self.replace_placeholder(placeholder, new_text)
        
        # If HTML handler was used, add its list paragraphs to ours
        if self.html_handler:
            self.list_paragraphs.extend(self.html_handler.list_paragraphs)
                    
    def replace_html_placeholder(self, placeholder: str, html_content: str):
        """Replace a placeholder with HTML content throughout the document."""
        # Track paragraphs that contain the placeholder
        paragraphs_to_process = []
        
        # Check headers and footers
        for section in self.doc.sections:
            if section.header:
                for paragraph in section.header.paragraphs:
                    if placeholder in paragraph.text:
                        paragraphs_to_process.append(paragraph)
                
                # Check header tables
                for table in section.header.tables:
                    for row in table.rows:
                        for cell in row.cells:
                            for paragraph in cell.paragraphs:
                                if placeholder in paragraph.text:
                                    paragraphs_to_process.append(paragraph)
            
            if section.footer:
                for paragraph in section.footer.paragraphs:
                    if placeholder in paragraph.text:
                        paragraphs_to_process.append(paragraph)
                
                # Check footer tables
                for table in section.footer.tables:
                    for row in table.rows:
                        for cell in row.cells:
                            for paragraph in cell.paragraphs:
                                if placeholder in paragraph.text:
                                    paragraphs_to_process.append(paragraph)
        
        # Check main document paragraphs
        for paragraph in self.doc.paragraphs:
            if placeholder in paragraph.text:
                paragraphs_to_process.append(paragraph)
        
        # Check tables
        for table in self.doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        if placeholder in paragraph.text:
                            paragraphs_to_process.append(paragraph)
        
        # Process each paragraph - special handling for HTML content
        for paragraph in paragraphs_to_process:
            # Only replace the HTML content if this paragraph contains ONLY the placeholder
            # Otherwise, we'll do a regular text replacement
            if paragraph.text.strip() == placeholder:
                # Replace entire paragraph with HTML content
                self.html_handler.replace_with_html(paragraph, html_content)
            else:
                # Just replace the placeholder text with plain text content
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(html_content, 'html.parser')
                plain_text = soup.get_text()
                self._replace_in_paragraph(paragraph, placeholder, plain_text)

    def replace_placeholder(self, placeholder: str, new_text: str):
        """Replace a placeholder with new text throughout the document."""
        # Handle headers
        for section in self.doc.sections:
            if section.header:
                self._process_container(section.header, placeholder, new_text)
            if section.footer:
                self._process_container(section.footer, placeholder, new_text)

        # Handle main document
        self._process_container(self.doc, placeholder, new_text)

    def _process_container(self, container, placeholder: str, new_text: str):
        """Process all paragraphs and tables in a container."""
        self.logger.debug("Processing container of type: %s", type(container).__name__)
        
        # If container is already a paragraph, process it directly
        if type(container).__name__ == 'Paragraph':
            try:
                # Get the text before attempting replacement
                paragraph_text = container.text
                if placeholder in paragraph_text:
                    self._replace_in_paragraph(container, placeholder, new_text)
                return
            except Exception as e:
                self.logger.error("Error processing paragraph: %s", str(e))
                return
            
        # For Document/Section containers that have paragraphs collection
        try:
            for paragraph in container.paragraphs:
                if placeholder in paragraph.text:
                    self._replace_in_paragraph(paragraph, placeholder, new_text)
        except Exception as e:
            self.logger.error("Error processing paragraphs: %s", str(e))

        # Process tables
        for table in container.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        if placeholder in paragraph.text:
                            self._replace_in_paragraph(paragraph, placeholder, new_text)

    def _find_placeholder_position(self, paragraph, placeholder: str):
        """
        Find the position of a placeholder within paragraph runs.
        Returns tuple of (placeholder_runs, start_pos, end_pos) where:
        - placeholder_runs is list of run indices containing the placeholder
        - start_pos is tuple (run_index, position_in_run) for start of placeholder
        - end_pos is tuple (run_index, position_in_run) for end of placeholder
        """
        placeholder_start = None
        placeholder_end = None
        placeholder_runs = []
        current_text = ""

        # Scan through runs to find the placeholder
        for i, run in enumerate(paragraph.runs):
            current_text += run.text
            if placeholder not in current_text:
                continue

            # Found the complete placeholder
            start_index = current_text.find(placeholder)
            end_index = start_index + len(placeholder)

            # Calculate which runs contain the placeholder
            current_position = 0
            for j, r in enumerate(paragraph.runs):
                run_start = current_position
                run_end = current_position + len(r.text)

                if (
                    run_start <= start_index < run_end
                    or run_start < end_index <= run_end
                    or (start_index <= run_start and run_end <= end_index)
                ):
                    placeholder_runs.append(j)
                    if placeholder_start is None and run_start <= start_index < run_end:
                        placeholder_start = (j, start_index - run_start)
                    if placeholder_end is None and run_start < end_index <= run_end:
                        placeholder_end = (j, end_index - run_start)

                current_position += len(r.text)

            if placeholder_start and placeholder_end:
                    break

        if not (placeholder_start and placeholder_end):
            self.logger.error("Failed to find placeholder positions for: %s", placeholder)

        return placeholder_runs, placeholder_start, placeholder_end

    def _replace_in_single_run(self, run, start_pos: int, end_pos: int, new_text: str):
        """Replace text in a single run while preserving surrounding text."""
        run.text = run.text[:start_pos] + new_text + run.text[end_pos:]

    def _replace_across_runs(
        self,
        paragraph,
        runs,
        start_run_index: int,
        start_pos: int,
        end_run_index: int,
        end_pos: int,
        new_text: str,
    ):
        """Replace text that spans multiple runs while preserving formatting."""
        # Keep first run's original formatting intact
        first_run = runs[start_run_index]
        first_run.text = first_run.text[:start_pos] + new_text

        # Preserve original styles for remaining runs
        for i in range(start_run_index + 1, end_run_index + 1):
            run = runs[i]
            # Keep the run but clear its text
            if i == end_run_index:
                run.text = run.text[end_pos:]
            else:
                run.text = ""

            # Keep original style
            if hasattr(run, '_element'):
                new_run = paragraph.add_run()
                new_run.bold = run.bold
                new_run.italic = run.italic
                new_run.underline = run.underline
                new_run.font.name = run.font.name
                new_run.font.size = run.font.size
                new_run.font.color.rgb = run.font.color.rgb
                run.text = ""

    def _replace_in_paragraph(self, paragraph, placeholder: str, new_text: str):
        """Replace placeholder in a paragraph while preserving formatting and surrounding text."""
        # Find the placeholder position
        _, start_pos, end_pos = self._find_placeholder_position(
            paragraph, placeholder
        )

        if not start_pos or not end_pos:
            return

        # Handle the replacement
        start_run_index, start_offset = start_pos
        end_run_index, end_offset = end_pos
        runs = paragraph.runs

        if start_run_index == end_run_index:
            # Placeholder is contained within a single run
            self._replace_in_single_run(
                runs[start_run_index], start_offset, end_offset, new_text
            )
        else:
            self._replace_across_runs(
                paragraph, runs, start_run_index, start_offset, end_run_index, end_offset, new_text
            )

    def save(self, output_path: str):
        """Save the processed document."""
        self.logger.info("Saving document to: %s", output_path)
        
        # Ensure the numbering support is present
        if self.contains_html_content or self.list_paragraphs:
            self.doc = ensure_numbering_part(self.doc)
    
        # Save the document
        self.doc.save(output_path)