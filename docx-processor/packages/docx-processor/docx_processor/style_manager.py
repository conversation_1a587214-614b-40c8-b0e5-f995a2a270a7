"""Style management for documents."""

import logging
import copy
from docx import Document
from docx.enum.style import WD_STYLE_TYPE
from docx.shared import Pt

logger = logging.getLogger(__name__)


def ensure_style(
    doc, style_name, base_style=None, font_size=None, bold=None, italic=None
):
    """
    Ensure that a style exists in the document.

    Args:
        doc: Document to modify
        style_name: Name of the style to ensure
        base_style: Base style to use if creating new style
        font_size: Font size in points (optional)
        bold: Whether text should be bold (optional)
        italic: Whether text should be italic (optional)

    Returns:
        The style object
    """
    try:
        # Try to get the style - if it exists, return it
        style = doc.styles[style_name]
        return style
    except KeyError:
        # Style doesn't exist, create it
        logger.info(f"Creating missing style: {style_name}")

        if base_style is None:
            base_style = "Normal"

        style = doc.styles.add_style(style_name, WD_STYLE_TYPE.PARAGRAPH)
        style.base_style = doc.styles[base_style]

        # Apply formatting if specified
        if font_size is not None:
            style.font.size = Pt(font_size)

        if bold is not None:
            style.font.bold = bold

        if italic is not None:
            style.font.italic = italic

        return style


def ensure_required_styles(doc):
    """
    Ensure that all required styles exist in the document.

    Args:
        doc: Document to modify

    Returns:
        Modified document
    """
    # Ensure heading styles exist
    ensure_style(doc, "Heading1", base_style="Normal", font_size=16, bold=True)
    ensure_style(doc, "Heading2", base_style="Normal", font_size=14, bold=True)
    ensure_style(doc, "Heading3", base_style="Normal", font_size=12, bold=True)

    # Ensure list styles exist
    ensure_style(doc, "ListBullet", base_style="Normal")
    ensure_style(doc, "ListNumber", base_style="Normal")

    return doc


def copy_styles_from_template(doc, template_path=None):
    """
    Copy all styles from a template document.

    Args:
        doc: Document to modify
        template_path: Path to template document (optional)

    Returns:
        Modified document
    """
    # If no template specified, just ensure required styles
    if template_path is None:
        return ensure_required_styles(doc)

    # Load the template document
    template = Document(template_path)

    # Get the existing style IDs
    existing_style_ids = [s.style_id for s in doc.styles]

    # Loop through template styles and add any missing ones
    added_styles = []

    for style in template.styles:
        if style.style_id not in existing_style_ids:
            try:
                # Deep copy the style element
                new_element = copy.deepcopy(style.element)

                # Add it to the document's styles
                doc.styles.element.append(new_element)

                added_styles.append(style.style_id)
                logger.debug(f"Added style: {style.style_id}")
            except (KeyError, AttributeError) as e:
                logger.warning(f"Error adding style {style.style_id}: {e}")

    if added_styles:
        logger.info(f"Added {len(added_styles)} styles from template")

    # Make sure required styles exist
    return ensure_required_styles(doc)
