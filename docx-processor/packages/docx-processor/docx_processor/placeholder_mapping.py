"""Placeholder mapping functionality for template processing."""

import json
import logging
from typing import Di<PERSON>, <PERSON><PERSON>, Tuple
from jsonschema import validate, ValidationError

MAPPING_SCHEMA = {
    "type": "object",
    "patternProperties": {
        "^[a-zA-Z][a-zA-Z0-9_-]*$": {  # Logical names should be valid identifiers
            "type": "string",
            "minLength": 1,
            "description": "Physical placeholder in template"
        }
    },
    "additionalProperties": False
}

class PlaceholderMapper:
    """Handles mapping between logical and physical placeholders."""

    RESERVED_KEYS = {'__array_data__', '__charts__', '__sections__'}

    def __init__(self, mapping_path: Optional[str] = None):
        """Initialize the mapper with optional mapping file."""
        self.mapping: Dict[str, str] = {}
        self.reverse_mapping: Dict[str, str] = {}
        self.logger = logging.getLogger("docx_processor")
        
        if mapping_path:
            self.load_mapping(mapping_path)

    def load_mapping(self, mapping_path: str):
        """Load and validate mapping configuration."""
        try:
            with open(mapping_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            errors = self.validate_mapping(data)
            if errors:
                self.logger.warning("Mapping validation errors:")
                for error in errors:
                    self.logger.warning("  - %s", error)
                raise ValueError("Invalid mapping configuration")

            self.mapping = data
            # Create reverse mapping for placeholder lookup
            self.reverse_mapping = {v: k for k, v in data.items()}
            self.logger.info("Loaded %d placeholder mappings", len(self.mapping))
            self.logger.debug("Mappings: %s", self.mapping)

        except FileNotFoundError as e:
            raise FileNotFoundError(f"Mapping file {mapping_path} not found") from e
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in mapping file {mapping_path}") from e

    @staticmethod
    def validate_mapping(mapping_data: Dict[str, str]) -> list:
        """Validate mapping configuration against schema."""
        try:
            validate(instance=mapping_data, schema=MAPPING_SCHEMA)
            return []
        except ValidationError as e:
            return [str(e)]

    def to_physical(self, logical_name: str) -> Optional[str]:
        """Convert logical placeholder name to physical placeholder."""
        if logical_name in self.RESERVED_KEYS:
            return logical_name
        return self.mapping.get(logical_name)

    def to_logical(self, physical_placeholder: str) -> Optional[str]:
        """Convert physical placeholder to logical name."""
        if physical_placeholder in self.RESERVED_KEYS:
            return physical_placeholder
        return self.reverse_mapping.get(physical_placeholder)

    def translate_replacements(self, replacements: Dict[str, str]) -> Dict[str, str]:
        """
        Translate replacements using logical names to replacements using physical placeholders.
        
        Args:
            replacements: Dictionary with logical placeholder names as keys
            
        Returns:
            Dictionary with physical placeholders as keys
        """
        translated = {}
        for logical_name, value in replacements.items():
            if logical_name in self.RESERVED_KEYS:
                translated[logical_name] = value
                continue

            physical = self.to_physical(logical_name)
            if physical is None:
                self.logger.warning(
                    "No physical placeholder mapping found for logical name '%s'",
                    logical_name
                )
                continue
            translated[physical] = value
            
        return translated

    def get_unmapped_placeholders(self, physical_placeholders: list) -> list:
        """
        Find physical placeholders that don't have a logical mapping.
        
        Args:
            physical_placeholders: List of physical placeholders found in template
            
        Returns:
            List of unmapped physical placeholders
        """
        return [p for p in physical_placeholders if p not in self.reverse_mapping and p not in self.RESERVED_KEYS]