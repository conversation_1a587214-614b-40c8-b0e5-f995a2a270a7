"""Support for processing tables with repeating rows with type support."""

from typing import Dict, List, Optional, Any, Union
from docx.table import Table, _Row
import re
from datetime import datetime
import logging
from decimal import Decimal
from .processor import DocxTemplateProcessor
from .chart_processor import ChartProcessor
from .html_handler import contains_html, HtmlHand<PERSON>, prepare_document_for_html


class ValueFormatter:
    """Handles formatting of different value types."""

    def __init__(self, settings):
        """Initialize formatter with settings."""
        self.settings = settings
        self.logger = logging.getLogger("docx_processor.formatter")

    def format_value(self, value: Any) -> str:
        """Format a value based on its type and settings."""
        if value is None:
            return ""
        
        if isinstance(value, bool):
            return str(value).lower()
        
        if isinstance(value, (int, float, Decimal)):
            return self._format_number(value)
        
        if isinstance(value, datetime):
            return self._format_datetime(value)
        
        if isinstance(value, dict):
            return self._format_dict(value)
        
        return str(value)

    def _format_number(self, value: Union[int, float, Decimal]) -> str:
        """Format numeric values according to settings."""
        try:
            # Handle integers
            if isinstance(value, int):
                return format(value, ",").replace(",", self.settings.get("thousands_separator", ","))
            
            # Handle floats and decimals
            str_value = format(value, "f")
            integer_part, _, decimal_part = str_value.partition(".")
            
            # Format integer part with thousands separator
            formatted_integer = format(int(integer_part), ",").replace(
                ",", self.settings.get("thousands_separator", ",")
            )
            
            # Format decimal part with correct separator
            if decimal_part:
                return f"{formatted_integer}{self.settings.get('decimal_separator', '.')}{decimal_part}"
            return formatted_integer
            
        # pylint: disable=W0718
        except Exception as e:
            self.logger.warning("Error formatting number %s: %s", value, str(e))
            return str(value)

    def _format_datetime(self, value: datetime) -> str:
        """Format datetime values according to settings."""
        try:
            return value.strftime(self.settings.get("date_format", "%Y-%m-%d"))
        # pylint: disable=W0718
        except Exception as e:
            self.logger.warning("Error formatting datetime %s: %s", value, str(e))
            return str(value)

    def _format_dict(self, value: Dict) -> str:
        """Format dictionary values according to their type field."""
        try:
            if "type" not in value:
                return str(value)

            if value["type"] == "currency":
                amount = self._format_number(value.get("amount", 0))
                symbol = self.settings.get("currency_symbol", "$")
                position = self.settings.get("currency_position", "before")
                return f"{symbol}{amount}" if position == "before" else f"{amount}{symbol}"

            if value["type"] == "date":
                try:
                    date_value = datetime.strptime(value["value"], "%Y-%m-%d")
                    format_string = value.get("format", self.settings.get("date_format", "%Y-%m-%d"))
                    return date_value.strftime(format_string)
                except ValueError as e:
                    self.logger.warning("Invalid date format in value %s: %s", value["value"], str(e))
                    return value["value"]

            return str(value.get("value", ""))

        # pylint: disable=W0718
        except Exception as e:
            self.logger.warning("Error formatting dictionary %s: %s", value, str(e))
            return str(value)


class TableProcessor:
    """Handles processing of tables with repeatable rows."""

    def __init__(self, settings, placeholder_pattern: str = r"\[(.*?)\]"):
        """Initialize the table processor."""
        self.placeholder_pattern = placeholder_pattern
        self.formatter = ValueFormatter(settings)
        self.logger = logging.getLogger("docx_processor.table")
        self.html_handler = None

    def _validate_collection(self, collection: Dict[str, Any]) -> List[str]:
        """Validate a collection configuration."""
        errors = []
        
        if "placeholders" not in collection:
            errors.append("Missing 'placeholders' in collection")
            return errors
            
        if "values" not in collection:
            errors.append("Missing 'values' in collection")
            return errors

        placeholders = collection["placeholders"]
        values = collection["values"]

        if not isinstance(placeholders, list):
            errors.append("'placeholders' must be a list")
            return errors

        if not isinstance(values, list):
            errors.append("'values' must be a list")
            return errors

        # Check that all value sets match placeholder length
        for idx, value_set in enumerate(values):
            if not isinstance(value_set, list):
                errors.append(f"Value set at index {idx} must be a list")
                continue
                
            if len(value_set) != len(placeholders):
                errors.append(
                    f"Value set at index {idx} has {len(value_set)} values "
                    f"but {len(placeholders)} placeholders"
                )

        return errors

    def _extract_placeholders_from_row(self, row: _Row) -> List[str]:
        """Extract all placeholders from a table row."""
        placeholders = []
        for cell in row.cells:
            text = cell.text.strip()
            matches = re.finditer(self.placeholder_pattern, text)
            placeholders.extend(match.group(0) for match in matches)
        return placeholders

    def _get_row_placeholder_set(self, row: _Row) -> set:
        """Get the set of placeholders in a row."""
        return set(self._extract_placeholders_from_row(row))

    def find_matching_collection(self, row: _Row, collections: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Find the collection that matches the placeholders in the row."""
        row_placeholders = self._get_row_placeholder_set(row)
        if not row_placeholders:
            return None

        for collection in collections:
            collection_placeholders = set(collection.get('placeholders', []))
            if row_placeholders.issubset(collection_placeholders):
                return collection
        
        return None

    def set_html_handler(self, html_handler):
        """Set the HTML handler for this table processor."""
        self.html_handler = html_handler

    def process_table(self, table: Table, replacements: Dict[str, Any]):
        """Process a table with both regular replacements and collections."""
        collections = replacements.get("__array_data__", [])
        if not collections:
            self._process_regular_table(table, replacements)
            return

        # Find template row and matching collection
        for idx, row in enumerate(table.rows):
            collection = self.find_matching_collection(row, collections)
            if collection:
                self.logger.info("Found matching collection for table row %d", idx)
                # Validate collection before processing
                errors = self._validate_collection(collection)
                if errors:
                    for error in errors:
                        self.logger.error("Collection validation error: %s", error)
                    continue
                
                self._process_collection(table, idx, collection)
                return

    def _process_collection(self, table: Table, template_row_idx: int, collection: Dict[str, Any]):
        """Process a collection of values, creating new rows as needed."""
        template_row = table.rows[template_row_idx]
        placeholders = collection["placeholders"]
        values = collection["values"]

        self.logger.info(
            "Processing collection with %d placeholders and %d value sets",
            len(placeholders), len(values)
        )

        # Remove template row
        table._element.remove(template_row._element)

        # Add rows for each value set
        for idx, value_set in enumerate(values):
            self.logger.debug("Processing value set %d", idx)
            new_row = table.add_row()
            self._copy_row_format(template_row, new_row)

            # Replace placeholders with formatted values
            for cell_idx, cell in enumerate(new_row.cells):
                if cell_idx < len(template_row.cells):
                    template_text = template_row.cells[cell_idx].text
                    new_text = template_text

                    # Check for HTML content in cells
                    for ph_idx, placeholder in enumerate(placeholders):
                        if placeholder in new_text and ph_idx < len(value_set):
                            value = value_set[ph_idx]
                            
                            # Handle HTML content if present and handler is set
                            if isinstance(value, str) and contains_html(value) and self.html_handler and template_text.strip() == placeholder:
                                # Clear the cell text
                                cell.text = ""
                                # Get first paragraph in the cell
                                paragraph = cell.paragraphs[0]
                                # Replace with HTML content
                                self.html_handler.replace_with_html(paragraph, value)
                            else:
                                # Regular value replacement
                                formatted_value = self.formatter.format_value(value)
                                new_text = new_text.replace(placeholder, formatted_value)
                                cell.text = new_text

    def _copy_row_format(self, src_row: _Row, dst_row: _Row):
        """Copy formatting from source row to destination row."""
        for src_cell, dst_cell in zip(src_row.cells, dst_row.cells):
            # Copy width if it exists
            if hasattr(src_cell, 'width'):
                dst_cell.width = src_cell.width

            # Get source properties
            src_tcPr = src_cell._tc.get_or_add_tcPr()
            dst_tcPr = dst_cell._tc.get_or_add_tcPr()

            # Copy vertical alignment if it exists
            if hasattr(src_tcPr, 'vAlign') and src_tcPr.vAlign is not None:
                dst_tcPr.vAlign = src_tcPr.vAlign

            # Copy text direction if it exists
            if hasattr(src_tcPr, 'textDirection') and src_tcPr.textDirection is not None:
                dst_tcPr.textDirection = src_tcPr.textDirection
                
            # Copy shading if it exists
            if hasattr(src_tcPr, 'shd') and src_tcPr.shd is not None:
                dst_tcPr.shd = src_tcPr.shd
                
            # Copy cell margins if they exist
            if hasattr(src_tcPr, 'tcMar') and src_tcPr.tcMar is not None:
                dst_tcPr.tcMar = src_tcPr.tcMar

    def _process_regular_table(self, table: Table, replacements: Dict[str, str]):
        """Process a table with regular replacements."""
        for row in table.rows:
            self._process_regular_row(row, replacements)

    def _process_regular_row(self, row: _Row, replacements: Dict[str, str]):
        """Process a row with regular replacements."""
        for cell in row.cells:
            # Handle potentially HTML content in cell paragraphs
            for paragraph in cell.paragraphs:
                text = paragraph.text
                
                # Check if any placeholder contains HTML
                for placeholder, value in replacements.items():
                    if isinstance(placeholder, str) and placeholder in text:
                        if isinstance(value, str) and contains_html(value) and self.html_handler:
                            # If paragraph only contains this placeholder, replace with HTML
                            if text.strip() == placeholder:
                                self.html_handler.replace_with_html(paragraph, value)
                            else:
                                # Format value regularly for mixed-content paragraphs
                                formatted_value = self.formatter.format_value(value)
                                paragraph.text = paragraph.text.replace(placeholder, formatted_value)
                        else:
                            # Regular replacement
                            formatted_value = self.formatter.format_value(value)
                            paragraph.text = paragraph.text.replace(placeholder, formatted_value)


class TableAwareDocxTemplateProcessor(DocxTemplateProcessor):
    """Extension of DocxTemplateProcessor with table collection and HTML support."""

    def __init__(self, template_path: str, settings):
        """Initialize the processor with enhanced table support."""
        super().__init__(template_path, settings)
        self.table_processor = TableProcessor(settings)
        self.chart_processor = ChartProcessor(self.doc)
        self.logger = logging.getLogger("docx_processor.enhanced")
        self.html_enabled = False
        
    def _initialize_html_support(self):
        """Initialize HTML support if needed."""
        if not self.html_enabled:
            # Prepare document for HTML content
            self.doc = prepare_document_for_html(self.doc)
            self.html_handler = HtmlHandler(self.doc)
            self.table_processor.set_html_handler(self.html_handler)
            self.html_enabled = True

    def process_document(self, replacements: Dict[str, Any]):
        """Process the document with both regular replacements and collections."""
        self.logger.info("Starting enhanced document processing")
        
        # Check if HTML content is present in replacements
        html_content_present = False
        for value in replacements.values():
            if isinstance(value, str) and contains_html(value):
                html_content_present = True
                self.logger.info("HTML content detected in replacements")
                break
                
        # Initialize HTML support if needed
        if html_content_present:
            self._initialize_html_support()
            
        # Process document with all replacements
        super().process_document(replacements)

        # Process charts if present
        if '__charts__' in replacements:
            self.chart_processor.process_charts(replacements['__charts__'])
            # Process paragraphs for charts
            for paragraph in self.doc.paragraphs:
                self.chart_processor.process_paragraph(paragraph)

        # Process tables with collections
        table_count = len(self.doc.tables)
        self.logger.info("Processing %d tables", table_count)
        
        for idx, table in enumerate(self.doc.tables):
            self.logger.debug("Processing table %d/%d", idx + 1, table_count)
            self.table_processor.process_table(table, replacements)

        self.logger.info("Document processing completed")
