"""Utility functions for document processing."""

from datetime import datetime
from typing import Dict, Any

def process_special_value(value: Any, settings) -> Any:
    """Process special values with respect to settings."""
    if not isinstance(value, str):
        return value
        
    # Only process strings that start with @
    if not value.startswith("@"):
        return value

    now = datetime.now()
    special_values = {
        "@DATE": now.strftime(settings.get("date_format")),
        "@DATE_LONG": now.strftime(settings.get("long_date_format")),
        "@TIME": now.strftime(settings.get("time_format")),
        "@YEAR": now.strftime("%Y"),
        "@MONTH": now.strftime("%m"),
        "@MONTH_NAME": now.strftime("%B"),
        "@DAY": now.strftime("%d"),
        "@WEEKDAY": now.strftime("%A"),
    }

    return special_values.get(value.upper(), value)

def process_replacements(replacements: Dict[str, Any], settings) -> Dict[str, Any]:
    """Process all special values in replacements dictionary."""
    return {
        key: process_special_value(value, settings)
        for key, value in replacements.items()
        if isinstance(key, str)  # Only process string keys
    }