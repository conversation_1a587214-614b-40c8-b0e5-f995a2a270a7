{"name": "docx-processor", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "packages/docx-processor/docx_processor", "targets": {"docs": {"executor": "@nxlv/python:run-commands", "options": {"command": "pydoc-markdown -p docx_processor --render-toc > docs/source/api.md", "cwd": "packages/docx-processor"}}, "lock": {"executor": "@nxlv/python:run-commands", "options": {"command": "poetry lock --no-update", "cwd": "packages/docx-processor"}}, "add": {"executor": "@nxlv/python:add", "options": {}}, "update": {"executor": "@nxlv/python:update", "options": {}}, "remove": {"executor": "@nxlv/python:remove", "options": {}}, "build": {"executor": "@nxlv/python:build", "outputs": ["{projectRoot}/dist"], "options": {"outputPath": "packages/docx-processor/dist", "publish": false, "lockedVersions": true, "bundleLocalDependencies": true}}, "install": {"executor": "@nxlv/python:install", "options": {"silent": false, "args": "", "cwd": "packages/docx-processor", "cacheDir": ".cache/pypoetry", "verbose": false, "debug": false}}, "lint": {"executor": "@nxlv/python:flake8", "outputs": ["{workspaceRoot}/reports/packages/docx-processor/pylint.txt"], "options": {"outputFile": "reports/packages/docx-processor/pylint.txt"}}, "poetry": {"executor": "@nxlv/python:run-commands", "options": {"command": "poetry {args}", "cwd": "packages/docx-processor"}, "dependsOn": ["install"]}, "cli": {"executor": "@nxlv/python:run-commands", "options": {"command": "poetry run docx-processor", "cwd": "packages/docx-processor"}}, "test": {"executor": "@nxlv/python:pytest", "outputs": ["{workspaceRoot}/reports/packages/docx-processor/unittests", "{workspaceRoot}/coverage/packages/docx-processor"], "options": {"command": "poetry run pytest tests/", "cwd": "packages/docx-processor", "outputFile": "reports/packages/docx-processor/unittests", "testFile": ["tests"]}}, "tox": {"executor": "@nxlv/python:tox", "outputs": ["{workspaceRoot}/reports/packages/docx-processor/unittests", "{workspaceRoot}/coverage/packages/docx-processor"], "options": {"outputFile": "reports/packages/docx-processor/unittests", "coverageDirectory": "reports/packages/docx-processor/htmlcov", "testFile": ["tests"], "silent": false, "args": ""}}}, "tags": []}