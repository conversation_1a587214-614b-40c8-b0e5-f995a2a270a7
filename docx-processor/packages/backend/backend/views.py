"""Views for document generation endpoints."""

import re
import json
import logging
from pathlib import Path
from typing import List, <PERSON><PERSON>
from docx import Document
from docx.document import Document as DocumentType
from django.http import FileResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from docx_processor import (
    Settings,
    TableAwareDocxTemplateProcessor,
    PlaceholderMapper,
    load_replacements,
)
from .temp_file_manager import TempFileManager

logger = logging.getLogger(__name__)

temp_manager = TempFileManager(Path(__file__).resolve().parent / "temp")

def get_template_path(template_name: str) -> Path:
    """Get the full path for a template file."""
    base_dir = Path(__file__).resolve().parent
    templates_dir = base_dir / "templates"
    return templates_dir / template_name

@csrf_exempt
@require_http_methods(["POST"])
def generate_document(request):
    """Generate a document using default template and settings."""
    try:
        # Load default settings and template
        settings = Settings()
        template_path = get_template_path("default-template.docx")
        
        # Initialize processor and mapper
        processor = TableAwareDocxTemplateProcessor(template_path, settings)
        mapper = PlaceholderMapper(get_template_path("placeholder-mappings.json"))
        
        # Load logical replacements and translate them
        logical_replacements_path = get_template_path("default-logical-replacements.json")
        logical_replacements = load_replacements(logical_replacements_path)
        replacements = mapper.translate_replacements(logical_replacements)
        
        # Generate document with automatic cleanup
        with temp_manager.temp_file(prefix="generated", suffix=".docx") as output_path:
            # Process and save the document
            processor.process_document(replacements)
            processor.save(str(output_path))
            
            # Open and return the file, it will be cleaned up after the response is sent
            return FileResponse(
                open(output_path, "rb"),
                as_attachment=True,
                filename="generated-document.docx"
            )

    except FileNotFoundError as e:
        return JsonResponse(
            {"error": f"File not found: {str(e)}"}, 
            status=404
        )
    # pylint: disable=W0718
    except Exception as e:
        logger.error('Exception occurred during request processing', exc_info=e)

        return JsonResponse(
            {"error": f"Error generating document: {str(e)}"},
            status=500
        )

@csrf_exempt
@require_http_methods(["POST"])
def generate_custom_document(request):
    """Generate a document using custom template and custom placeholder mapping."""
    try:
        # Validate required files and data
        if "template" not in request.FILES:
            return JsonResponse(
                {"error": "No template file provided"},
                status=400
            )
        
        # Get and validate mappings from POST data
        try:
            mappings = json.loads(request.POST.get("mappings", "{}"))
        except json.JSONDecodeError:
            return JsonResponse(
                {"error": "Invalid JSON in mappings"},
                status=400
            )
        
        # Use context manager for both temporary files
        with temp_manager.temp_files(
            ("template", ".docx"),
            ("output", ".docx")
        ) as (temp_template_path, output_path):
            # Save uploaded template
            with open(temp_template_path, "wb") as f:
                for chunk in request.FILES["template"].chunks():
                    f.write(chunk)
            
            # Load settings
            settings = Settings()
            
            # Initialize processor with custom template
            processor = TableAwareDocxTemplateProcessor(temp_template_path, settings)
            
            # Initialize mapper with provided mappings
            mapper = PlaceholderMapper()
            mapper.mapping = mappings
            
            # Load our standard logical replacements
            logical_replacements_path = get_template_path("default-logical-replacements.json")
            logical_replacements = load_replacements(logical_replacements_path)
            
            # Translate logical replacements using the custom mapping
            replacements = mapper.translate_replacements(logical_replacements)
            
            # Process and save the document
            processor.process_document(replacements)
            processor.save(str(output_path))
            
            # Return the file, it will be cleaned up after the response is sent
            return FileResponse(
                open(output_path, "rb"),
                as_attachment=True,
                filename="generated-custom-document.docx"
            )

    except FileNotFoundError as e:
        return JsonResponse(
            {"error": f"File not found: {str(e)}"},
            status=404
        )
    # pylint: disable=W0718
    except Exception as e:
        logger.error('Exception occurred during request processing', exc_info=e)

        return JsonResponse(
            {"error": f"Error generating document: {str(e)}"},
            status=500
        )
    

@require_http_methods(["GET"])
def get_placeholder_mappings(request):
    """Return the available placeholder mappings with categories."""
    try:
        mapping_file = get_template_path("placeholder-mappings.json")
        categories_file = get_template_path("placeholder-categories.json")

        with open(mapping_file, 'r', encoding='utf-8') as f:
            mappings = json.load(f)
            
        with open(categories_file, 'r', encoding='utf-8') as f:
            categories = json.load(f)
            
        return JsonResponse({
            'mappings': mappings,
            'categories': categories,
            'special_values': [
                '@DATE',
                '@DATE_LONG',
                '@TIME',
                '@YEAR',
                '@MONTH',
                '@MONTH_NAME',
                '@DAY',
                '@WEEKDAY'
            ]
        })
    
    except FileNotFoundError:
        return JsonResponse(
            {"error": "Mapping configuration not found"},
            status=404
        )
    except json.JSONDecodeError:
        return JsonResponse(
            {"error": "Invalid mapping configuration"},
            status=500
        )
    # pylint: disable=W0718
    except Exception as e:
        logger.error('Exception occurred during request processing', exc_info=e)

        return JsonResponse(
            {"error": f"Error loading mappings: {str(e)}"},
            status=500
        )
    
@csrf_exempt
@require_http_methods(["POST"])
def process_custom_document(request):
    """Process a custom document with provided template and mappings."""
    try:
        # Validate required files and data
        if "template" not in request.FILES:
            return JsonResponse(
                {"error": "No template file provided"},
                status=400
            )
        
        # Get and validate mappings from POST data
        try:
            replacements = json.loads(request.POST.get("replacements", "{}"))
        except json.JSONDecodeError:
            return JsonResponse(
                {"error": "Invalid JSON in replacements"},
                status=400
            )

        # Use context manager for both temporary files
        with temp_manager.temp_files(
            ("template", ".docx"),
            ("output", ".docx")
        ) as (temp_template_path, output_path):
            # Save uploaded template
            with open(temp_template_path, "wb") as f:
                for chunk in request.FILES["template"].chunks():
                    f.write(chunk)
            
            # Initialize processor with custom template
            settings = Settings()
            processor = TableAwareDocxTemplateProcessor(temp_template_path, settings)
            
            # Process and save the document
            processor.process_document(replacements)
            processor.save(str(output_path))
            
            # Return the file, it will be cleaned up after the response is sent
            return FileResponse(
                open(output_path, "rb"),
                as_attachment=True,
                filename="processed-document.docx"
            )

    except FileNotFoundError as e:
        return JsonResponse(
            {"error": f"File not found: {str(e)}"},
            status=404
        )
    # pylint: disable=W0718
    except Exception as e:
        logger.error('Exception occurred during request processing', exc_info=e)

        return JsonResponse(
            {"error": f"Error processing document: {str(e)}"},
            status=500
        )

class PlaceholderExtractionError(Exception):
    """Custom exception for placeholder extraction errors."""
    pass

def extract_placeholders_from_paragraph(text: str, pattern: str) -> List[str]:
    """Extract placeholders from a paragraph using the given pattern."""
    try:
        matches = re.finditer(pattern, text)
        # Get the first capture group from each match
        return [match.group(0) for match in matches]
    except re.error as e:
        raise PlaceholderExtractionError(f"Invalid regex pattern: {str(e)}") from e

def process_document_element(element, pattern: str) -> List[str]:
    """Process a document element (paragraph or table cell) for placeholders."""
    if hasattr(element, 'text'):
        return extract_placeholders_from_paragraph(element.text, pattern)
    return []

# Here message about "doc: Document": Expected class but received "(docx: str | IO[bytes] | None = None) -> Document" Pylance
def extract_placeholders_from_document(doc: DocumentType, pattern: str) -> Tuple[List[str], dict]:
    """
    Extract all placeholders from a Word document.
    
    Returns:
        Tuple of (list of placeholders, statistics dictionary)
    """
    placeholders = set()
    stats = {
        'paragraphs_processed': 0,
        'tables_processed': 0,
        'cells_processed': 0,
        'matches_found': 0
    }

    # Process main document paragraphs
    for paragraph in doc.paragraphs:
        found = process_document_element(paragraph, pattern)
        placeholders.update(found)
        stats['matches_found'] += len(found)
        stats['paragraphs_processed'] += 1

    # Process tables
    for table in doc.tables:
        stats['tables_processed'] += 1
        for row in table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    found = process_document_element(paragraph, pattern)
                    placeholders.update(found)
                    stats['matches_found'] += len(found)
                    stats['cells_processed'] += 1

    # Process headers and footers
    for section in doc.sections:
        if section.header:
            for paragraph in section.header.paragraphs:
                found = process_document_element(paragraph, pattern)
                placeholders.update(found)
                stats['matches_found'] += len(found)
                stats['paragraphs_processed'] += 1

        if section.footer:
            for paragraph in section.footer.paragraphs:
                found = process_document_element(paragraph, pattern)
                placeholders.update(found)
                stats['matches_found'] += len(found)
                stats['paragraphs_processed'] += 1

    return sorted(list(placeholders)), stats

@csrf_exempt
@require_http_methods(["POST"])
def extract_placeholders(request):
    """Extract placeholders from an uploaded template document."""
    try:
        if 'template' not in request.FILES:
            return JsonResponse(
                {"error": "No template file provided"},
                status=400
            )

        template_file = request.FILES['template']
        pattern = request.POST.get('pattern', r'\[(.*?)\]')

        # Basic pattern validation
        if not pattern:
            return JsonResponse(
                {"error": "Invalid placeholder pattern"},
                status=400
            )

        # Load and process document
        doc = Document(template_file)
        placeholders, stats = extract_placeholders_from_document(doc, pattern)

        return JsonResponse({
            "placeholders": placeholders,
            "stats": stats,
            "pattern_used": pattern
        })

    except PlaceholderExtractionError as e:
        logger.error("Placeholder extraction error: %s", str(e))
        return JsonResponse(
            {"error": str(e)},
            status=400
        )
    # pylint: disable=W0718
    except Exception as e:
        logger.error("Error processing template: %s", str(e))
        return JsonResponse(
            {"error": "Error processing template document"},
            status=500
        )
    
@csrf_exempt
@require_http_methods(["POST"])
def process_template(request):
    """Process a document with provided template name and mappings."""
    try:
        data = json.loads(request.body)
        template_name = data.get('template')
        mapping = data.get('mapping')

        if not template_name or not mapping:
            return JsonResponse({"error": "Template name and mapping are required"}, status=400)

        template_path = get_template_path(template_name)
        if not template_path.exists():
            return JsonResponse({"error": f"Template file '{template_name}' not found"}, status=404)

        # Use context manager for the temporary output file
        with temp_manager.temp_file(prefix="processed", suffix=".docx") as output_path:
            # Initialize processor with the specified template
            settings = Settings()
            processor = TableAwareDocxTemplateProcessor(template_path, settings)

            # Process and save the document
            processor.process_document(mapping)
            processor.save(str(output_path))

            # Return the file, it will be cleaned up after the response is sent
            return FileResponse(
                open(output_path, "rb"),
                as_attachment=True,
                filename="processed-document.docx"
            )

    except json.JSONDecodeError:
        return JsonResponse({"error": "Invalid JSON in request body"}, status=400)
    except FileNotFoundError as e:
        return JsonResponse({"error": f"File not found: {str(e)}"}, status=404)
    except Exception as e:
        logger.error('Exception occurred during request processing', exc_info=e)
        return JsonResponse({"error": f"Error processing document: {str(e)}"}, status=500)