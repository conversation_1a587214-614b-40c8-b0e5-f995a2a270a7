"""Temporary file management utilities."""

import uuid
import time
from pathlib import Path
from contextlib import contextmanager
from typing import Generator
import logging

logger = logging.getLogger("docx_processor")

# pylint: disable=W0718
class TempFileManager:
    """Manages temporary files with unique names and automatic cleanup."""
    
    def __init__(self, base_dir: Path):
        """Initialize the manager with a base directory for temp files."""
        self.base_dir = base_dir
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
    def _generate_unique_path(self, prefix: str, suffix: str) -> Path:
        """Generate a unique path using UUID to prevent collisions."""
        unique_id = str(uuid.uuid4())
        return self.base_dir / f"{prefix}_{unique_id}{suffix}"
    
    @contextmanager
    def temp_file(self, prefix: str = "temp", suffix: str = "") -> Generator[Path, None, None]:
        """
        Context manager for temporary file handling.
        
        Args:
            prefix: Prefix for the temporary file name
            suffix: Suffix (extension) for the temporary file
            
        Yields:
            Path to the temporary file
            
        The file is automatically deleted when the context is exited.
        """
        temp_path = self._generate_unique_path(prefix, suffix)
        try:
            yield temp_path
        finally:
            try:
                if temp_path.exists():
                    temp_path.unlink()
                    logger.debug("Cleaned up temporary file: %s", temp_path)
            except Exception as e:
                logger.error("Error cleaning up temporary file %s: %s", temp_path, str(e))

    @contextmanager
    def temp_files(self, *specs: tuple[str, str]) -> Generator[list[Path], None, None]:
        """
        Context manager for handling multiple temporary files.
        
        Args:
            *specs: Tuples of (prefix, suffix) for each temporary file
            
        Yields:
            List of paths to the temporary files
            
        All files are automatically deleted when the context is exited.
        """
        paths: list[Path] = []
        try:
            for prefix, suffix in specs:
                paths.append(self._generate_unique_path(prefix, suffix))
            yield paths
        finally:
            for path in paths:
                try:
                    if path.exists():
                        path.unlink()
                        logger.debug("Cleaned up temporary file: %s", path)
                except Exception as e:
                    logger.error("Error cleaning up temporary file %s: %s", path, str(e))

    def cleanup_old_files(self, max_age_hours: int = 24):
        """
        Clean up temporary files older than the specified age.
        
        Args:
            max_age_hours: Maximum age of files in hours before cleanup
        """
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        try:
            for file_path in self.base_dir.iterdir():
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        try:
                            file_path.unlink()
                            logger.info("Cleaned up old temporary file: %s", file_path)
                        except Exception as e:
                            logger.error("Error cleaning up old file %s: %s", file_path, str(e))
        except Exception as e:
            logger.error("Error during old files cleanup: %s", str(e))