"""backend URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.urls import path
from . import views

urlpatterns = [
    path('api/generate', views.generate_document, name='generate_document'),
    path('api/generate-custom', views.generate_custom_document, name='generate_custom_document'),
    path('api/process-custom', views.process_custom_document, name='process_custom_document'),
    path('api/placeholder-mappings', views.get_placeholder_mappings, name='get_placeholder_mappings'),
    path('api/extract-placeholders', views.extract_placeholders, name='extract_placeholders'),
    path('api/process-template', views.process_template, name='process_template'),
]
