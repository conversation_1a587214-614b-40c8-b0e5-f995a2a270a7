[tool.coverage.run]
branch = true
source = ["backend"]

[tool.coverage.report]
exclude_lines = ['if TYPE_CHECKING:']
show_missing = true

[tool.pytest.ini_options]
addopts = "--cov --cov-fail-under=100 --cov-report html:'../../coverage/packages/backend/html' --cov-report xml:'../../coverage/packages/backend/coverage.xml' --junitxml='../../reports/packages/backend/unittests/junit.xml' --html='../../reports/packages/backend/unittests/html/index.html'"

[tool.poetry]
name = "docx_processor_api"
version = "1.0.0"
description = "Backend for the PoC Word templates generator"
authors = ["<PERSON> <jean.lazar<PERSON>@cetic.be>"]
license = 'Proprietary'
readme = 'README.md'

[[tool.poetry.packages]]
include = "backend"

[tool.poetry.dependencies]
python = ">=3.10,<4.0"
django = "^5.1.4"
djangorestframework = "^3.15.2"
django-cors-headers = "^4.3.1"
docx_processor = { path = "../docx-processor" }
beautifulsoup4 = "^4.13.3"

[tool.poetry.group.dev.dependencies]
pytest = ">=7.0"
pytest-django = "^4.5.2"
black = ">=22.0"
isort = ">=5.0"
mypy = ">=1.0"
pylint = ">=2.17.0,<3.0.0"
flake8 = ">=6.0.0,<7.0.0"

[build-system]
requires = ["poetry-core==1.1.0"]
build-backend = "poetry.core.masonry.api"
