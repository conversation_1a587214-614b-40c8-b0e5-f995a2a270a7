{"name": "backend", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "packages/backend", "targets": {"build": {"executor": "@nxlv/python:build", "outputs": ["{projectRoot}/dist"], "options": {"outputPath": "packages/backend/dist", "packageDir": "packages/backend"}}, "install": {"executor": "@nxlv/python:install", "options": {"silent": false, "args": "", "cwd": "packages/backend", "verbose": false}}, "lint": {"executor": "@nxlv/python:flake8", "options": {"targetDir": "packages/backend"}}, "test": {"executor": "@nxlv/python:test", "options": {"targetDir": "packages/backend", "testDir": "tests", "coverageProvider": "pytest", "silent": false}}, "serve": {"executor": "nx:run-commands", "options": {"command": "poetry run python manage.py runserver 0.0.0.0:8000", "cwd": "packages/backend"}}}, "tags": []}