"""Generate a Word document from HTML content using a template."""

import argparse
from .rich_text_handler import RichTextHandler

# Example usage with nested HTML
DEFAULT_OUTPUT_PATH = "output.docx"
TEMPLATE_PATH = "html_support_processor/template.docx"
HTML_CONTENT = """
<div class="container">
    <h1>Main Title</h1>
    <p>A paragraph with <span style="font-style:italic">styled text</span> in italic</p>
    <ul>
        <li>First item</li>
        <li>Second item</li>
    </ul>
    <p>Another paragraph with <span style="font-style:italic">more styled text</span></p>
    <ol>
        <li>List item 1</li>
        <li>List item 2</li>
    </ol>
"""

def main():
    """
    Main function to handle command line arguments and generate the document.
    """

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--template", help='Document template path that has "content" placeholder', default=None
    )
    parser.add_argument("output", nargs="?", help="Output document path", default=DEFAULT_OUTPUT_PATH)

    args = parser.parse_args()

    handler = RichTextHandler()

    if args.template and args.output:
        doc = handler.generate_document(args.template, {"content": HTML_CONTENT})
        doc.save(args.output)

        print(f"'{args.output}' document generated successfully.")
    else:
        doc = handler.generate_document(TEMPLATE_PATH, {"content": HTML_CONTENT})

        doc.save(DEFAULT_OUTPUT_PATH)

        print(f"'{DEFAULT_OUTPUT_PATH}' document generated successfully.")

if __name__ == "__main__":
    main()