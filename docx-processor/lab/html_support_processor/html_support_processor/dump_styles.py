"""
Dump styles from a Word document.
This script can be used to dump the styles from a Word document.
It can be used to check the styles available in a document and their properties.
"""

import argparse
from docx import Document

def main():
    """
    Main function to handle command line arguments and dump styles from a document.
    """
    parser = argparse.ArgumentParser()
    parser.add_argument("document", nargs="?", help="Document path", default=None)

    args = parser.parse_args()

    if args.document:
        doc = Document(args.document)

        print(f"Styles in '{args.document}':")
        for x in doc.styles:
            print(x)
    else:
        doc = Document()

        print("Default styles:")
        for x in doc.styles:
            print(x)

if __name__ == "__main__":
    main()