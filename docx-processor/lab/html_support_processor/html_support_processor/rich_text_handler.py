"""
Rich Text Handler for Django Document Generation
This module provides functionality to parse and handle rich text content
for generating Word documents using a template.
It uses BeautifulSoup for HTML parsing and python-docx for document generation.
"""

import copy
from bs4 import BeautifulSoup
from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement
from docx.oxml.ns import qn

def merge_numbering_xml(template_doc):
    """
    Merge needed bullet and numbering definitions into the document's existing numbering.xml
    """
    import tempfile
    import os
    from zipfile import ZipFile
    from lxml import etree
    
    # First check if the document has a numbering.xml file
    temp_path = tempfile.NamedTemporaryFile(suffix='.docx', delete=False).name
    template_doc.save(temp_path)
    
    has_numbering = False
    with ZipFile(temp_path, 'r') as zip_ref:
        has_numbering = 'word/numbering.xml' in zip_ref.namelist()
        if has_numbering:
            numbering_content = zip_ref.read('word/numbering.xml')
    
    # If no numbering.xml exists, create a basic one
    if not has_numbering:
        numbering_content = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?><w:numbering xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"></w:numbering>'
    
    # Parse the existing numbering content
    numbering_root = etree.fromstring(numbering_content)
    
    # Find the highest abstractNumId currently in use
    ns = {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}
    abstract_nums = numbering_root.findall('.//w:abstractNum', ns)
    max_abstract_id = 0
    for abstract_num in abstract_nums:
        id_attr = abstract_num.get('{%s}abstractNumId' % ns['w'])
        if id_attr and int(id_attr) > max_abstract_id:
            max_abstract_id = int(id_attr)
    
    # Find the highest numId currently in use
    nums = numbering_root.findall('.//w:num', ns)
    max_num_id = 0
    for num in nums:
        id_attr = num.get('{%s}numId' % ns['w'])
        if id_attr and int(id_attr) > max_num_id:
            max_num_id = int(id_attr)
    
    # Create new abstract numbering for bullet list
    bullet_abstract_id = max_abstract_id + 1
    bullet_abstract = etree.fromstring(f'''
    <w:abstractNum xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" w:abstractNumId="{bullet_abstract_id}">
        <w:nsid w:val="FFFFFF89" />
        <w:multiLevelType w:val="singleLevel" />
        <w:tmpl w:val="29761A62" />
        <w:lvl w:ilvl="0">
            <w:start w:val="1" />
            <w:numFmt w:val="bullet" />
            <w:pStyle w:val="ListBullet" />
            <w:lvlText w:val="•" />
            <w:lvlJc w:val="left" />
            <w:pPr>
                <w:tabs>
                    <w:tab w:val="num" w:pos="360" />
                </w:tabs>
                <w:ind w:left="360" w:hanging="360" />
            </w:pPr>
            <w:rPr>
                <w:rFonts w:ascii="Symbol" w:hAnsi="Symbol" w:hint="default" />
            </w:rPr>
        </w:lvl>
    </w:abstractNum>
    ''')
    
    # Create new abstract numbering for numbered list
    number_abstract_id = max_abstract_id + 2
    number_abstract = etree.fromstring(f'''
    <w:abstractNum xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" w:abstractNumId="{number_abstract_id}">
        <w:nsid w:val="FFFFFF88" />
        <w:multiLevelType w:val="singleLevel" />
        <w:tmpl w:val="D0A62B40" />
        <w:lvl w:ilvl="0">
            <w:start w:val="1" />
            <w:numFmt w:val="decimal" />
            <w:pStyle w:val="ListNumber" />
            <w:lvlText w:val="%1." />
            <w:lvlJc w:val="left" />
            <w:pPr>
                <w:tabs>
                    <w:tab w:val="num" w:pos="360" />
                </w:tabs>
                <w:ind w:left="360" w:hanging="360" />
            </w:pPr>
        </w:lvl>
    </w:abstractNum>
    ''')
    
    # Add the abstract numbering definitions to the existing numbering.xml
    numbering_root.append(bullet_abstract)
    numbering_root.append(number_abstract)
    
    # Create new numbering instances that reference the abstract definitions
    bullet_num_id = max_num_id + 1
    bullet_num = etree.fromstring(f'''
    <w:num xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" w:numId="{bullet_num_id}">
        <w:abstractNumId w:val="{bullet_abstract_id}" />
    </w:num>
    ''')
    
    number_num_id = max_num_id + 2
    number_num = etree.fromstring(f'''
    <w:num xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" w:numId="{number_num_id}">
        <w:abstractNumId w:val="{number_abstract_id}" />
    </w:num>
    ''')
    
    # Add the numbering instances to the existing numbering.xml
    numbering_root.append(bullet_num)
    numbering_root.append(number_num)
    
    # Create a new docx file with the merged numbering.xml
    temp_output = tempfile.NamedTemporaryFile(suffix='.docx', delete=False).name
    
    with ZipFile(temp_path, 'r') as inzip:
        with ZipFile(temp_output, 'w') as outzip:
            # Copy all files except numbering.xml
            for item in inzip.infolist():
                if item.filename != 'word/numbering.xml':
                    outzip.writestr(item, inzip.read(item.filename))
            
            # Add the merged numbering.xml
            outzip.writestr('word/numbering.xml', etree.tostring(numbering_root, 
                                                  xml_declaration=True, 
                                                  encoding='UTF-8', 
                                                  standalone=True))
    
    # Clean up the input temp file
    os.unlink(temp_path)
    
    # Load the fixed document
    fixed_doc = Document(temp_output)
    
    # Clean up the output temp file
    os.unlink(temp_output)
    
    # Store the bullet and number IDs for reference when creating lists
    fixed_doc._bullet_num_id = bullet_num_id
    fixed_doc._number_num_id = number_num_id
    
    return fixed_doc
    
class RichTextHandler:
    """
    Rich Text Handler for Document Generation
    This class handles the parsing of rich text content and its conversion
    into a Word document format. It maintains the nested structure of HTML
    elements and applies the appropriate styles to the document.
    Attributes:
        preserved_styles (dict): A dictionary defining the styles to be preserved
        during the conversion process.
    """
    def __init__(self):
        self._doc = None
        self.preserved_styles = {
            'p': {'font_size': '1em'},
            'h1': {'font_size': '2em'},
            'h2': {'font_size': '1.5em'},
            'span': {'italic': True},
            'strong': {'bold': True},
            'ul': {'bullet_points': True},
            'ol': {'numbered_list': True}
        }

    def import_missing_styles(self, template_path):
        """
        Import all missing styles from a source document (or a new default document)
        into the template document.
        
        Args:
            template_path: The path to the document to add styles to
        
        Returns:
            The template document with added styles
        """
        template = Document(template_path)

        source_doc = Document()

        existing_style_ids = [s.style_id for s in template.styles]
        
        added_styles = []
        
        for style in source_doc.styles:
            if style.style_id not in existing_style_ids:
                try:
                    new_element = copy.deepcopy(style.element)
                    
                    template.styles.element.append(new_element)
                    
                    added_styles.append(style.style_id)
                except (KeyError, AttributeError) as e:
                    print(f"Error adding style {style.style_id}: {e}")
        
        self._ensure_list_styles(template)
        
        return template

    def _ensure_list_styles(self, doc):
        numbering_part = doc.part.numbering_part
        if numbering_part is None:
            numbering_part = doc.part.new_numbering()

        numbering_elm = numbering_part.element

        # Bullet list numbering definition
        if "ListBullet" in doc.styles:
            if not self._has_numbering_def(numbering_elm, "List Bullet"):
                num_id = numbering_part.numbering.add_abstract_num()
                num = CT_Num(numId=numbering_part.numbering.get_next_num_id())
                num.abstractNumId.val = num_id
                numbering_elm.append(num)

                abstract_num = numbering_elm.abstractNum[num_id]
                lvl = CT_Lvl(ilvl=0)
                pPr = CT_PPr()
                pPr.numPr = OxmlElement('w:numPr')
                pPr.numPr.append(OxmlElement('w:ilvl'))
                pPr.numPr[0].val = 0
                pPr.numPr.append(OxmlElement('w:numId'))
                pPr.numPr[1].val = num_id
                lvl.pPr = pPr
                lvl.lvlText = OxmlElement('w:lvlText')
                lvl.lvlText.val = '•'
                lvl.numFmt = OxmlElement('w:numFmt')
                lvl.numFmt.val = 'bullet'
                abstract_num.append(lvl)

        # Numbered list numbering definition
        if "ListNumber" in doc.styles:
            if not self._has_numbering_def(numbering_elm, "List Number"):
                num_id = numbering_part.numbering.add_abstract_num()
                num = CT_Num(numId=numbering_part.numbering.get_next_num_id())
                num.abstractNumId.val = num_id
                numbering_elm.append(num)

                abstract_num = numbering_elm.abstractNum[num_id]
                lvl = CT_Lvl(ilvl=0)
                pPr = CT_PPr()
                pPr.numPr = OxmlElement('w:numPr')
                pPr.numPr.append(OxmlElement('w:ilvl'))
                pPr.numPr[0].val = 0
                pPr.numPr.append(OxmlElement('w:numId'))
                pPr.numPr[1].val = num_id
                lvl.pPr = pPr
                lvl.lvlText = OxmlElement('w:lvlText')
                lvl.lvlText.val = '%1.'
                lvl.numFmt = OxmlElement('w:numFmt')
                lvl.numFmt.val = 'decimal'
                abstract_num.append(lvl)

    def _has_numbering_def(self, numbering_elm, style_name):
        for num in numbering_elm.num:
            abstract_num_id = num.abstractNumId.val
            for abstract_num in numbering_elm.abstractNum:
                if abstract_num.abstractNumId == abstract_num_id:
                    for lvl in abstract_num.lvl:
                        if lvl.pPr and lvl.pPr.numPr:
                            for num_id_element in lvl.pPr.numPr.findall(qn('w:numId')):
                                if num_id_element.val == numbering_elm.get_or_create_numId(style_name):
                                    return True
        return False

    def parse_rich_text(self, html_content, original_paragraph=None):
        """Parse HTML and maintain nested structure"""
        soup = BeautifulSoup(html_content, 'html.parser')
    
        def add_numbering_to_paragraph(paragraph, is_bullet=True):
            """Add bullet or numbering to paragraph using the document's numbering definitions"""
            
            # Get the correct numId based on bullet or number
            num_id = getattr(self._doc, '_bullet_num_id', 1) if is_bullet else getattr(self._doc, '_number_num_id', 5)
            
            p = paragraph._p
            pPr = p.get_or_add_pPr()
            numPr = OxmlElement('w:numPr')
            
            # Set level to 0 (first level)
            ilvl = OxmlElement('w:ilvl')
            ilvl.set(qn('w:val'), '0')
            numPr.append(ilvl)
            
            # Set numId to the correct value
            numId = OxmlElement('w:numId')
            numId.set(qn('w:val'), str(num_id))
            numPr.append(numId)
            
            pPr.append(numPr)
        
        def process_element(element, parent_paragraph=None):
            if element.name == 'p':
                para = self._doc.add_paragraph()
                para.alignment = WD_ALIGN_PARAGRAPH.LEFT

                for child in element.children:
                    if isinstance(child, str):
                        para.add_run(child)
                    else:
                        process_element(child, para)

                return para
            
            elif element.name == 'span':
                run = None
                if parent_paragraph:
                    run = parent_paragraph.add_run(element.text)
                    font = run.font
                    if 'italic' in self.preserved_styles['span']:
                        font.italic = True
                return run
            
            elif element.name == 'h1':
                heading = self._doc.add_paragraph(style="Heading1")
                heading.alignment = WD_ALIGN_PARAGRAPH.CENTER
                heading.add_run(element.text)
                return heading
            
            elif element.name in ['ul', 'ol']:
                is_bullet = element.name == 'ul'
                
                for li in element.find_all('li'):
                    list_item = li.text.strip()
                    if list_item:
                        para = self._doc.add_paragraph(list_item, style="ListBullet" if is_bullet else "ListNumber")
                        para.alignment = WD_ALIGN_PARAGRAPH.LEFT

                        add_numbering_to_paragraph(para, is_bullet=is_bullet)
                            
                return None
            
            return None
        
        # Process all elements recursively
        for element in soup.find_all(True):
            process_element(element)

    def generate_document(self, template_path, data):
        """Generate a Word document from a template and data"""
        self._doc = self.import_missing_styles(template_path)
    
        # Merge numbering.xml
        self._doc = merge_numbering_xml(self._doc)

        paragraphs_to_replace = []
        for para in self._doc.paragraphs:
            for key, value in data.items():
                if isinstance(value, str):
                    placeholder = f'{{{{{key}}}}}'
                    if placeholder in para.text:
                        paragraphs_to_replace.append((para, value))

        for original_para, html_content in paragraphs_to_replace:
            self.parse_rich_text(html_content, original_para)
            p = original_para._element
            p.getparent().remove(p)

        return self._doc
