[tool.poetry]
name = "html-support-processor"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.10"
python-docx = "^1.1.2"
django = "^5.1.7"
beautifulsoup4 = "^4.13.3"

[tool.poetry.scripts]
html-support-processor = "html_support_processor.generate_doc:main"
dump-styles = "html_support_processor.dump_styles:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
