# Basic HTML support

The spike in this sub-project is about supporting HTML as input to replace placeholders of a `docx` template document.

The ordered and unordered lists is a rather tricky thing, the following sections explain how list are handled in `docx` documents.

## The `number.xml` file

The `number.xml` file plays a crucial role in DOCX files as part of the document's core structure. Let me explain its purpose and organization.

### Role and Purpose

The `number.xml` file serves as a fundamental component in DOCX documents, specifically within the Word document part 0:2. Its primary purpose is to store numbering definitions that control various sequential elements throughout the document, including:

- Heading styles (Heading 1, Heading 2, etc.)
- Numbered lists
- Outline levels
- Other sequential formatting elements

```mermaid
flowchart TD
    subgraph "number.xml Structure"
        direction TB
        
        N["number.xml"] --> AN["Abstract Number Definitions"]
        
        AN --> AN1["abstractNum ID='1'"]
        AN --> AN2["abstractNum ID='2'"]
        
        AN1 --> F1["• Level Format
        • Start Number
        • Number Style"]
        
        AN --> NUM["Number Definitions"]
        
        NUM --> N1["num ID='1'"]
        NUM --> N2["num ID='2'"]
        
        N1 --> A1["References abstractNum='1'"]
        N2 --> A2["References abstractNum='2'"]
        
        style N fill:#f9f,stroke:#333,color:#000
        style AN fill:#bbf,stroke:#333,color:#000
        style NUM fill:#bfb,stroke:#333,color:#000
    end
```

As shown in the diagram above, the `number.xml` file organizes numbering definitions in a hierarchical manner:

- Abstract Number Definitions (`abstractNum`) contain reusable templates for numbering styles
- Each template defines formatting properties like level format, start number, and number style
- Concrete Number Definitions (`num`) reference these templates using unique IDs
- These definitions ultimately determine how numbered elements appear in your document

### Content Structure

The XML content follows a strict hierarchical organization 0:3:

1. Root Element:
```xml
<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:numbering xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
```


2. Abstract Number Definitions (`abstractNum`):
```xml
<w:abstractNum w:abstractNumId="1">
    <w:multiLevelType/>
    <!-- Level definitions -->
    <w:levelFormat>
        <w:startNumber>1</w:startNumber>
        <w:numberStyle>decimal</w:numberStyle>
    </w:levelFormat>
</w:abstractNum>
```


3. Concrete Number Definitions (`num`):
```xml
<w:num numId="1">
    <w:abstractNumId r:id="rId1"/>
</w:num>
```

### Key Components

1. **Abstract Numbers (`abstractNum`)**:
  - Define reusable numbering templates
  - Contain level-specific formatting
  - Include properties like:
    - Number style (decimal, alpha, roman)
    - Starting number
    - Indentation settings
    - Alignment options

2. **Concrete Numbers (`num`)**:
  - Reference abstract numbers
  - Assign unique identifiers
  - Link to actual document content

3. **Level Definitions**:
  - Specify formatting for different levels
  - Control indentation and spacing
  - Define numbering patterns

### Relationship with Other Components

The `number.xml` file integrates with other DOCX components 0:2:

- References styles defined in `styles.xml`
- Used by document content in `document.xml`
- Maintains consistency across document sections

This structured organization ensures consistent numbering throughout your document, whether you're working with headings, lists, or other sequential elements. The separation of abstract and concrete numbers allows for reusable formatting templates while maintaining flexibility in application.

### Example of the formatting of a numbered list

Let me show you exactly how the `number.xml` file controls numbered list formatting in a DOCX document. Here's a practical example:

Consider this simple numbered list in a Word document:

First itemSecond itemThird itemBehind the scenes, Word creates two XML files to manage this formatting:

1. `number.xml` defines the numbering style:
```xml
<w:numbering xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
  <!-- Abstract definition -->
  <w:abstractNum w:abstractNumId="1">
    <w:multiLevelType/>
    <w:levelFormat>
      <w:startNumber>1</w:startNumber>
      <w:numberStyle>decimal</w:numberStyle>
    </w:levelFormat>
  </w:abstractNum>

  <!-- Concrete definition -->
  <w:num numId="1">
    <w:abstractNumId r:id="rId1"/>
  </w:num>
</w:numbering>
```

2. `document.xml` references this numbering style:
```xml
<w:p>First item</w:p>
<w:numPr>
  <w:numId r:id="rId1"/>
</w:numPr>

<w:p>Second item</w:p>
<w:numPr>
  <w:numId r:id="rId1"/>
</w:numPr>

<w:p>Third item</w:p>
<w:numPr>
  <w:numId r:id="rId1"/>
</w:numPr>
```

Let's visualize how these files work together:

```mermaid
flowchart TD
    subgraph "number.xml"
        AN["Abstract Number Definition
        abstractNumId='1'
        • startNumber=1
        • numberStyle=decimal"]
        
        CN["Concrete Number
        numId='1'
        references abstractNumId='1'"]
    end
    
    subgraph "document.xml"
        P1["Paragraph 1
        First item"]
        P2["Paragraph 2
        Second item"]
        P3["Paragraph 3
        Third item"]
        
        NP1["numPr
        numId='1'"]
        NP2["numPr
        numId='1'"]
        NP3["numPr
        numId='1'"]
    end
    
    CN -->|"References"| AN
    NP1 -->|"Links to"| CN
    NP2 -->|"Links to"| CN
    NP3 -->|"Links to"| CN
    
    style AN fill:#f9f,stroke:#333,color:#000
    style CN fill:#bbf,stroke:#333,color:#000
    style NP1 fill:#bfb,stroke:#333,color:#000
    style NP2 fill:#bfb,stroke:#333,color:#000
    style NP3 fill:#bfb,stroke:#333,color:#000
```

In this diagram:

- Pink boxes show the abstract definition in `number.xml` that defines the numbering style
- Blue boxes represent the concrete number definition that makes this style usable
- Green boxes show how paragraphs in `document.xml` reference this numbering style
- Arrows demonstrate how everything connects: the concrete number references the abstract definition, and each paragraph links to the concrete number

This hierarchical organization provides several practical benefits:

1. **Consistent Formatting**: All three items share the same numbering style because they reference the same concrete number (numId='1')
2. **Easy Updates**: Changing the abstract definition affects all numbered items automatically
3. **Reusability**: The same numbering style can be applied to other lists in the document

When you modify the numbering format in Word (like changing from decimal to roman numerals), you're essentially updating the abstract number definition in `number.xml`. This change then cascades down through all connected paragraphs, ensuring consistent formatting throughout your document.

This structured approach is part of the Open Packaging Conventions (OPC) XML schema 0:3, which ensures reliable document formatting across different systems and versions of Microsoft Word.

## Analysis of a `number.xml` file

```xml
<?xml version='1.0' encoding='UTF-8' standalone='yes'?>
<w:numbering xmlns:wpc="http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas"
    xmlns:mo="http://schemas.microsoft.com/office/mac/office/2008/main"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:mv="urn:schemas-microsoft-com:mac:vml" xmlns:o="urn:schemas-microsoft-com:office:office"
    xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
    xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math"
    xmlns:v="urn:schemas-microsoft-com:vml"
    xmlns:wp14="http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing"
    xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing"
    xmlns:w10="urn:schemas-microsoft-com:office:word"
    xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
    xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml"
    xmlns:wpg="http://schemas.microsoft.com/office/word/2010/wordprocessingGroup"
    xmlns:wpi="http://schemas.microsoft.com/office/word/2010/wordprocessingInk"
    xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml"
    xmlns:wps="http://schemas.microsoft.com/office/word/2010/wordprocessingShape"
    mc:Ignorable="w14 wp14">
    <w:abstractNum w:abstractNumId="0">
        <w:nsid w:val="FFFFFF7C" />
        <w:multiLevelType w:val="singleLevel" />
        <w:tmpl w:val="C310EC42" />
        <w:lvl w:ilvl="0">
            <w:start w:val="1" />
            <w:numFmt w:val="decimal" />
            <w:lvlText w:val="%1." />
            <w:lvlJc w:val="left" />
            <w:pPr>
                <w:tabs>
                    <w:tab w:val="num" w:pos="1800" />
                </w:tabs>
                <w:ind w:left="1800" w:hanging="360" />
            </w:pPr>
        </w:lvl>
    </w:abstractNum>
    <w:abstractNum w:abstractNumId="1">
        <w:nsid w:val="FFFFFF7D" />
        <w:multiLevelType w:val="singleLevel" />
        <w:tmpl w:val="E4089024" />
        <w:lvl w:ilvl="0">
            <w:start w:val="1" />
            <w:numFmt w:val="decimal" />
            <w:lvlText w:val="%1." />
            <w:lvlJc w:val="left" />
            <w:pPr>
                <w:tabs>
                    <w:tab w:val="num" w:pos="1440" />
                </w:tabs>
                <w:ind w:left="1440" w:hanging="360" />
            </w:pPr>
        </w:lvl>
    </w:abstractNum>
    <w:abstractNum w:abstractNumId="2">
        <w:nsid w:val="FFFFFF7E" />
        <w:multiLevelType w:val="singleLevel" />
        <w:tmpl w:val="FB12693A" />
        <w:lvl w:ilvl="0">
            <w:start w:val="1" />
            <w:numFmt w:val="decimal" />
            <w:pStyle w:val="ListNumber3" />
            <w:lvlText w:val="%1." />
            <w:lvlJc w:val="left" />
            <w:pPr>
                <w:tabs>
                    <w:tab w:val="num" w:pos="1080" />
                </w:tabs>
                <w:ind w:left="1080" w:hanging="360" />
            </w:pPr>
        </w:lvl>
    </w:abstractNum>
    <w:abstractNum w:abstractNumId="3">
        <w:nsid w:val="FFFFFF7F" />
        <w:multiLevelType w:val="singleLevel" />
        <w:tmpl w:val="38441652" />
        <w:lvl w:ilvl="0">
            <w:start w:val="1" />
            <w:numFmt w:val="decimal" />
            <w:pStyle w:val="ListNumber2" />
            <w:lvlText w:val="%1." />
            <w:lvlJc w:val="left" />
            <w:pPr>
                <w:tabs>
                    <w:tab w:val="num" w:pos="720" />
                </w:tabs>
                <w:ind w:left="720" w:hanging="360" />
            </w:pPr>
        </w:lvl>
    </w:abstractNum>
    <w:abstractNum w:abstractNumId="4">
        <w:nsid w:val="FFFFFF81" />
        <w:multiLevelType w:val="singleLevel" />
        <w:tmpl w:val="171AC3A4" />
        <w:lvl w:ilvl="0">
            <w:start w:val="1" />
            <w:numFmt w:val="bullet" />
            <w:lvlText w:val="" />
            <w:lvlJc w:val="left" />
            <w:pPr>
                <w:tabs>
                    <w:tab w:val="num" w:pos="1440" />
                </w:tabs>
                <w:ind w:left="1440" w:hanging="360" />
            </w:pPr>
            <w:rPr>
                <w:rFonts w:ascii="Symbol" w:hAnsi="Symbol" w:hint="default" />
            </w:rPr>
        </w:lvl>
    </w:abstractNum>
    <w:abstractNum w:abstractNumId="5">
        <w:nsid w:val="FFFFFF82" />
        <w:multiLevelType w:val="singleLevel" />
        <w:tmpl w:val="F3EAFDEC" />
        <w:lvl w:ilvl="0">
            <w:start w:val="1" />
            <w:numFmt w:val="bullet" />
            <w:pStyle w:val="ListBullet3" />
            <w:lvlText w:val="" />
            <w:lvlJc w:val="left" />
            <w:pPr>
                <w:tabs>
                    <w:tab w:val="num" w:pos="1080" />
                </w:tabs>
                <w:ind w:left="1080" w:hanging="360" />
            </w:pPr>
            <w:rPr>
                <w:rFonts w:ascii="Symbol" w:hAnsi="Symbol" w:hint="default" />
            </w:rPr>
        </w:lvl>
    </w:abstractNum>
    <w:abstractNum w:abstractNumId="6">
        <w:nsid w:val="FFFFFF83" />
        <w:multiLevelType w:val="singleLevel" />
        <w:tmpl w:val="3D1EFFD4" />
        <w:lvl w:ilvl="0">
            <w:start w:val="1" />
            <w:numFmt w:val="bullet" />
            <w:pStyle w:val="ListBullet2" />
            <w:lvlText w:val="" />
            <w:lvlJc w:val="left" />
            <w:pPr>
                <w:tabs>
                    <w:tab w:val="num" w:pos="720" />
                </w:tabs>
                <w:ind w:left="720" w:hanging="360" />
            </w:pPr>
            <w:rPr>
                <w:rFonts w:ascii="Symbol" w:hAnsi="Symbol" w:hint="default" />
            </w:rPr>
        </w:lvl>
    </w:abstractNum>
    <w:abstractNum w:abstractNumId="7">
        <w:nsid w:val="FFFFFF88" />
        <w:multiLevelType w:val="singleLevel" />
        <w:tmpl w:val="D0A62B40" />
        <w:lvl w:ilvl="0">
            <w:start w:val="1" />
            <w:numFmt w:val="decimal" />
            <w:pStyle w:val="ListNumber" />
            <w:lvlText w:val="%1." />
            <w:lvlJc w:val="left" />
            <w:pPr>
                <w:tabs>
                    <w:tab w:val="num" w:pos="360" />
                </w:tabs>
                <w:ind w:left="360" w:hanging="360" />
            </w:pPr>
        </w:lvl>
    </w:abstractNum>
    <w:abstractNum w:abstractNumId="8">
        <w:nsid w:val="FFFFFF89" />
        <w:multiLevelType w:val="singleLevel" />
        <w:tmpl w:val="29761A62" />
        <w:lvl w:ilvl="0">
            <w:start w:val="1" />
            <w:numFmt w:val="bullet" />
            <w:pStyle w:val="ListBullet" />
            <w:lvlText w:val="" />
            <w:lvlJc w:val="left" />
            <w:pPr>
                <w:tabs>
                    <w:tab w:val="num" w:pos="360" />
                </w:tabs>
                <w:ind w:left="360" w:hanging="360" />
            </w:pPr>
            <w:rPr>
                <w:rFonts w:ascii="Symbol" w:hAnsi="Symbol" w:hint="default" />
            </w:rPr>
        </w:lvl>
    </w:abstractNum>
    <w:num w:numId="1">
        <w:abstractNumId w:val="8" />
    </w:num>
    <w:num w:numId="2">
        <w:abstractNumId w:val="6" />
    </w:num>
    <w:num w:numId="3">
        <w:abstractNumId w:val="5" />
    </w:num>
    <w:num w:numId="4">
        <w:abstractNumId w:val="4" />
    </w:num>
    <w:num w:numId="5">
        <w:abstractNumId w:val="7" />
    </w:num>
    <w:num w:numId="6">
        <w:abstractNumId w:val="3" />
    </w:num>
    <w:num w:numId="7">
        <w:abstractNumId w:val="2" />
    </w:num>
    <w:num w:numId="8">
        <w:abstractNumId w:val="1" />
    </w:num>
    <w:num w:numId="9">
        <w:abstractNumId w:val="0" />
    </w:num>
</w:numbering>
```

Let me analyze this `number.xml` file and break down its structure and functionality.

### Overview

This XML file defines nine distinct numbering styles for a DOCX document, organized into two categories:

1. Decimal-numbered lists (IDs 0, 1, 2, 3, 7)
2. Bullet-point lists (IDs 4, 5, 6, 8)

Here's a visual representation of how these numbering styles are organized and referenced:
```mermaid
flowchart LR
    subgraph "Abstract Number Definitions"
        direction TB
        D["Decimal Lists
        • IDs: 0,1,2,3,7
        • Format: %1.
        • Indents: 1800,1440,1080,720,360px"]
        
        B["Bullet Lists
        • IDs: 4,5,6,8
        • Symbol: 
        • Indents: 1440,1080,720,360px"]
    end
    
    subgraph "Concrete Numbers"
        direction TB
        C1["numId=1→8
        References abstract
        definitions"]
    end
    
    subgraph "Document Usage"
        direction TB
        U["Paragraphs in
        document.xml
        Reference numIds"]
    end
    
    D --> C1
    B --> C1
    C1 --> U
    
    style D fill:#f9f,stroke:#333,color:#000
    style B fill:#9ff,stroke:#333,color:#000
    style C1 fill:#ff9,stroke:#333,color:#000
    style U fill:#f96,stroke:#333,color:#000
```




In this diagram:

- Pink boxes represent decimal-numbered list definitions
- Blue boxes show bullet-point list definitions
- Yellow represents concrete number definitions that link to abstract ones
- Orange shows how paragraphs in the document actually use these styles
- Arrows indicate how styles flow from abstract definitions to concrete usage

### Detailed Analysis

#### Decimal Number Lists

Each decimal list shares these characteristics:

```xml
<w:abstractNum w:abstractNumId="0">
    <w:multiLevelType w:val="singleLevel" />
    <w:lvl w:ilvl="0">
        <w:start w:val="1" />
        <w:numFmt w:val="decimal" />
        <w:lvlText w:val="%1." />
        <w:lvlJc w:val="left" />
    </w:lvl>
</w:abstractNum>
```

Key features:

- Single-level numbering (no nested lists)
- Left-aligned text
- Decimal format with period suffix
- Progressive indentation (from 1800px to 360px)

#### Bullet Point Lists

The bullet point lists use a similar structure but with different formatting:

```xml
<w:abstractNum w:abstractNumId="4">
    <w:multiLevelType w:val="singleLevel" />
    <w:lvl w:ilvl="0">
        <w:start w:val="1" />
        <w:numFmt w:val="bullet" />
        <w:lvlText w:val="" />
        <w:lvlJc w:val="left" />
    </w:lvl>
</w:abstractNum>
```

Notable differences:

- Uses bullet symbol (``) instead of numbers
- Same indentation pattern as decimal lists
- Special font handling for bullet symbols

### Implementation Details

The concrete number definitions (`num` elements) map to these abstract definitions:

```xml
<w:num w:numId="1"><w:abstractNumId w:val="8"/></w:num>
<w:num w:numId="2"><w:abstractNumId w:val="6"/></w:num>
<!-- ... -->
```

This mapping system allows:

- Reusable formatting templates
- Consistent styling across the document
- Easy updates to entire classes of lists
- Precise control over indentation and alignment

### Usage Impact

When creating lists in the document, paragraphs reference these number definitions:

```xml
<w:p>
    <w:numPr><w:numId r:id="rId1"/></w:numPr>
    List Item Text
</w:p>
```

This implementation follows the Open Packaging Conventions (OPC) XML schema 0:3, providing a robust foundation for document formatting that maintains consistency across different systems and versions of Microsoft Word.

## Important

Word's numbering system uses a specific hierarchy to determine which `numPr` element to use when multiple options are available. Let me explain the exact process.

### Selection Process

When Word encounters a paragraph that needs numbering, it follows this strict hierarchy:

1. **Direct Paragraph Level** 
   - First looks for a `numPr` element directly in the paragraph
   - If found, uses this as the primary source


2. **Run Properties**  
   - If no direct `numPr`, checks the paragraph's run properties
   - Each run within the paragraph can have its own `numPr`


3. **Paragraph Style** 
   - If no run-level `numPr`, checks the paragraph's style
   - Looks for `numPr` in the style definition


4. **Document Defaults**
   - If no style-level `numPr`, uses document-level defaults
   - Falls back to the first abstract number definition


Let's visualize this hierarchy:
```mermaid
flowchart TD
    subgraph "Paragraph Level"
        P["<w:p>
        <w:numPr>
        <w:numId r:id='rId1'/>
        </w:numPr>
        Text"]
    end
    
    subgraph "Run Level"
        R["<w:r>
        <w:numPr>
        <w:numId r:id='rId2'/>
        </w:numPr>
        Text"]
    end
    
    subgraph "Style Level"
        S["<w:style>
        <w:numPr>
        <w:numId r:id='rId3'/>
        </w:numPr>
        Style Properties"]
    end
    
    subgraph "Document Level"
        D["First Abstract Number
        Definition"]
    end
    
    P -->|"Uses if present"| R
    R -->|"Uses if present"| S
    S -->|"Uses if present"| D
    
    style P fill:#f96,stroke:#333,color:#000
    style R fill:#9f6,stroke:#333,color:#000
    style S fill:#69f,stroke:#333,color:#000
    style D fill:#f9f,stroke:#333,color:#000
```




In this diagram:

- Orange represents the paragraph level - the most specific and highest priority
- Green shows the run level - individual text segments within a paragraph
- Blue represents the style level - paragraph formatting templates
- Pink shows the document level - the fallback option
- Arrows indicate the fallback path Word takes when a higher-level `numPr` isn't found

### Practical Implications

This hierarchy explains several important behaviors:

1. **Overriding Defaults**  - A paragraph-level `numPr` will always override style-level settings
  - Run-level `numPr` elements override both paragraph and style settings
  - This allows for fine-grained control over numbering


2. **Style Inheritance**  - When no direct `numPr` is found, Word looks up the paragraph's style
  - This is why your bullet points worked when you removed the conflicting definition
  - The style-level `numPr` provided the necessary formatting


3. **Document-Level Fallback**  - If no higher-level `numPr` is found, Word uses the first abstract number definition
  - This explains why removing the first definition fixed your bullet point issue
  - The document-level fallback ensures consistent numbering throughout



This hierarchical system allows for both flexibility and consistency in document formatting, while maintaining a clear order of precedence for numbering definitions.