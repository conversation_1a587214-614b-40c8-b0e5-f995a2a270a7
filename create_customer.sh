docker exec cyrus-postgres-1 bash -c "psql -U postgres -d cyrus -c 'DELETE FROM customer;'"
test="INSERT INTO customer(id, name, description) VALUES (1, 'CETIC', 'As an applied research centre in the field of ICT, CETIC’s mission is to support economic development by transferring the results of the most innovative research in ICT to companies, particularly SMEs. CETIC helps companies integrate these technological breakthroughs into their products, processes and services, enabling them to innovate faster, save time and money and develop new markets. CETIC develops its expertise in key technologies, including Software engineering, Artificial Intelligence, Cybersecurity, Algorithmic and combinatorial Optimisation, Internet of Things. These innovations are applied in domains of primary importance to society, such as health, smart mobility, energy and industry. This expertise is continuously supplemented through CETIC’s active involvement in European and regional projects. CETIC is located on the Aeropole of Charleroi, in the Walloon region, Belgium.');"
docker exec cyrus-postgres-1 bash -c "psql -U postgres -d cyrus -c \"$test\""
