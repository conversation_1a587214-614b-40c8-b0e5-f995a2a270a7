/* You can add global styles to this file, and also import other style files */

@use '@angular/material' as mat;
@include mat.core();
@import "assets/scss/variables";

// Define a dark theme
$dark-theme: mat.define-theme((color: (theme-type: dark,
                primary: mat.$violet-palette,
            ),
        ));

html,
body {
    height: 100%;

    .dark-theme {
        @include mat.all-component-themes($dark-theme);
        background-color: $darkBold;
    }

    .dark-theme .mat-sidenav,
    .dark-theme .mat-drawer-content,
    .dark-theme .topbar,
    .dark-theme .theme-card,
    .dark-theme .mat-sidenav-container {
        background-color: #111111 !important;
    }

    .dark-theme .mat-sidenav {
        border-right: 1px solid rgb(34, 33, 33) !important;
    }

    .dark-theme .mdc-list-group__subheader,
    .dark-theme .mdc-list-item__primary-text,
    .dark-theme .mat-mdc-card-title,
    .dark-theme .mdc-label,
    .dark-theme .mat-subtitle-2 {
        color: $darktext !important;
    }

    .dark-theme .theme-card,
    .dark-theme .mat-mdc-dialog-surface {
        background-color: $darkLight !important;
        color: $darktext !important;
    }

    .dark-theme .pageWrapper {
        color: rgb(166, 166, 166) !important;
    }

    .dark-theme .ng-scrollbar-thumb,
    .dark-theme .section mat-card,
    .dark-theme mat-toolbar,
    .dark-theme .copyright {
        background-color: $darkLight !important;
    }

    .dark-theme #benefits {
        background-color: transparent !important;
    }

    .dark-theme .connector-label {
        fill: $darkLight;
    }

    .dark-theme td,
    .dark-theme th,
    .dark-theme tr {
        color: $darktext;
    }

    .dark-theme .topbar {
        border-bottom: 2px solid $darkLight !important;
    }

    .dark-theme {
        color: $darktext;

        .ql-editor {
            color: #000000 !important;
        }

        input,
        select,
        option,
        textarea,
        .mat-mdc-select-value-text,
        .mat-mdc-select-placeholder,
        .zoom-controls *,
        mat-label,
        .results-header h3,
        .results-header mat-icon,
        .search-container *,
        .mdc-text-field__input,
        text,
        tspan,
        .sidebar,
        mat-form-field * {
            color: #000000 !important;
        }

        td,
        tr,
        th {
            font-size: calc(1em + 1px);
        }

        .mat-mdc-table {
            background: $darkLight !important;
        }

        .component-form-container {
            background: $darkLight !important;
        }

        .component-form-container mat-label {
            color: $darktext !important;
        }

        .mat-mdc-card {
            background: $darkLight !important;
            border: 1px solid $darkBold !important;
        }

        .mdc-tab__text-label {
            color: $darktext !important;
        }

        ngx-charts-bar-vertical,
        ngx-charts-pie-chart {
            background-color: $darkBold !important;
            color: $darktext !important;
        }

        .axis path,
        .axis line {
            stroke: $darktext !important;
        }

        .dashboard-section text {
            fill: $darktext !important;
        }

        .nav-item.active-link {
            color: $primary !important;
        }

        /* Sidebar */
        .sidebarNav,
        .secret-value {
            color: $darktext;
        }

        .nav-item {
            &.active-link {
                color: $primary !important;

                .nav-icon,
                .nav-label {
                    color: $primary !important;
                }
            }
        }

        .nav-icon {
            color: $darktext;
        }

        .nav-label {
            color: $darktext;
        }

        .expand-icon {
            color: $darktext;
        }

        /* Active link styling */
        .active-link .nav-icon,
        .active-link .nav-label {
            color: $primary !important;
        }

        /* Hover and active states */
        .nav-item:hover {
            background-color: $darkBold;
        }

        .nav-item.active-link {
            color: $primary !important;
        }

        .nav-item.active-link .nav-label {
            color: $primary !important;
        }

        .mat-mdc-tab-body-wrapper .mat-mdc-tab-body-wrapper,
        .mat-mdc-tab-body-wrapper .special,
        .mat-mdc-tab-label-container {
            background: $darkBold;
            border: 1px solid $darkLight;
        }

        .cardWithShadow {
            box-shadow: none !important;
        }

    }


}

body {
    margin: 0;
    /*font-family: Roboto, "Helvetica Neue", sans-serif;*/
}

//New layout
.sidenav-header {
    padding: 16px;
    text-align: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);

    .logo {
        width: 64px;
        height: 64px;
        margin-bottom: 8px;
    }

    h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
    }

    .subtitle {
        margin: 4px 0 0;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.6);
    }
}

.nav-item-container {
    .nav-item {
        height: 48px;
        cursor: pointer;

        &:hover {
            background: rgba(0, 0, 0, 0.04);
        }
    }
}

.nav-item-content {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0px !important;
}

.nav-icon {
    margin-right: 16px;
    color: rgba(0, 0, 0, 0.54);
    flex-shrink: 0;
}

.nav-label {
    flex-grow: 1;
    margin-right: 16px;
}

.expand-icon {
    margin-left: auto;
    flex-shrink: 0;
}

.nav-children {
    .nested-item {
        padding-left: 32px;

        .nav-item-content {
            padding-left: 32px;
        }
    }

    .component-item {
        .nav-item-content {
            padding-left: 48px;
        }
    }
}

.version-item {
    background: rgba(0, 0, 0, 0.02);
}

mat-nav-list {
    padding-top: 0;
}

::ng-deep .mat-list-item-content {
    padding: 0 !important;
}

/* Updated indentation styles */
.indent-level-1 {
    padding-left: 10px;

    .nav-item-content {
        padding-left: 10px;
    }
}

.indent-level-2 {
    padding-left: 20px;

    .nav-item-content {
        padding-left: 20px;
    }
}

.indent-level-3 {
    padding-left: 30px;

    .nav-item-content {
        padding-left: 30px;
    }
}

/* Updated nav-item-content styles */
.nav-item-content {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 16px;
    height: 48px;
}

.nav-icon {
    margin-right: 16px;
    color: rgba(0, 0, 0, 0.54);
    flex-shrink: 0;
}

.nav-label {
    flex-grow: 1;
    margin-right: 16px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.87);
}

.expand-icon {
    margin-left: auto;
    flex-shrink: 0;
}

::ng-deep .mat-list-item-content {
    padding: 0 !important;
}

.nav-item {
    &:hover {
        background-color: rgba(0, 0, 0, 0.04);
    }
}

.mdc-list-item {
    padding-left: 0px !important;
    padding-right: 0px !important;
}

.active-link {
    background-color: rgba(63, 81, 181, 0.12) !important;

    .nav-icon,
    .nav-label {
        color: $primary;
    }
}

.nav-item {
    &:hover {
        background-color: rgba(0, 0, 0, 0.04);
    }
}

.nav-item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 16px;
    height: 48px;
}

.nav-item-left {
    display: flex;
    align-items: center;
    cursor: pointer;
    flex-grow: 1;
}

.expand-icon {
    cursor: pointer;
    margin-left: 8px;
}

.active-link {
    background-color: rgba(63, 81, 181, 0.12);

    .nav-icon,
    .nav-label {
        color: $primary;
    }
}

.nav-item {
    background-color: transparent !important;
    transition: background-color 0.3s ease;
}

.nav-item:hover {
    background-color: transparent !important;
}

.nav-item.active-link {
    background-color: transparent !important;
}


.nav-item:hover,
.nav-item::before,
.nav-item::after,
.nav-item-content:hover,
.nav-item-content::before,
.nav-item-content::after,
nav-item-container:hover,
.nav-item-container::after,
.nav-item-container::before,
.nav-item:hover::after,
.nav-item:hover::before {
    background-color: transparent !important;
    background: transparent !important;
}

form .action {
    display: flex !important;
    justify-content: flex-end !important;
    padding: 5px !important;
}

form .button-container {
    display: flex !important;
    gap: 5px !important;
}

.image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-top: 16px;
}

.image-list mat-card {
    width: 180px;
    border: 1px solid #ddd;
    border-radius: 8px;
    transition: box-shadow 0.3s ease-in-out;
}

.image-list mat-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.image-list mat-card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
}

.image-preview {
    width: 100%;
    height: auto;
    max-height: 140px;
    object-fit: contain;
    border-radius: 4px;
}

.image-name {
    font-size: 0.9rem;
    font-weight: 500;
    margin-top: 8px;
    text-align: center;
    word-break: break-word;
}

.image-actions {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 8px;
}

/* dashboard.component.scss */
.dashboard-container {
    padding: 0px;
    margin: 0 auto;
}

/* dashboard.component.scss */
.dashboard-container {
    display: grid;
    grid-template-columns: 70% 28.5%; // 70/30 split
    gap: 20px;
    //max-width: 1800px;
    width: 100%;
    margin: 0 auto;
    //height: calc(100vh - 40px); // Full height minus padding
}

.left-column {
    display: flex;
    flex-direction: column;
    gap: 5px;
    overflow-y: auto; // Scrollable if content is too long
}

.right-column {
    position: sticky;
    top: 20px;
    height: fit-content;
}

.top-section-section {
    margin-bottom: 10px;

    h2 {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
    }
}

.top-section-card {
    .edit-button {
        margin-left: 16px;
    }

    .company-logo {
        max-width: 150px;
        margin-bottom: 16px;
    }

    .top-section-info {
        padding: 0px;
    }

    .contact-table {
        width: 100%;
        margin-top: 16px;
    }
}

.bottom-section {
    margin-bottom: 10px;

    h2 {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
    }

    .bottom-cards {
        //display: grid;
        //grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 16px;
    }

    .bottom-card {
        position: relative;

        .close-button {
            position: absolute;
            top: 8px;
            right: 8px;
        }

        .bottom-image {
            max-width: 100%;
            height: auto;
            margin-top: 16px;
        }
    }
}

.chart-wrapper {
    height: 350px; // Reduced from 400px
    width: 100%;
    margin-bottom: 20px;

    h3 {
        margin-bottom: 16px;
        font-size: 1.2rem;
        font-weight: 500;
    }
}

::ng-deep {
    .ngx-charts {
        .grid-line-path {
            stroke: #ddd;
            stroke-width: 0.5;
        }

        .tick-mark {
            stroke-width: 0.5;
        }
    }
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .dashboard-container {
        grid-template-columns: 1fr; // Full width on mobile
        height: auto;
        gap: 2px;
    }

    .right-column {
        position: static;
        width: 100%;
    }

    .left-column {
        width: 100%;
    }
}


/* Utility classes */
.mat-card {
    margin-bottom: 16px;
}

.mat-card-title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.mat-mdc-card {
    border: 1px solid rgb(238, 237, 230);
}

///Title and buttons actions
.title-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-left: 10px;
}

td,
th {
    font-size: 0.8rem !important;
}

.mat-mdc-option {
    padding-left: 5px !important;
}

.btn-default {
    font-size: 0.8rem !important;
}

.default-image {
    border: 2px solid #007bff !important;
    background-color: rgba(0, 123, 255, 0.1);
}

.ql-editor {
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: normal;
    width: 100%;
    max-width: 100%;
    height: auto;
    max-height: none;
    display: block;
    min-height: 150px !important;
}

.loading-spinner {
    margin: 0 auto !important;
}

.description {
    white-space: pre-line;
}

.ng-image-fullscreen-view {
    z-index: 9999 !important;
    display: block !important;
}

.mat-drawer.mat-drawer-side {
    z-index: 1 !important;
}


///Content
.content-all {
    display: flex;
    flex-direction: row;
    gap: 10px;
}

.content-left {
    flex: 1;
}

.content-right {
    min-width: 200px;
    max-width: 200px;
    height: auto;
    border-radius: 2px;
}

@media (min-width: 1200px) {
    .mt-sm {
        padding-top: 40px !important;
    }
}

@media (max-width: 576px) {
    .content-all {
        display: block;
    }

    .content-left {
        min-width: 100% !important;
        max-width: 100% !important;
        flex: none;
    }

    .content-right {
        min-width: 100% !important;
        max-width: 100% !important;
    }

    .hidden-sm {
        display: none;
    }
}

.content-right img {
    width: 100%;
    height: auto;
    max-height: 140px;
    object-fit: contain;
    border-radius: 4px;
}

.mdc-tab--active {
    background-color: $primary !important;
}

.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label {
    color: white !important;
}

.cardWithShadow {
    border: 1px solid #eee !important;
}

.div-show {
    display: inline !important;
}

.div-hide {
    display: none !important;
}

//menu right
mat-toolbar {
    display: flex;
    justify-content: space-around;
    padding: 5px 10px;
    font-weight: bold;
    overflow-x: auto !important;
    overflow-y: hidden !important;
}

.toolbar-item {
    cursor: pointer;
    padding: 5px 10px;
    margin: 0 5px;
    white-space: nowrap;
    font-size: 0.9rem !important;
}

/*.toolbar-item:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 5px;
}

mat-toolbar:first-child {
    background-color: #4a00e0;
    color: white;
}

mat-toolbar:nth-child(2) {
    background-color: #64b5f6;
    color: white;
}

*/

.right {
    text-align: right !important;
    float: right !important;
}

.fixed-notes {
    position: fixed !important;
    top: 50% !important;
    right: 6px !important;
    z-index: 8 !important;
    background-color: yellow !important;
    color: #000000 !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
    border-radius: 15px !important;
}

.mat-mdc-tab-body-wrapper .mat-mdc-tab-body-wrapper,
.mat-mdc-tab-body-wrapper .special {
    padding: 10px 20px !important;
    background: #eaeaea;
    padding-right: 50px !important;
}

quill-editor,
.mdc-text-field {
    background: #ffffff !important;
    border-radius: 7px !important;
}

.mat-elevation-z8 {
    z-index: 20 !important;
}

.chart-legend .legend-labels {
    overflow-x: auto !important;
}


.light-theme .mdc-button,
.dark-theme .mdc-button {
    padding-top: 8px !important;
    padding-bottom: 8px !important;
    padding-left: 16px !important;
    padding-right: 16px !important;
}

.ng-image-slider .ng-image-slider-container .main .main-inner .img-div {
    background-image: none !important;
}

.mat-mdc-checkbox.mat-primary {
    --mdc-checkbox-disabled-selected-icon-color: #007bff !important;
    --mdc-checkbox-disabled-unselected-icon-color: #007bff !important;
}