th {
    font-weight: bold !important;
}

/* Flexbox layout for responsive table */
.responsive-table {
    width: 100%;
    overflow-x: auto;
    /* Enables horizontal scrolling on small screens */
}

.mat-mdc-form-field-hint-wrapper,
.mat-mdc-form-field-error-wrapper,
.no-pad {
    padding: 0px !important;
    margin: 0px !important;
}

/*.mdc-text-field, .mat-mdc-form-field-flex, .mat-mdc-form-field-infix{
    height: 40px !important;
    padding: 3px 3px !important;
}*/
.w-45 {
    width: 45% !important;
}

.mat-mdc-dialog-title::before {
    height: 0px !important;
}

/*.mat-mdc-dialog-title{
    border-bottom: 2px solid gray !important;
}*/
.mat-mdc-dialog-title h4 {
    margin-top: 0px !important;
    margin-bottom: 0px !important;
}

table {
    width: 100%;
    border-collapse: collapse;
    /* Ensures no spacing between table borders */
}

th,
td {
    padding: 5px 10px 0px 0px;
    text-align: left;
    vertical-align: middle;
}

.key-input,
.value-input {
    width: 100%;
    /* Ensure input fields are fully stretched within their table cells */
}

th {
    font-weight: bold;
}

.remove-button {
    display: inline-block;
    /* Ensure button doesn't stretch to fill available space */
    margin-left: 10px;
}

.mat-mdc-radio-button .mdc-radio {
    padding-left: 0px !important;
}

.mat-internal-form-field>label {
    padding-right: 5px !important;
}

.checkbox-group {
    margin-left: -10px !important;
}

.no-underline {
    text-decoration: none !important;
}

.small-marg {
    padding-left: 15px !important;
}

.mt100 {
    margin-top: 100px !important;
}


.header-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo {
    height: 40px;
    cursor: pointer;
}

.spacer {
    flex: 1;
}

.menu-container {
    display: flex;
    gap: 8px;
}

button[mat-button] {
    margin-top: 5px !important;
    font-size: 16px;
    color: white;
}

.section {
    padding: 80px 0;
}

.text-center {
    text-align: center;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.feature-card {
    height: 100%;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.copyright {
    text-align: center;
    padding: 10px;
    font-size: 14px;
    color: white;
}

a:visited {
    color: $primary;
}

a {
    text-decoration: none !important;
}

.no-gap {
    gap: 0px !important;
    margin-bottom: 0px;
}

@media (min-width: 768px) {
    .mat-mdc-dialog-container {
        min-width: 650px !important;
    }

    //solve issue of modal size in dark mode
    .cdk-overlay-pane.mat-mdc-dialog-panel {
        max-width: 950px !important;
    }

    textarea {
        min-height: 150px !important;
    }
}


.diagram-container {
    height: 90vh;
}

.toolbar {
    position: fixed;
    top: 20px;
    left: 20px;
    background: white;
    padding: 10px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

button {
    padding: 8px 16px;
    margin: 2px 6px 2px 0px;
    cursor: pointer;
    border: none;
    border-radius: 4px;
}

.node {
    cursor: move;
}

.node rect {
    fill: white;
    stroke: #333;
    stroke-width: 2px;
}

.node text {
    text-anchor: middle;
    dominant-baseline: middle;
    pointer-events: none;
}

.connector {
    stroke: #666;
    stroke-width: 2px;
    fill: none;
    cursor: pointer;
}

.connector-label {
    fill: #333;
    text-anchor: middle;
    dominant-baseline: middle;
    cursor: pointer;
}

.label-input {
    position: fixed;
    top: 50px;
    display: block;
    padding: 4px;
    border: 1px solid red;
    border-radius: 4px;
    font-family: Arial, sans-serif;
    font-size: 14px;
    transform: translate(-50%, -50%);
}

.connector-handle {
    fill: #fff;
    stroke: #666;
    stroke-width: 2px;
    cursor: pointer;
}