@use "sass:map";
@use 'variables';

$utilities: (
  'margin': (
    property: margin,
    class: m,
    values: map.merge(variables.$spacers, (auto: auto))
  ),
  'margin-x': (
    property: margin-left margin-right,
    class: m-x,
    values: map.merge(variables.$spacers, (auto: auto))
  ),
  'margin-y': (
    property: margin-top margin-bottom,
    class: m-y,
    values: map.merge(variables.$spacers, (auto: auto))
  ),
  'margin-top': (
    property: margin-top,
    class: m-t,
    values: map.merge(variables.$spacers, (auto: auto))
  ),
  
  'margin-bottom': (
    property: margin-bottom,
    class: m-b,
    values: map.merge(variables.$spacers, (auto: auto))
  ),
  
  'negative-margin': (
    property: margin,
    class: m,
    values: variables.$negative-spacers
  ),
  'negative-margin-x': (
    property: margin-left margin-right,
    class: m-x,
    values: variables.$negative-spacers
  ),
  'negative-margin-y': (
    property: margin-top margin-bottom,
    class: m-y,
    values: variables.$negative-spacers
  ),
  'negative-margin-top': (
    property: margin-top,
    class: m-t,
    values: variables.$negative-spacers
  ),
  'negative-margin-right': (
    property: margin-right,
    class: m-r,
    values: variables.$negative-spacers
  ),
  'negative-margin-bottom': (
    property: margin-bottom,
    class: m-b,
    values: variables.$negative-spacers
  ),
  'negative-margin-left': (
    property: margin-left,
    class: m-l,
    values: variables.$negative-spacers
  ),
  'padding': (
    property: padding,
    class: p,
    values: variables.$spacers
  ),
  'padding-x': (
    property: padding-left padding-right,
    class: p-x,
    values: variables.$spacers
  ),
  'padding-y': (
    property: padding-top padding-bottom,
    class: p-y,
    values: variables.$spacers
  ),
  'padding-top': (
    property: padding-top,
    class: p-t,
    values: variables.$spacers
  ),
  
  'padding-bottom': (
    property: padding-bottom,
    class: p-b,
    values: variables.$spacers
  ),
  'gap': (
    property: gap,
    class: gap,
    values: variables.$spacers
  ),
  
);

$ltr:(
  'margin-right': (
    property: margin-right,
    class: m-r,
    values: map.merge(variables.$spacers, (auto: auto))
  ),
  'margin-left': (
    property: margin-left,
    class: m-l,
    values: map.merge(variables.$spacers, (auto: auto))
  ),
  'padding-left': (
    property: padding-left,
    class: p-l,
    values: variables.$spacers
  ),
  'padding-right': (
    property: padding-right,
    class: p-r,
    values: variables.$spacers
  ),
);

$rtl:(
  'margin-right': (
    property: margin-left,
    class: m-r,
    values: map.merge(variables.$spacers, (auto: auto))
  ),
  'margin-left': (
    property: margin-right,
    class: m-l,
    values: map.merge(variables.$spacers, (auto: auto))
  ),
  'padding-left': (
    property: padding-right,
    class: p-l,
    values: variables.$spacers
  ),
  'padding-right': (
    property: padding-left,
    class: p-r,
    values: variables.$spacers
  ),
)
