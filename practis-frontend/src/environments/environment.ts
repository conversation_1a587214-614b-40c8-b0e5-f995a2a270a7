export const environment = {
  production: true,
  apiUrl: '/api',
  imagePrefixUrl: '/images',
  noderedUrl: 'http://127.0.0.1:1880/',
  gvmUrl: 'http://127.0.0.1:9392/',
  gvmInstance: '127.0.0.1',
  kiwiUrl: 'http://127.0.0.1:8000/',
  rulesReportUrl: 'http://127.0.0.1:8001/api/process-template',
  importUrl: 'http://localhost:9394/retrieve/',
  keycloakUrl: 'https://localhost:30443',
  keycloakRealm: 'test',
  keycloakClientId: 'test',
};

// for local Testing, you can copy the config in environmement.devlopment.ts and juste change the value of production to true
// put back the current config before pushing to the repository