import { Injectable } from '@angular/core';
import {
  <PERSON>ttpE<PERSON>,
  HttpHandler,
  HttpInterceptor,
  HttpRequest,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { KeycloakService } from 'keycloak-angular';
import { Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
  constructor(
    private keycloakService: KeycloakService,
    private router: Router,
    public _snackBar: MatSnackBar
  ) {}

  intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      catchError((error: HttpErrorResponse) => {
        // Handle specific status codes
        if (error.status === 401) {
          // Unauthorized: Redirect to login
          this.keycloakService.login({
            redirectUri: window.location.href,
          });
        } else if (error.status === 403) {
          // Forbidden: Navigate to an access denied page or display a message
          this.router.navigate(['/restricted']).then(() => {
            location.reload();
          });
        } /*else if (error.status >= 500) {
          // Server error: Display a generic error page
          this.router.navigate(['/unknown']);
        }*/

        // Log the error (optional)
        console.error('HTTP Error:', error);

        // Optionally, you can transform the error before rethrowing it
        //const customErrorMessage = this.getCustomErrorMessage(error);
        let errorMessages = this.extractErrors(error);

        this._snackBar.open(
          errorMessages.join('\n') || error.message,
          'Close',
          { duration: 5000 }
        );
        return throwError(
          () => new Error(errorMessages.join('\n') || error.message)
        );
      })
    );
  }

  private extractErrors(error: HttpErrorResponse): string[] {
    let messages: string[] = [];

    if (error.error) {
      if (typeof error.error === 'string') {
        if (error.error.includes('html')) {
          messages.push('Something went wrong');
        } else {
          messages.push(error.error);
        }
      } else if (typeof error.error === 'object') {
        for (const key in error.error) {
          if (Array.isArray(error.error[key])) {
            messages.push(`${key}: ${error.error[key].join(', ')}`); // Add field name + error messages
          } else if (typeof error.error[key] === 'string') {
            if (error.error[key].includes('html')) {
              messages.push('Something went wrong');
            } else {
              messages.push(`${key}: ${error.error[key]}`);
            }
          }
        }
      }
    }

    return messages.length ? messages : ['An unknown error occurred'];
  }
}
