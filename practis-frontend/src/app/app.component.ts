import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  ViewChild,
  AfterViewInit,
  Renderer2,
} from '@angular/core';
import { RouterOutlet } from '@angular/router';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit, OnDestroy, AfterViewInit {
  title = 'PRACTIS';
  constructor(private _renderer: Renderer2) {}
  private observer: MutationObserver | undefined;

  ngOnInit(): void {}

  private addLightboxObserver() {
    const body = document.body;

    this.observer = new MutationObserver(() => {
      const lightbox = document.querySelector('.ng-image-fullscreen-view');
      const overlay = document.querySelector('.component-form-overlay');
      const toolbar = document.querySelector('.mat-toolbar') as HTMLElement;

      if (toolbar) {
        if (lightbox || overlay) {
          this._renderer.setStyle(toolbar, 'z-index', '0'); // Hide toolbar
        } else {
          this._renderer.setStyle(toolbar, 'z-index', '9'); // Restore toolbar
        }
      }
    });

    // Observe body for child element changes (lightbox appearing/disappearing)
    this.observer.observe(body, { childList: true, subtree: true });
  }

  ngAfterViewInit() {
    this.addLightboxObserver();
  }

  ngOnDestroy(): void {
    if (this.observer) {
      this.observer.disconnect(); // Cleanup on destroy
    }
  }
}
