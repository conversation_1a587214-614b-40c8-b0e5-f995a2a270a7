import { MaterialModule } from 'app/material.module';
import { Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Sut, SutWithVersions } from 'app/models/sut';
import { Version } from 'app/models/version';
import { SutService } from 'app/services/sut.service';
import { VersionService } from 'app/services/version.service';
import { UtilsService } from 'app/utils/utils';
import { SutAddEditComponent } from '../sut-add-edit/sut-add-edit.component';
import { VersionAddEditComponent } from '../version-add-edit/version-add-edit.component';
import { SafeHtml } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { MenuService } from 'app/services/menu.service';

@Component({
  selector: 'app-sut-view',
  standalone: true,
  imports: [MaterialModule],
  templateUrl: './sut-view.component.html',
  styleUrl: './sut-view.component.scss',
})
export class SutViewComponent implements OnInit, OnDestroy {
  @ViewChild('tabGroupAll') tabGroupAll!: MatTabGroup;
  @ViewChild('tabGroup') tabGroup!: MatTabGroup;
  sut?: SutWithVersions;
  safeDescriptions: { [key: string]: SafeHtml } = {};
  sliderImages: { [key: string]: Array<Object> } = {};
  sutId?: string;

  constructor(
    private _dialog: MatDialog,
    private _sutService: SutService,
    private _versionService: VersionService,
    public _snackBar: MatSnackBar,
    private _utilsService: UtilsService,
    private _route: ActivatedRoute,
    private _router: Router,
    private _menuService: MenuService
  ) {}

  ngOnInit(): void {
    this._route.params.subscribe((params) => {
      this.sutId = params['sutId'];
      //console.log(this.sutId);
      this.refresh();
    });
  }

  //get sut
  refresh() {
    this._sutService.getSutById(this.sutId as string).subscribe({
      next: (res) => {
        //console.log(res);
        this.sut = res;
        this.setImagesSut();
        this.sut?.versions?.forEach((version) => {
          if (version.uuid) {
            this.safeDescriptions[version.uuid] =
              this._utilsService.getSafeDescription(version.description);
            this.sliderImages[version.uuid] = version.images
              ? this._utilsService.getSliderImages(version.images)
              : [];
          }
        });
      },
      error: console.log,
    });
  }

  setImagesSut() {
    this.safeDescriptions['sut'] = this._utilsService.getSafeDescription(
      this.sut?.description
    );
    this.sliderImages['sut'] = this.sut?.images
      ? this._utilsService.getSliderImages(this.sut.images)
      : [];
  }

  openEditSutForm(sut: Sut): void {
    const dialogRef = this._dialog.open(SutAddEditComponent, {
      data: { sut: sut, sut_provider: this.sut?.sut_provider?.uuid },
    });
    dialogRef.afterClosed().subscribe({
      next: (val) => {
        if (val) {
          this.refresh();
          this._menuService.getMenuList();
        }
      },
    });
  }

  //delate sut
  deleteSut(sut: Sut) {
    // Open a snackbar with an undo option
    const snackBarRef = this._snackBar.open(
      `Sut '${sut.name}' will be deleted`,
      'Undo',
      { duration: 3000 }
    );

    // Handle snackbar dismissal
    snackBarRef.afterDismissed().subscribe((res) => {
      if (!res.dismissedByAction) {
        // If user did not click 'Undo', proceed with deletion
        this._sutService.deleteSut(sut.uuid as string).subscribe({
          next: (response: any) => {
            this._snackBar.open('Sut deleted!', 'Done', { duration: 3000 });
            this._menuService.getMenuList();
            this._router.navigate(['/']);
          },
          error: (error) => {
            //console.error('Error deleting sut:', error);
            //Restore data on error
            //204 No content should not return message (Backend needs to correct that)
            this._snackBar.open(
              'Failed to delete sut. Please try again.',
              'Close',
              { duration: 5000 }
            );
          },
        });
      }
    });
  }

  openAddVersionForm(): void {
    const dialogRef = this._dialog.open(VersionAddEditComponent, {
      data: { sut: this.sut?.uuid, onlyNotes: false },
    });
    dialogRef.afterClosed().subscribe({
      next: (val) => {
        if (val) {
          this.refresh();
          this._menuService.getMenuList();
        }
      },
    });
  }

  openEditVersionForm(version: Version): void {
    const dialogRef = this._dialog.open(VersionAddEditComponent, {
      data: { version: version, sut: this.sut?.uuid, onlyNotes: false },
    });
    dialogRef.afterClosed().subscribe({
      next: (val) => {
        if (val) {
          this.refresh();
          this._menuService.getMenuList();
        }
      },
    });
  }

  //delete version
  deleteversion(version: Version) {
    const backup = this.sut?.versions ? [...this.sut?.versions] : []; // Create a shallow copy of the data for backup
    if (this.sut && this.sut.versions) {
      this.sut.versions = this.sut.versions.filter(
        (m) => m.uuid !== version.uuid
      ); // Remove sut from data source
    }

    // Open a snackbar with an undo option
    const snackBarRef = this._snackBar.open(
      `Version '${version.name}' will be deleted`,
      'Undo',
      { duration: 3000 }
    );

    // Handle snackbar dismissal
    snackBarRef.afterDismissed().subscribe((res) => {
      if (!res.dismissedByAction) {
        // If user did not click 'Undo', proceed with deletion
        this._versionService.deleteVersion(version.uuid as string).subscribe({
          next: (response: any) => {
            this._menuService.getMenuList();
            this._snackBar.open('Version deleted!', 'Done', { duration: 3000 });
          },
          error: (error) => {
            //console.error('Error deleting sut:', error);
            //Restore data on error
            //204 No content should not return message (Backend needs to correct that)
            if (this.sut) {
              this.sut.versions = backup;
            }
            this._snackBar.open(
              'Failed to delete version. Please try again.',
              'Close',
              { duration: 5000 }
            );
          },
        });
      } else {
        // If 'Undo' was clicked, restore the original data
        if (this.sut) {
          this.sut.versions = backup;
        }
      }
    });
  }

  duplicateVersion(version: Version) {
    const formData = new FormData();

    formData.append('uuid', JSON.stringify(version?.uuid));
    //console.log(formData);

    this._versionService.addVersion(formData).subscribe({
      next: (val: any) => {
        //console.log(val);
        this._snackBar.open('Version duplicated!', 'Done', {
          duration: 3000,
        });
        this.refresh();
        this._menuService.getMenuList();
      },
      error: (err: any) => {
        console.error(err);
      },
    });
  }

  ngOnDestroy(): void {
    this._snackBar.dismiss();
  }
}
