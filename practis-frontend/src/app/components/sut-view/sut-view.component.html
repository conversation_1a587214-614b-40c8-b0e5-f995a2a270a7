<mat-tab-group animationDuration="0ms" #tabGroupAll>
    <mat-tab label="Inputs">
        <div>
            <mat-tab-group animationDuration="0ms" #tabGroup>
                <mat-tab label="SUT">
                    <div class="content">
                        <h2>
                            <span class="text-primary">SUT Details</span>
                        </h2>
                        <div class="bottom-section">
                            <div class="bottom-cards">
                                <mat-card class="bottom-card cardWithShadow">
                                    <div class="title-buttons">
                                        <h4><a class="text-primary no-underline"
                                                [routerLink]="['/sut', sut?.uuid]">{{sut?.name}}</a>
                                        </h4>
                                        <div>
                                            <button mat-icon-button (click)="sut && openEditSutForm(sut)">
                                                <mat-icon color="primary">edit</mat-icon>
                                            </button>
                                            <button mat-icon-button (click)="sut && deleteSut(sut)">
                                                <mat-icon color="warn">delete</mat-icon>
                                            </button>
                                        </div>
                                    </div>
                                    <mat-card-content>
                                        <div class="content-all">
                                            <div class="content-left">
                                                <div *ngIf="sut?.uuid" [innerHTML]="safeDescriptions['sut']"></div>
                                            </div>
                                            <div class="content-right" *ngIf="sut && sut.images && sut.images.length>0">
                                                <ng-image-slider *ngIf="sut?.uuid" [images]="sliderImages['sut']" #nav
                                                    [manageImageRatio]="true" [imagePopup]="true"
                                                    [imageSize]="{width: 200, height: 100, space: 2}"></ng-image-slider>
                                            </div>
                                        </div>
                                    </mat-card-content>
                                </mat-card>

                            </div>
                        </div>
                        <div class="bottom-section">
                            <h2>
                                <span class="text-primary">Versions:</span>
                                <button *ngIf="sut" mat-raised-button color="primary" class="new-bottom-button"
                                    (click)="openAddVersionForm()">
                                    New Version
                                </button>
                            </h2>
                            <div class="bottom-cards">
                                <ng-container *ngIf="sut && sut.versions && sut.versions.length == 0">
                                    <mat-card class="bottom-card cardWithShadow">
                                        <mat-card-content>
                                            <div class="no-top-section">
                                                <h3>No versions found</h3>
                                            </div>
                                        </mat-card-content>
                                    </mat-card>
                                </ng-container>
                                <ng-container *ngIf="sut && sut.versions && sut.versions.length > 0">
                                    <mat-card *ngFor="let version of sut.versions" class="bottom-card cardWithShadow">
                                        <div class="title-buttons">
                                            <h4><a class="text-primary no-underline"
                                                    [routerLink]="['/sut', sut.uuid, 'version', version.uuid]">{{version.name}}</a>
                                            </h4>
                                            <div>
                                                <button mat-icon-button (click)="openEditVersionForm(version)">
                                                    <mat-icon color="primary">edit</mat-icon>
                                                </button>
                                                <button mat-icon-button (click)="duplicateVersion(version)">
                                                    <mat-icon color="accent">copy_all</mat-icon>
                                                </button>
                                                <button mat-icon-button (click)="deleteversion(version)">
                                                    <mat-icon color="warn">delete</mat-icon>
                                                </button>
                                            </div>
                                        </div>
                                        <mat-card-content>
                                            <div class="content-all">
                                                <div class="content-left">
                                                    <div *ngIf="version.uuid"
                                                        [innerHTML]="safeDescriptions[version.uuid]">
                                                    </div>
                                                    <p>Status: {{version.status}}</p>
                                                </div>
                                                <div class="content-right"
                                                    *ngIf="version.images && version.images.length > 0">
                                                    <ng-image-slider *ngIf="version.uuid"
                                                        [images]="sliderImages[version.uuid]" #nav
                                                        [manageImageRatio]="true" [imagePopup]="true"
                                                        [imageSize]="{width: 200, height: 100, space: 2}"></ng-image-slider>
                                                </div>
                                            </div>
                                        </mat-card-content>
                                    </mat-card>
                                </ng-container>
                            </div>
                        </div>

                    </div>
                </mat-tab>
                <mat-tab label="Objectives" [disabled]="true">
                    <div class="content">
                    </div>
                </mat-tab>
                <mat-tab label="Scope" [disabled]="true">
                    <div class="content">
                    </div>
                </mat-tab>
                <mat-tab label="Risk Analysis" [disabled]="true">
                    <div class="content">
                    </div>
                </mat-tab>
                <mat-tab label="Requirements" [disabled]="true">
                    <div class="content">
                    </div>
                </mat-tab>
            </mat-tab-group>
        </div>
    </mat-tab>
    <mat-tab label="Information Gathering" [disabled]="true">

    </mat-tab>
    <mat-tab label="Reconnaissance" [disabled]="true">

    </mat-tab>
    <mat-tab label="Vulnerability Assessment" [disabled]="true">

    </mat-tab>
    <mat-tab label="Tests" [disabled]="true">

    </mat-tab>
    <mat-tab label="Recommendations & Reporting" [disabled]="true">

    </mat-tab>
</mat-tab-group>