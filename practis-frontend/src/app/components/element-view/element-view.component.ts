import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON>t,
  <PERSON><PERSON>hild,
  <PERSON><PERSON><PERSON><PERSON>,
  AfterViewInit,
} from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { Vulnerability } from 'app/models/vulnerability';
import { MaterialModule } from 'app/material.module';
import { VulnerabilityAddEditComponent } from '../vulnerability-add-edit/vulnerability-add-edit.component';
import { environment } from 'environments/environment';
import { MatTabChangeEvent, MatTabGroup } from '@angular/material/tabs';
import { ActivatedRoute, Router } from '@angular/router';
import { VersionAddEditComponent } from '../version-add-edit/version-add-edit.component';
import { DiagramService } from 'app/services/diagram.service';
import { VulnerabilityService } from 'app/services/vulnerability.service';
import { VulnerabilityStatus } from 'app/models/enum-types';
import { FlowComponent } from '../flow/flow.component';
import { FlowExecutionComponent } from '../flow-execution/flow-execution.component';
import { SafeHtml } from '@angular/platform-browser';
import { UtilsService } from 'app/utils/utils';
import { DiagramBuilderComponent } from '../diagram-builder/diagram-builder.component';
import { TestMenuComponent } from '../test-menu/test-menu.component';
import { ElementAddEditComponent } from '../element-add-edit/element-add-edit.component';

@Component({
  selector: 'app-element-view',
  standalone: true,
  imports: [
    MaterialModule,
    FlowComponent,
    FlowExecutionComponent,
    DiagramBuilderComponent,
    TestMenuComponent
  ],
  templateUrl: './element-view.component.html',
  styleUrl: './element-view.component.scss',
})
export class ElementViewComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('tabGroupAll') tabGroupAll!: MatTabGroup;
  @ViewChild('tabGroup') tabGroup!: MatTabGroup;
  selectedTabAllIndex = 3;

  displayedColumns: string[] = [
    'name',
    'description',
    'value',
    'level',
    'status',
  ];
  vulnerabilitys?: Vulnerability[];
  vulnerabilitysConfirmed?: Vulnerability[];
  gvmUrl: string = environment.gvmUrl;
  importUrl?: string;
  sutId?: string;
  versionId?: string;
  componentId?: string;
  subcomponentId?: string;
  portId?: string;
  element?: any;
  element_type?: string;
  element_id?: string;
  dataInput?: any;
  diagramInput?: any;
  selectedParameterTypes?: string[];
  safeVulDescriptions: { [key: string]: SafeHtml } = {};
  safeVulRecommandations: { [key: string]: SafeHtml } = {};
  version?: any;

  constructor(
    private _dialog: MatDialog,
    private _diagramService: DiagramService,
    private _vulnerabilityService: VulnerabilityService,
    private _router: Router,
    private _route: ActivatedRoute,
    public _snackBar: MatSnackBar,
    private _utilsService: UtilsService
  ) {}

  ngOnInit(): void {
    this._route.params.subscribe((params) => {
      this.sutId = params['sutId'];
      this.versionId = params['versionId'];
      this.componentId = params['componentId'];
      this.subcomponentId = params['subcomponentId'];
      this.portId = params['portId'];
      this.diagramInput = {
        sutId: params['sutId'],
        versionId: params['versionId'],
        componentId: params['componentId'],
        subcomponentId: params['subcomponentId'],
        portId: params['portId'],
      };
      this.refresh();
    });
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.selectedTabAllIndex = 2;
    });
  }

  onTabChange(event: MatTabChangeEvent) {
    if (event.index === 0) {
      // 0 is the index of the "SUT" tab
      this._router.navigate(['/sut', this.sutId]);
    }
    // else if (event.index === 1) {
    //   // 0 is the index of the "SUT" tab
    //   this._router.navigate([
    //     '/diagram',
    //     this.sutId,
    //     'version',
    //     this.versionId,
    //   ]);
    // }
  }

  refresh(): void {
    if (this.portId) {
      this._diagramService.getPortById(this.portId).subscribe({
        next: (res) => {
          this.element = res;
          this.element_type = 'port';
          this.element_id = this.portId;
          this.importUrl =
            environment.importUrl +
            '?instance=' +
            environment.gvmInstance +
            '&sut=' +
            this.sutId +
            '&version=' +
            this.versionId +
            '&element=' +
            this.element_id;
          this.selectedParameterTypes =
            this.element?.parameters?.length > 0
              ? this.element.parameters
                  .map((p: any) => p?.parameter_type?.id)
                  .filter(Boolean) // Removes undefined/null values
              : [];
          this.version = this.element.version;
          this.dataInput = {
            selectedParameterTypes: this.selectedParameterTypes,
            sut: {
              uuid: this.element.sut?.uuid,
              name: this.element.sut?.name,
            },
            version: {
              uuid: this.element.version.uuid,
              name: this.element.version?.name,
            },
            component: {
              uuid: this.element.component.id,
              name: this.element.component?.name,
              parameters: this.element.component?.parameters,
            },
            port: {
              uuid: this.element.id,
              name: this.element?.name,
              parameters: this.element?.parameters,
            },
          };

          this.element.vulnerabilities.forEach((vulnerability: any) => {
            if (vulnerability.uuid) {
              this.safeVulDescriptions[vulnerability.uuid] =
                this._utilsService.getSafeDescription(
                  vulnerability.description
                );
              this.safeVulRecommandations[vulnerability.uuid] =
                this._utilsService.getSafeDescription(
                  vulnerability.recommandations
                );
            }
          });
          this.vulnerabilitys = [...this.element.vulnerabilities];

          this.vulnerabilitysConfirmed = this.element.vulnerabilities.filter(
            (v: any) =>
              v.status == VulnerabilityStatus.Confirmed ||
              v.status == VulnerabilityStatus.ConfirmedResolved
          );
        },
        error: console.log,
      });
    } else if (this.subcomponentId) {
      this._diagramService.getSubComponentById(this.subcomponentId).subscribe({
        next: (res) => {
          this.element = res;
          this.element_type = 'subcomponent';
          this.element_id = this.subcomponentId;
          this.importUrl =
            environment.importUrl +
            '?instance=' +
            environment.gvmInstance +
            '&sut=' +
            this.sutId +
            '&version=' +
            this.versionId +
            '&element=' +
            this.element_id;
          this.selectedParameterTypes =
            this.element?.parameters?.length > 0
              ? this.element.parameters
                  .map((p: any) => p?.parameter_type?.id)
                  .filter(Boolean)
              : [];
          this.version = this.element.version;
          this.dataInput = {
            selectedParameterTypes: this.selectedParameterTypes,
            sut: {
              uuid: this.element.sut?.uuid,
              name: this.element.sut?.name,
            },
            version: {
              uuid: this.element.version.uuid,
              name: this.element.version?.name,
            },
            component: {
              uuid: this.element.component.id,
              name: this.element.component?.name,
              parameters: this.element.component?.parameters,
            },
            subcomponent: {
              uuid: this.element.id,
              name: this.element?.name,
              parameters: this.element?.parameters,
            },
          };
          this.element.vulnerabilities.forEach((vulnerability: any) => {
            if (vulnerability.uuid) {
              this.safeVulDescriptions[vulnerability.uuid] =
                this._utilsService.getSafeDescription(
                  vulnerability.description
                );
              this.safeVulRecommandations[vulnerability.uuid] =
                this._utilsService.getSafeDescription(
                  vulnerability.recommandations
                );
            }
          });
          this.vulnerabilitys = [...this.element.vulnerabilities];

          this.vulnerabilitysConfirmed = this.element.vulnerabilities.filter(
            (v: any) =>
              v.status == VulnerabilityStatus.Confirmed ||
              v.status == VulnerabilityStatus.ConfirmedResolved
          );
        },
        error: console.log,
      });
    } else if (this.componentId) {
      this._diagramService.getComponentById(this.componentId).subscribe({
        next: (res) => {
          //console.log(res);
          this.element = res;
          this.element_type = 'component';
          this.element_id = this.componentId;
          this.importUrl =
            environment.importUrl +
            '?instance=' +
            environment.gvmInstance +
            '&sut=' +
            this.sutId +
            '&version=' +
            this.versionId +
            '&element=' +
            this.element_id;
          this.selectedParameterTypes =
            this.element?.parameters?.length > 0
              ? this.element.parameters
                  .map((p: any) => p?.parameter_type?.id)
                  .filter(Boolean)
              : [];
          this.version = this.element.version;
          this.dataInput = {
            selectedParameterTypes: this.selectedParameterTypes,
            sut: {
              uuid: this.element.sut?.uuid,
              name: this.element.sut?.name,
            },
            version: {
              uuid: this.element.version.uuid,
              name: this.element.version?.name,
            },
            component: {
              uuid: this.element.id,
              name: this.element?.name,
              parameters: this.element?.parameters,
            },
          };
          this.element.vulnerabilities.forEach((vulnerability: any) => {
            if (vulnerability.uuid) {
              this.safeVulDescriptions[vulnerability.uuid] =
                this._utilsService.getSafeDescription(
                  vulnerability.description
                );
              this.safeVulRecommandations[vulnerability.uuid] =
                this._utilsService.getSafeDescription(
                  vulnerability.recommandations
                );
            }
          });

          this.vulnerabilitys = [...this.element.vulnerabilities];

          this.vulnerabilitysConfirmed = this.element.vulnerabilities.filter(
            (v: any) =>
              v.status == VulnerabilityStatus.Confirmed ||
              v.status == VulnerabilityStatus.ConfirmedResolved
          );
        },
        error: console.log,
      });
    }
  }

  actionProcess(dataProcess: any) {
    if (dataProcess.action == 'refresh') {
      this.refresh();
    }
  }

  //delate vulnerability
  deleteVulnerability(vulnerability: Vulnerability) {
    const backup = this.vulnerabilitys ? [...this.vulnerabilitys] : []; // Create a shallow copy of the data for backup
    this.vulnerabilitys = this.vulnerabilitys?.filter(
      (m) => m.uuid !== vulnerability.uuid
    ); // Remove vulnerability from data source

    // Open a snackbar with an undo option
    const snackBarRef = this._snackBar.open(
      `Vulnerability '${vulnerability.name}' will be deleted`,
      'Undo',
      { duration: 3000 }
    );

    // Handle snackbar dismissal
    snackBarRef.afterDismissed().subscribe((res) => {
      if (!res.dismissedByAction) {
        // If user did not click 'Undo', proceed with deletion
        this._vulnerabilityService
          .deleteVulnerability(vulnerability.uuid as string)
          .subscribe({
            next: (response: any) => {
              this.refresh();
              this._snackBar.open('Vulnerability deleted!', 'Done', {
                duration: 3000,
              });
            },
            error: (error) => {
              //console.error('Error deleting vulnerability:', error);
              //Restore data on error
              //204 No content should not return message (Backend needs to correct that)
              this.vulnerabilitys = backup;
              this._snackBar.open(
                'Failed to delete vulnerability. Please try again.',
                'Close',
                { duration: 5000 }
              );
            },
          });
      } else {
        // If 'Undo' was clicked, restore the original data
        this.vulnerabilitys = backup;
      }
    });
  }

  //add form
  openAddForm() {
    const dialogRef = this._dialog.open(VulnerabilityAddEditComponent, {
      data: {
        element_type: this.element_type,
        element_id: this.element_id,
        versionId: this.versionId,
      },
    });
    dialogRef.afterClosed().subscribe({
      next: (val) => {
        if (val) {
          this.refresh();
        }
      },
    });
  }

  //edit form
  openEditForm(vulnerability: Vulnerability) {
    const dialogRef = this._dialog.open(VulnerabilityAddEditComponent, {
      data: {
        element_type: this.element_type,
        element_id: this.element_id,
        vulnerability: vulnerability,
        versionId: this.versionId,
      },
    });
    dialogRef.afterClosed().subscribe({
      next: (val) => {
        if (val) {
          this.refresh();
        }
      },
    });
  }

  openEditRecommendationsForm(vulnerability: Vulnerability) {
    const dialogRef = this._dialog.open(VulnerabilityAddEditComponent, {
      data: {
        element_type: this.element_type,
        element_id: this.element_id,
        vulnerability: vulnerability,
        versionId: this.versionId,
        onlyNotes: true,
      },
    });
    dialogRef.afterClosed().subscribe({
      next: (val) => {
        if (val) {
          this.refresh();
        }
      },
    });
  }

  openEditNotesForm(): void {
    if (!this.element) {
      console.error('No element available');
      this._snackBar.open('No element found to edit', 'Close', {
        duration: 3000,
      });
      return;
    }

    // Make sure we have the element ID
    console.log('Element:', this.element);

    if (!this.element.id) {
      console.error('Element has no ID:', this.element);
      this._snackBar.open('Cannot edit notes: Element ID missing', 'Close', {
        duration: 3000,
      });
      return;
    }

    // Pass all necessary information to the dialog
    const dialogRef = this._dialog.open(ElementAddEditComponent, {
      width: '500px',
      data: {
        element: this.element,
        elementType: this.element_type,
        elementId: this.element.id, // Explicitly pass the ID
        versionId: this.versionId, // Pass the version ID
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.refresh();
      }
    });
  }

  openEditVersionForm(version: any): void {
      const dialogRef = this._dialog.open(VersionAddEditComponent, {
        data: { version: version, sut: this.sutId, onlyNotes: true },
      });
      dialogRef.afterClosed().subscribe({
        next: (val) => {
          if (val) {
            this.refresh();
            //this._menuService.getMenuList();
          }
        },
      });
    }
  

  ngOnDestroy(): void {
    this._snackBar.dismiss();
  }
}
