<mat-tab-group animationDuration="0ms" dynamicHeight #tabGroupAll [selectedIndex]="selectedTabAllIndex"
    (selectedTabChange)="onTabChange($event)">
    <mat-tab label="Inputs">
    </mat-tab>
    <mat-tab label="Information Gathering">
        <div class="special">
            <div class="bottom-section">
                <h2>
                    <span class="text-primary">Diagram</span>
                </h2>
                <div class="bottom-cards">
                    <app-diagram-builder [diagramInput]="diagramInput"
                        (actionProcess)="actionProcess($event)"></app-diagram-builder>
                </div>
            </div>
        </div>
    </mat-tab>
    <mat-tab label="Reconnaissance">
        <div>
            <mat-tab-group animationDuration="0ms" dynamicHeight>
                <mat-tab label="Flows">
                    <div class="content">
                        <app-flow [dataInput]="dataInput" (actionProcess)="actionProcess($event)"></app-flow>
                    </div>
                </mat-tab>
                <mat-tab label="Flow Executions">
                    <div class="content">
                        <app-flow-execution [flowexecutions]="element?.flowexecutions"
                            (actionProcess)="actionProcess($event)"></app-flow-execution>
                    </div>
                </mat-tab>
            </mat-tab-group>
        </div>
    </mat-tab>
    <mat-tab label="Vulnerability Assessment">
        <div class="special">
            <div class="bottom-section">
                <h2>
                    <span class="text-primary">Vulnerabilities</span>
                    <div>
                        <a mat-raised-button color="accent" (click)="refresh()" target="_blank">Refresh
                        </a>&nbsp;&nbsp;&nbsp;
                        <a mat-raised-button color="accent" [href]="importUrl" target="_blank">Import Vulnerabilities
                        </a>&nbsp;&nbsp;&nbsp;
                        <a mat-raised-button color="accent" [href]="gvmUrl" target="_blank">Open
                            GVM</a>&nbsp;&nbsp;&nbsp;
                        <button mat-raised-button color="primary" class="new-bottom-button" (click)="openAddForm()">
                            Add Vulnerability
                        </button>
                    </div>
                </h2>

                <div class="bottom-cards">
                    <ng-container *ngIf="!vulnerabilitys?.length">
                        <mat-card class="bottom-card cardWithShadow">
                            <mat-card-content>
                                <div class="no-top-section">
                                    <h3>No Vulnerabilities found</h3>
                                </div>
                            </mat-card-content>
                        </mat-card>
                    </ng-container>
                    <ng-container *ngIf="vulnerabilitys?.length ?? 0 > 0">
                        <mat-card *ngFor="let vulnerability of vulnerabilitys" class="bottom-card cardWithShadow">
                            <div class="title-buttons">
                                <h4>{{vulnerability.name}}</h4>
                                <div>
                                    <button mat-icon-button (click)="openEditForm(vulnerability)">
                                        <mat-icon color="primary">edit</mat-icon>
                                    </button>
                                    <button mat-icon-button (click)="deleteVulnerability(vulnerability)">
                                        <mat-icon color="warn">delete</mat-icon>
                                    </button>
                                </div>
                            </div>
                            <mat-card-content>
                                <div class="content-all">
                                    <div class="content-left">
                                        <div *ngIf="vulnerability.uuid"
                                            [innerHTML]="safeVulDescriptions[vulnerability.uuid]">
                                        </div>
                                        <p><b>Attack path/Vector:</b> {{vulnerability.attack_path_or_vector}}</p>
                                        <p><b>CVE/CWE:</b> {{vulnerability.cve_cwe}}</p>
                                        <mat-label *ngIf="vulnerability.risks?.length"
                                            class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block"><b>Risks</b></mat-label>
                                        <div class="responsive-table" *ngIf="vulnerability.risks?.length">
                                            <!-- Table structure for Key and Value inputs -->

                                            <table mat-table [dataSource]="vulnerability.risks || []"
                                                class="contact-table">
                                                <!-- Name Column -->
                                                <ng-container matColumnDef="name">
                                                    <th mat-header-cell *matHeaderCellDef> Name </th>
                                                    <td mat-cell *matCellDef="let row"> {{row.name}} </td>
                                                </ng-container>

                                                <!-- Description Column -->
                                                <ng-container matColumnDef="description">
                                                    <th mat-header-cell *matHeaderCellDef> Description </th>
                                                    <td mat-cell *matCellDef="let row"> {{row.description}} </td>
                                                </ng-container>

                                                <!-- Value Column -->
                                                <ng-container matColumnDef="value">
                                                    <th mat-header-cell *matHeaderCellDef> Value </th>
                                                    <td mat-cell *matCellDef="let row"> {{row.value}} </td>
                                                </ng-container>
                                                <ng-container matColumnDef="level">
                                                    <th mat-header-cell *matHeaderCellDef> Level </th>
                                                    <td mat-cell *matCellDef="let row"> {{row.level}} </td>
                                                </ng-container>
                                                <ng-container matColumnDef="status">
                                                    <th mat-header-cell *matHeaderCellDef> Status </th>
                                                    <td mat-cell *matCellDef="let row"> {{row.status}} </td>
                                                </ng-container>

                                                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="content-right">
                                        <p><b>Status:</b> {{vulnerability.status}}</p>
                                    </div>
                                </div>
                            </mat-card-content>
                        </mat-card>
                    </ng-container>
                </div>
            </div>
        </div>
    </mat-tab>
    <mat-tab label="Tests">
        <div>
            <app-test-menu [dataInput]="dataInput" (actionProcess)="actionProcess($event)"></app-test-menu>
        </div>
    </mat-tab>
    <mat-tab label="Recommendations & Reporting">
        <div>
            <mat-tab-group animationDuration="0ms" dynamicHeight>
                <mat-tab label="Recommendations">
                    <div class="content">
                        <div class="bottom-section">
                            <h2>
                                <span class="text-primary">Confirmed Vulnerabilities</span>
                            </h2>

                            <div class="bottom-cards">
                                <ng-container *ngIf="!vulnerabilitysConfirmed?.length">
                                    <mat-card class="bottom-card cardWithShadow">
                                        <mat-card-content>
                                            <div class="no-top-section">
                                                <h3>No confirmed vulnerabilities found</h3>
                                            </div>
                                        </mat-card-content>
                                    </mat-card>
                                </ng-container>
                                <ng-container *ngIf="vulnerabilitysConfirmed?.length ?? 0 > 0">
                                    <mat-card *ngFor="let vulnerability of vulnerabilitysConfirmed"
                                        class="bottom-card cardWithShadow">
                                        <div class="title-buttons">
                                            <h4>{{vulnerability.name}}</h4>
                                            <div>
                                                <button mat-icon-button (click)="openEditForm(vulnerability)">
                                                    <mat-icon color="primary">edit</mat-icon>
                                                </button>
                                            </div>
                                        </div>
                                        <mat-card-content>
                                            <div class="content-all">
                                                <div class="content-left">
                                                    <div *ngIf="vulnerability.uuid"
                                                        [innerHTML]="safeVulDescriptions[vulnerability.uuid]">
                                                    </div>
                                                    <p><b>Attack path/Vector:</b>
                                                        {{vulnerability.attack_path_or_vector}}</p>
                                                    <p><b>CVE/CWE:</b> {{vulnerability.cve_cwe}}</p>
                                                    <p><b>Recommendations:</b></p>
                                                    <div *ngIf="vulnerability.recommendations"
                                                        [innerHTML]="vulnerability.recommendations">
                                                    </div>
                                                </div>
                                                <div class="content-right">
                                                    <p><b>Status:</b> {{vulnerability.status}}</p>
                                                </div>
                                            </div>
                                        </mat-card-content>
                                    </mat-card>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                </mat-tab>
                <mat-tab label="Reporting" [disabled]="true">
                    <div class="content">
                    </div>
                </mat-tab>
            </mat-tab-group>
        </div>
    </mat-tab>
</mat-tab-group>

<!--Need to implement later to edit notes for each component-->
<button mat-fab aria-label="Edit notes" class="fixed-notes" (click)="openEditVersionForm(version)" color="primary">
    <mat-icon>edit</mat-icon>
</button>