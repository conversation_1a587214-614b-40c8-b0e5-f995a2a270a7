<div mat-dialog-title>
    <h4>{{data ? onlyNotes? 'Update Vulnerability Recommandations': 'Update Vulnerability': 'Add Vulnerability'}} </h4>
</div>


<form [formGroup]="form" class="form" novalidate>
    <div mat-dialog-content class="content">
        <div class="row custom-row">
            <div class="col-12 col-sm-12">
                <div [className]="onlyNotes? 'div-hide': 'div-show'">
                    <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Name *</mat-label>
                    <mat-form-field appearance="outline" class="w-100" color="primary">
                        <input matInput formControlName="name" required />
                        <mat-error *ngIf="form.get('name')?.hasError('required')" class="no-pad">
                            Name is required
                        </mat-error>
                    </mat-form-field>

                    <!-- Description Field -->
                    <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Description</mat-label>
                    <mat-form-field appearance="outline" class="w-100" color="primary">
                        <textarea matInput formControlName="description"></textarea>
                    </mat-form-field>

                    <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">CVE/CWE</mat-label>
                    <mat-form-field appearance="outline" class="w-100" color="primary">
                        <input matInput formControlName="cve_cwe" />
                    </mat-form-field>

                    <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Attack path or vector</mat-label>
                    <mat-form-field appearance="outline" class="w-100" color="primary">
                        <input matInput formControlName="attack_path_or_vector" />
                    </mat-form-field>

                    <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Status</mat-label>
                    <mat-form-field appearance="outline" class="w-100" color="primary">
                        <mat-select formControlName="status">
                            @for (status of statuses; track status) {
                            <mat-option [value]="status">{{status}}</mat-option>
                            }
                        </mat-select>
                    </mat-form-field>


                    <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Risks</mat-label>
                    <mat-form-field appearance="outline" class="w-100" color="primary">
                        <mat-select formControlName="risks" multiple>
                            @for (risk of risks; track risk) {
                            <mat-option [value]="risk.uuid">{{risk.name}}</mat-option>
                            }
                        </mat-select>
                    </mat-form-field>
                </div>
                <div>
                    <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Recommendations</mat-label>
                    <mat-form-field appearance="outline" class="w-100" color="primary">
                        <textarea matInput formControlName="recommendations"></textarea>
                    </mat-form-field>
                </div>
            </div>
        </div>
    </div>
    <div mat-dialog-actions class="action">
        <div>
            <button mat-raised-button color="primary" type="submit"
                [disabled]="form.pristine || form.invalid || form.pending" (click)="onFormSubmit()">{{data ? 'Update':
                'Save'}}</button>
            <button mat-raised-button type="button" [mat-dialog-close]="false">Cancel</button><br />
        </div><br /><br />
    </div>
</form>