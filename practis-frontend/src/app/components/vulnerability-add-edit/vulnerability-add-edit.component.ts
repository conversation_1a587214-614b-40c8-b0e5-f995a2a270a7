import { Component, Inject, OnInit } from '@angular/core';
import { MaterialModule } from 'app/material.module';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { VulnerabilityService } from 'app/services/vulnerability.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Vulnerability } from 'app/models/vulnerability';
import { environment } from 'environments/environment';
import { Risk } from 'app/models/risk';
import { RiskService } from 'app/services/risk.service';
import { VulnerabilityStatus } from 'app/models/enum-types';

@Component({
  selector: 'app-vulnerability-add-edit',
  standalone: true,
  imports: [MaterialModule],
  templateUrl: './vulnerability-add-edit.component.html',
  styleUrl: './vulnerability-add-edit.component.scss',
})
export class VulnerabilityAddEditComponent implements OnInit {
  form: FormGroup;
  risks?: Risk[];
  statuses: string[] = Object.values(VulnerabilityStatus);
  element_type?: any;
  element_id?: any;
  versionId?: any;
  data?: Vulnerability;
  onlyNotes?: boolean = false;

  constructor(
    private _fb: FormBuilder,
    private _vulnerabilityService: VulnerabilityService,
    private _dialogRef: MatDialogRef<VulnerabilityAddEditComponent>,
    private _riskService: RiskService,
    @Inject(MAT_DIALOG_DATA) public all_data: any,
    public _snackBar: MatSnackBar
  ) {
    this.form = this._fb.group({
      name: ['', Validators.required],
      description: [''],
      cve_cwe: [''],
      attack_path_or_vector: [''],
      status: [VulnerabilityStatus.New, Validators.required],
      risks: [[]],
      recommendations: [''],
    });
  }

  ngOnInit(): void {
    this._riskService.getRiskList().subscribe({
      next: (res) => {
        this.risks = res;
        console.log(this.versionId);
        if (this.all_data) {
          this.data = this.all_data.vulnerability;
          this.onlyNotes = this.all_data.onlyNotes;
          // If editing, initialize the form with vulnerability data
          this.element_id = this.all_data.element_id;
          this.element_type = this.all_data?.element_type;
          this.versionId = this.all_data?.versionId;
          this.risks = res.filter((r: any) => {
            if (r.risk_analysis && r.risk_analysis.sut_version) {
              return r.risk_analysis.sut_version == this.versionId;
            } else {
              return false;
            }
          });
          if (this.data) {
            this.initializeForm(this.data);
          }
        }
      },
      error: console.log,
    });
  }

  // Populate form fields for editing an existing vulnerability
  private initializeForm(data: Vulnerability): void {
    data = new Vulnerability(data);
    this.form.patchValue({
      name: data.name,
      description: data.description,
      cve_cwe: data.cve_cwe,
      attack_path_or_vector: data.attack_path_or_vector,
      status: data.status,
      risks: data.risk_uuids,
      recommendations: data.recommendations,
    });

    // Manually update form validity after patching values to ensure button reflects the correct state
    this.markFormGroupTouchedAndDirty(this.form);
  }

  // Function to mark all controls as touched and dirty
  private markFormGroupTouchedAndDirty(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      if (control instanceof FormArray) {
        // Recursively apply to each FormGroup in the FormArray
        (control as FormArray).controls.forEach((arrayControl) => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouchedAndDirty(arrayControl);
          }
        });
      } else if (control instanceof FormGroup) {
        this.markFormGroupTouchedAndDirty(control);
      } else {
        control.markAsTouched();
        control.markAsDirty();
      }
    });
  }

  onFormSubmit() {
    if (this.form.valid) {
      const formData = this.form.value;
      if (this.element_type == 'component') {
        formData.component = this.element_id;
      } else if (this.element_type == 'subcomponent') {
        formData.subcomponent = this.element_id;
      } else if (this.element_type == 'port') {
        formData.port = this.element_id;
      }
      //console.log('Form submitted:', formData);
      if (this.data) {
        this._vulnerabilityService
          .updateVulnerability(this.data.uuid as string, formData)
          .subscribe({
            next: (val: any) => {
              this._snackBar.open('Vulnerability detail updated!', 'Done', {
                duration: 3000,
              });
              this._dialogRef.close(true);
            },
            error: (err: any) => {
              console.error(err);
            },
          });
      } else {
        this._vulnerabilityService.addVulnerability(formData).subscribe({
          next: (val: any) => {
            this._snackBar.open('Vulnerability added successfully', 'Done', {
              duration: 3000,
            });
            this._dialogRef.close(true);
          },
          error: (err: any) => {
            console.error(err);
          },
        });
      }
    } else {
      console.error('Form is invalid');
    }
  }
}
