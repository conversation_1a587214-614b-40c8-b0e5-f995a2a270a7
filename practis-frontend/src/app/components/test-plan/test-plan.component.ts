import { Component, OnInit, OnDestroy, Output, Input, EventEmitter } from '@angular/core';
import { MaterialModule } from 'app/material.module';
import { MatSnackBar } from '@angular/material/snack-bar';
import { forkJoin } from 'rxjs';
import { CdkDragDrop, transferArrayItem, DragDropModule } from '@angular/cdk/drag-drop';

import { Version } from 'app/models/version';
import { Sut } from 'app/models/sut';
import { ComponentModel } from 'app/models/component';
import { SubcomponentModel } from 'app/models/subcomponent';
import { PortModel } from 'app/models/port';
import { TestCase } from 'app/models/test-case';
import { TestPlan } from 'app/models/test-plan';
import { TestCaseService } from 'app/services/test-case.service';
import { TestPlanService } from 'app/services/test-plan.service';
import { VersionService } from 'app/services/version.service';

// Interface for TestPlan with expanded UI property
interface TestPlanWithUIData extends TestPlan {
  expanded?: boolean;
  showTestCaseSelection?: boolean;
  isNew?: boolean;
}

/**
 * Component for managing test plans and their associated test cases.
 * Provides functionality to create, view, update, and delete test plans,
 * as well as manage test case assignments with drag and drop capability.
 */
@Component({
  selector: 'app-test-plan',
  standalone: true,
  imports: [MaterialModule, DragDropModule],
  templateUrl: './test-plan.component.html',
  styleUrl: './test-plan.component.scss'
})
export class TestPlanComponent implements OnInit, OnDestroy {

  // Component state
  testPlans?: TestPlanWithUIData[];

  // Context properties
  sut?: Sut;
  version?: Version;
  component?: ComponentModel;
  subcomponent?: SubcomponentModel;
  port?: PortModel;
  private _dataInput?: any;

  // Test case management
  availableTestCases: TestCase[] = [];
  filteredAvailableTestCases: TestCase[] = [];
  selectedTestCases: TestCase[] = [];
  selectedTestCase?: TestCase;
  currentTestPlan?: TestPlan;

  // Search functionality
  searchTerm: string = '';
  isSearching: boolean = false;
  allSelected: boolean = false;

  // Output events
  @Output() public actionProcess: EventEmitter<any> = new EventEmitter<any>();

  // Obtain the data input (sut, version, component, subcomponent, port)
  get dataInput(): any | undefined {
    return this._dataInput;
  }

  @Input() set dataInput(value: any | undefined) {
    this._dataInput = value;

    if (value) {
      this.sut = value.sut ? value.sut : null;
      let versionId: string | undefined;

      // Get the version from different sources
      if (value.version) {
        this.version = value.version;
        this.refresh();
      }
      // Check if there is a component
      else if (value.component) {

        // Get the version from the component
        if (value.component.version) {
          this.version = value.component.version;
          this.refresh();
        } else if (value.component.version_id) {
          this._versionService.getVersionById(value.component.version_id).subscribe({
            next: (versionData) => {
              this.version = versionData;
              this.refresh();
            },
            error: (err) => {
              console.error('Error fetching version details:', err);
            }
          });
        }
      }
      // Check if there is a subcomponent
      else if (value.subcomponent) {

        // Get the version from the subcomponent
        if (value.subcomponent.version) {
          this.version = value.subcomponent.version;
          this.refresh();
        }
        else if (value.subcomponent.component && value.subcomponent.component.version) {
          this.version = value.subcomponent.component.version;
          this.refresh();
        }
        else if (value.subcomponent.component && value.subcomponent.component.version_id) {
          this._versionService.getVersionById(value.subcomponent.component.version_id).subscribe({
            next: (versionData) => {
              this.version = versionData;
              this.refresh();
            },
            error: (err) => {
              console.error('Error fetching version details:', err);
            }
          });
        }
      }
      // Check if there is a port
      else if (value.port && value.port.subcomponent && value.port.subcomponent.component) {

        // Get the version from the port
        if (value.port.subcomponent.component.version) {
          this.version = value.port.subcomponent.component.version;
          this.refresh();
        }
        else if (value.port.subcomponent.component.version_id) {
          this._versionService.getVersionById(value.port.subcomponent.component.version_id).subscribe({
            next: (versionData) => {
              this.version = versionData;
              this.refresh();
            },
            error: (err) => {
              console.error('Error fetching version details:', err);
            }
          });
        }
      }

      // Set the component, subcomponent, and port
      this.component = value.component ? value.component : null;
      this.subcomponent = value.subcomponent ? value.subcomponent : null;
      this.port = value.port ? value.port : null;

    }
  }

  constructor(
    private _testPlanService: TestPlanService,
    private _snackBar: MatSnackBar,
    private _versionService: VersionService,
    private _testCaseService: TestCaseService
  ) {}

  /**
   * Initializes the component and loads initial test plans
   */
  ngOnInit(): void {
    this.refresh();
  }

  /**
   * Loads test plans from the backend service
   */
  refresh() {
    forkJoin({
      testPlans: this._testPlanService.getTestPlanList(),
    }).subscribe({
      next: (results) => {
        this.testPlans = results.testPlans.map(testPlan => new TestPlan(testPlan)) as TestPlanWithUIData[];
      },
      error: (err) => {
        console.error('Error loading data:', err);
        this._snackBar.open('Error loading test plans', 'Close', {
          duration: 3000
        });
      }
    });
  }

  /**
   * Creates a new test plan with default values
   * and automatically opens it for editing
   */
  createNewTestPlan() {
    // Check if there's already an unsaved test plan
    const hasUnsavedTestPlan = this.testPlans?.some(tp => tp.isNew === true);
    if (hasUnsavedTestPlan) {
      this._snackBar.open('Please save or cancel the current test plan before adding a new one', 'Close', {
        duration: 4000
      });
      return;
    }

    // Create new test plan with default values
    const newTestPlan = new TestPlan({
      name: 'Unnamed',
      description: '',
      version: this.version,
    });

    // Check if test plans array is initialized
    if (!this.testPlans) {
      this.testPlans = [];
    }

    // Add custom properties for UI
    const newTestPlanWithUI = {
      ...newTestPlan,
      isNew: true,
      expanded: true
    } as TestPlanWithUIData;

    // Add the new test plan to the beginning of the array
    this.testPlans.unshift(newTestPlanWithUI);
  }

  /**
   * Saves a test plan - either creates a new one or updates an existing one
   * If test cases are being managed, saves those assignments first
   *
   * @param testPlan The test plan to save
   */
  saveTestPlan(testPlan: TestPlan) {
    // First check if we have a valid version
    if (!this.version || !this.version.uuid) {
      this._snackBar.open('Cannot create test plan: No valid version selected', 'Close', {
        duration: 4000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    // Validate the test plan
    const validationResult = this.validateTestPlan(testPlan);
    if (!validationResult.isValid && validationResult.error) {
      this._snackBar.open(validationResult.error, 'Close', {
        duration: 4000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    // Check if we are currently managing test cases for this test plan
    const isCurrentlyManagingTestCases = this.currentTestPlan === testPlan && (testPlan as TestPlanWithUIData).showTestCaseSelection;

    // If we are managing test cases and the test plan has a UUID, save the test case assignments first
    if (isCurrentlyManagingTestCases && testPlan.uuid) {
      this.saveTestCaseAssignments(testPlan, () => {
        this.completeTestPlanSave(testPlan);
      });
    } else {
      this.completeTestPlanSave(testPlan);
    }
  }

  /**
   * Completes the test plan save process after test case assignments are saved
   *
   * @param testPlan The test plan to save
   * @private
   */
  private completeTestPlanSave(testPlan: TestPlan) {
    // Extract and prepare test case UUIDs if available
    let testCaseUuids: string[] = [];

    // If the test plan is currently being managed, use the selected test cases
    if (this.currentTestPlan === testPlan && this.selectedTestCases) {
      console.log('Using currently selected test cases:', this.selectedTestCases);
      testCaseUuids = this.selectedTestCases
        .filter(tc => tc.uuid && typeof tc.uuid === 'string')
        .map(tc => tc.uuid as string);
    }
    // If the test plan has existing test cases, use them
    else if (testPlan.test_cases && Array.isArray(testPlan.test_cases)) {
      console.log('Using test plan existing test cases:', testPlan.test_cases);
      testCaseUuids = testPlan.test_cases
        .map(tc => (typeof tc === 'string' ? tc : tc?.uuid))
        .filter((id): id is string => typeof id === 'string' && id.length > 0);
    }

    // Check if the test plan is new or existing with uuid
    if (!testPlan.uuid) {
      const createData: any = {
        name: testPlan.name,
        description: testPlan.description,
        version: this.version?.uuid,
        attachments: [],
        test_cases: testCaseUuids
      };

      // Create a new test plan
      this._testPlanService.addTestPlan(createData).subscribe({
        next: (res) => {
          const newTestPlan = new TestPlan({
            uuid: res.uuid,
            name: testPlan.name,
            description: testPlan.description,
            version: {
              uuid: this.version!.uuid,
              name: this.version!.name,
              description: this.version!.description,
              status: this.version!.status
            },
            attachments: [],
            test_cases: testCaseUuids.length > 0 ? this.selectedTestCases : []
          });

          // Update the local test plans array
          if (this.testPlans) {
            const index = this.testPlans.findIndex(tp => tp === testPlan);
            if (index !== -1) {
              this.testPlans[index] = newTestPlan as TestPlanWithUIData;
            }
          }

          // Hide test case selection if it was open
          if (this.currentTestPlan === testPlan) {
            const testPlanWithUI = testPlan as TestPlanWithUIData;
            testPlanWithUI.showTestCaseSelection = false;
            this.selectedTestCase = undefined;
          }

          // Clear the isNew flag
          (testPlan as TestPlanWithUIData).isNew = false;

          this._snackBar.open('Test plan created successfully', 'Close', {
            duration: 3000
          });

          this.refresh();
        },
        error: (err) => {
          console.error('Error creating test plan:', err);
          this._snackBar.open(`Error creating test plan: ${err.error ? JSON.stringify(err.error) : err.message || 'Unknown error'}`, 'Close', {
            duration: 3000
          });
        }
      });
    } else {
      // Update existing test plan
      try {
        const updateData: any = {
          name: testPlan.name,
          description: testPlan.description || '',
          version: this.version?.uuid,
          attachments: [],
          test_cases: testCaseUuids
        };

        console.log('Updating test plan with data:', updateData);

        const testPlanId = testPlan.uuid;
        if (!testPlanId) {
          throw new Error('Invalid Test Plan ID for update.');
        }

        this._testPlanService.updateTestPlan(testPlanId, updateData).subscribe({
          next: (res) => {
            // Hide test case selection if it was open
            if (this.currentTestPlan === testPlan) {
              const testPlanWithUI = testPlan as TestPlanWithUIData;
              testPlanWithUI.showTestCaseSelection = false;
              this.selectedTestCase = undefined;
            }

            // Clear the isNew flag
            (testPlan as TestPlanWithUIData).isNew = false;

            this._snackBar.open('Test plan updated successfully', 'Close', {
              duration: 3000
            });
            this.refresh();
          },
          error: (err) => {
            console.error('Error updating test plan:', err);
            let errorMsg = 'Unknown error';
            if (err.error) {
              errorMsg = typeof err.error === 'object' ? JSON.stringify(err.error) : err.error;
            } else if (err.message) {
              errorMsg = err.message;
            }
            this._snackBar.open(`Error updating test plan: ${errorMsg}`, 'Close', {
              duration: 5000,
              panelClass: ['error-snackbar']
            });
          }
        });
      } catch (error: any) {
        console.error('Error preparing test plan update:', error);
      }
    }
  }

  /**
   * Saves test case assignments to a test plan
   *
   * @param testPlan The test plan to update with test case assignments
   * @param callback Optional callback function to execute after successful save
   */
  saveTestCaseAssignments(testPlan: TestPlan = this.currentTestPlan!, callback?: () => void) {
    if (!testPlan || !testPlan.uuid) {
      this._snackBar.open('Cannot save test cases: No valid test plan selected', 'Close', {
        duration: 3000
      });
      return;
    }

    // Filter out any test cases without valid UUIDs and extract just the UUIDs
    const validTestCaseUuids = this.selectedTestCases
      .filter(tc => tc.uuid && typeof tc.uuid === 'string')
      .map(tc => tc.uuid as string);

    console.log('Valid Test Case UUIDs to send:', validTestCaseUuids);

    // Prepare data to send
    const updateData: any = {
      name: testPlan.name,
      description: testPlan.description || '',
      version: this.version?.uuid,
      attachments: { "files": [] },
      test_cases: validTestCaseUuids // Use the filtered array of UUIDs
    };

    // Update the test plan
    this._testPlanService.updateTestPlan(testPlan.uuid, updateData).subscribe({
      next: (res) => {
        // Update the local test plan
        if (testPlan) {
          testPlan.test_cases = [...this.selectedTestCases];
        }

        this._snackBar.open('Test cases assigned successfully', 'Close', {
          duration: 3000
        });

        // Call the callback if provided
        if (callback) {
          callback();
        }
      },
      error: (err) => {
        console.error('Error assigning test cases:', err);
        let errorMsg = 'Unknown error';
        if (err.error) {
          errorMsg = typeof err.error === 'object' ? JSON.stringify(err.error) : err.error;
        } else if (err.message) {
          errorMsg = err.message;
        }
        this._snackBar.open(`Error assigning test cases: ${errorMsg}`, 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  // Delete a test plan
  deleteTestPlan(testPlan: TestPlan) {
    if (confirm('Are you sure you want to delete this test plan ?')) {
      if (testPlan.uuid) {
        this._testPlanService.deleteTestPlan(testPlan.uuid).subscribe({
          next: () => {
            if (this.testPlans) {
              this.testPlans = this.testPlans.filter(tp => tp.uuid !== testPlan.uuid);
            }

            this._snackBar.open('Test plan deleted successfully', 'Close', {
              duration: 3000
            });
          },
          error: (err) => {
            console.error('Error deleting test plan:', err);
            this._snackBar.open(`Error deleting test plan: ${err.message || 'Unknown error'}`, 'Close', {
              duration: 3000
            });
          }
        });
      } else {
        // For new test plans (without UUID)
        if (this.testPlans) {
          this.testPlans = this.testPlans.filter(tp => tp !== testPlan);
        }

        // No need to reset isNew flag as we're removing the test plan

        this._snackBar.open('Test plan removed', 'Close', {
          duration: 3000
        });
      }
    }
  }

  // Toggle expanded property for a test plan
  toggleExpanded(testPlan: TestPlanWithUIData) {
    testPlan.expanded = !testPlan.expanded;
  }

  // Cancel update and restore original or remove if new
  cancelUpdate(testPlan: TestPlan) {
    // Close the test case selection if it was open
    if (this.currentTestPlan === testPlan) {
      const testPlanWithUI = testPlan as TestPlanWithUIData;
      testPlanWithUI.showTestCaseSelection = false;
      this.selectedTestCase = undefined;
    }

    // If the test plan is new and has no UUID, remove it from the list
    if (!testPlan.uuid) {
      if (this.testPlans) {
        this.testPlans = this.testPlans.filter(tp => tp !== testPlan);
      }

      // No need to reset isNew flag as we're removing the test plan

      this._snackBar.open('Test plan cancelled', 'Close', {
        duration: 3000
      });
    } else {
      this.refresh();
    }
  }

  // Validate test plan data
  validateTestPlan(testPlan: TestPlan): { isValid: boolean; error: string | null } {
    if (!testPlan.name || testPlan.name.trim() === '') {
      return { isValid: false, error: 'Test Plan Name is required' };
    }

    if (!testPlan.description || testPlan.description.trim() === '') {
      return { isValid: false, error: 'Description is required' };
    }

    return { isValid: true, error: null };
  }

  // Get filtered test plans based on the selected version
  getFilteredTestPlans(): TestPlanWithUIData[] {
    if (!this.testPlans) {
      return [];
    }

    // If no version is selected, return all test plans
    if (!this.version || !this.version.uuid) {
      return this.testPlans as TestPlanWithUIData[];
    }

    return this.testPlans.filter(testPlan => {
      if (!testPlan.uuid) {
        return true;
      }

      if (testPlan.version) {
        if (typeof testPlan.version === 'string') {
          return testPlan.version === this.version?.uuid;
        } else if (testPlan.version.uuid) {
          return testPlan.version.uuid === this.version?.uuid;
        }
      }

      return false;
    }) as TestPlanWithUIData[];
  }

  // Open the test case selector for a test plan
  openTestCaseSelector(testPlan: TestPlanWithUIData) {
    this.currentTestPlan = testPlan;
    testPlan.showTestCaseSelection = true;

    // Clear previous selections
    this.selectedTestCases = [];
    this.availableTestCases = [];
    this.filteredAvailableTestCases = [];
    this.searchTerm = '';
    this.isSearching = false;

    // Load available test cases
    this.loadTestCases();
  }

  // Load all available test cases
  loadTestCases() {
    if (!this.currentTestPlan) {
      console.error("Cannot load test cases: No current test plan selected.");
      this._snackBar.open('Cannot load test cases: No test plan selected.', 'Close', { duration: 3000 });
      return;
    }

    // Initialize arrays
    this.selectedTestCases = [];
    this.availableTestCases = [];
    this.filteredAvailableTestCases = [];

    // If the test plan is new, we need to fetch all test cases
    if (!this.currentTestPlan.uuid) {
      this._testCaseService.getTestCaseList().subscribe({
        next: (allTestCases) => {

          // All test cases should have valid UUIDs
          this.availableTestCases = allTestCases.filter(tc => tc.uuid && typeof tc.uuid === 'string');
          this.filteredAvailableTestCases = [...this.availableTestCases];

          // If there is a search term, filter the test cases
          if (this.searchTerm && this.searchTerm.trim() !== '') {
            this.searchTestCases();
          } else {
            this.isSearching = false;
          }

          this.allSelected = false; // Reset select all state
        },
        error: (err) => {
          console.error('Error loading test cases:', err);
          this._snackBar.open('Error loading test cases', 'Close', { duration: 3000 });
        }
      });
      return;
    }

    // If the test plan is existing, we need to fetch the test plan details and then load the test cases
    const testPlanId = this.currentTestPlan.uuid;

    // Fetch the most up-to-date test plan details first
    this._testPlanService.getTestPlanById(testPlanId).subscribe({
      next: (updatedTestPlan) => {
        console.log('Fetched updated test plan:', updatedTestPlan);

        let assignedTestCaseIds: string[] = [];

        // Check if test cases are present in the response
        if (updatedTestPlan.test_cases) {
          const testCasesData = updatedTestPlan.test_cases;
          console.log('Test cases data from API:', testCasesData);

          if (Array.isArray(testCasesData)) {
            console.log('First test case structure:', JSON.stringify(testCasesData[0]));

            // Extract UUIDs based on possible structures
            assignedTestCaseIds = testCasesData.map(tc => {
              console.log('Test case item:', tc);

              if (typeof tc === 'string') {
                return tc;
              } else if (tc && typeof tc === 'object') {
                const uuid = tc.uuid || '';
                console.log('Extracted UUID:', uuid);
                return uuid;
              }
              return '';
            }).filter(id => typeof id === 'string' && id.length > 0);
          }
        } else if (this.currentTestPlan && this.currentTestPlan.test_cases) {
          console.log('Using current test plan test cases:', this.currentTestPlan.test_cases);
          assignedTestCaseIds = this.currentTestPlan.test_cases
            .map(tc => (typeof tc === 'string' ? tc : tc?.uuid))
            .filter((id): id is string => typeof id === 'string' && id.length > 0);
        } else {
          assignedTestCaseIds = [];
        }

        // Now fetch all test cases from the service
        this._testCaseService.getTestCaseList().subscribe({
          next: (allTestCases) => {
            // Ensure all test cases have valid UUIDs before filtering
            const validTestCases = allTestCases.filter(tc => tc.uuid && typeof tc.uuid === 'string');

            // Filter into selected and available based on assigned IDs
            this.selectedTestCases = validTestCases.filter(tc =>
              assignedTestCaseIds.includes(tc.uuid!) // Use non-null assertion as we filtered for valid UUIDs
            );

            this.availableTestCases = validTestCases.filter(tc =>
              !assignedTestCaseIds.includes(tc.uuid!) // Use non-null assertion
            );

            // Initialize the filtered list for display (respecting search term)
            this.filteredAvailableTestCases = [...this.availableTestCases];
            if (this.searchTerm && this.searchTerm.trim() !== '') {
              this.searchTestCases(); // Re-apply search filter if needed
            } else {
              this.isSearching = false; // Ensure search state is reset if no term
            }
            // Update the 'Select All' button state
            this.allSelected = this.availableTestCases.length === 0 && this.selectedTestCases.length > 0;

          },
          error: (err) => {
            console.error('Error loading test cases:', err);
            this._snackBar.open('Error loading test cases', 'Close', { duration: 3000 });
          }
        });
      },
      error: (err) => {
        console.error(`Error fetching updated test plan ${testPlanId}:`, err);
        this._snackBar.open('Error fetching test plan details. Cannot load test cases accurately.', 'Close', { duration: 5000, panelClass: ['error-snackbar'] });
        // Clear lists to avoid displaying incorrect data
        this.selectedTestCases = [];
        this.availableTestCases = [];
        this.filteredAvailableTestCases = [];
      }
    });
  }

  // Search test cases based on the search term
  searchTestCases() {
    this.isSearching = true;

    if (!this.searchTerm || this.searchTerm.trim() === '') {
      this.filteredAvailableTestCases = [...this.availableTestCases];
      this.isSearching = false;
      return;
    }

    const searchTermLower = this.searchTerm.toLowerCase().trim();

    // Filter test cases by name, category or description
    this.filteredAvailableTestCases = this.availableTestCases.filter(tc =>
      (tc.name?.toLowerCase().includes(searchTermLower)) ||
      (tc.category?.toLowerCase().includes(searchTermLower)) ||
      (tc.description?.toLowerCase().includes(searchTermLower))
    );

    // Notify user about search results
    const results = this.filteredAvailableTestCases.length;
    this._snackBar.open(`Found ${results} matching test case${results !== 1 ? 's' : ''}`, 'Close', {
      duration: 3000
    });
  }

  // Clear search
  clearSearch() {
    this.searchTerm = '';
    this.filteredAvailableTestCases = [...this.availableTestCases];
    this.isSearching = false;
  }

  // Handle drop of an item between the two lists
  onDrop(event: CdkDragDrop<TestCase[]>) {
    if (event.previousContainer !== event.container) {
      // Transfer the item between containers
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );

      // If item was moved from available to selected, also update the source array
      if (event.previousContainer.id === 'availableList') {
        this.availableTestCases = this.availableTestCases.filter(tc =>
          !this.selectedTestCases.some(stc => stc.uuid === tc.uuid)
        );
        this.filteredAvailableTestCases = this.filteredAvailableTestCases.filter(tc =>
          !this.selectedTestCases.some(stc => stc.uuid === tc.uuid)
        );
      }
      // If item was moved from selected to available, reapply search filter
      else if (event.previousContainer.id === 'selectedList' && this.searchTerm.trim() !== '') {
        this.searchTestCases();
      }
    }
  }

  // Display details of a test case
  showTestCaseDetails(testCase: TestCase) {
    this.selectedTestCase = testCase;
  }

  // Method to select/deselect all test cases
  toggleSelectAll() {
    if (!this.allSelected) {
      // Select all test cases - use filtered cases if searching
      const casesToSelect = this.isSearching ?
        this.filteredAvailableTestCases :
        this.availableTestCases;

      if (casesToSelect.length > 0) {
        // Add all available/filtered test cases to the selected list
        this.selectedTestCases = [...this.selectedTestCases, ...casesToSelect];

        // Update available and filtered available lists
        this.availableTestCases = this.availableTestCases.filter(tc =>
          !this.selectedTestCases.some(stc => stc.uuid === tc.uuid)
        );
        this.filteredAvailableTestCases = [];

        this.allSelected = true;

        this._snackBar.open(`${casesToSelect.length} test case${casesToSelect.length !== 1 ? 's' : ''} selected`, 'Close', {
          duration: 3000
        });
      }
    } else {
      // Deselect all test cases
      if (this.selectedTestCases.length > 0) {
        // Move all selected test cases back to the available list
        this.availableTestCases = [...this.selectedTestCases, ...this.availableTestCases];

        // Update filtered list if searching
        if (this.isSearching) {
          this.searchTestCases();
        } else {
          this.filteredAvailableTestCases = [...this.availableTestCases];
        }

        // Clear the selected list
        this.selectedTestCases = [];
        this.allSelected = false;

        this._snackBar.open(`All test cases unselected`, 'Close', {
          duration: 3000
        });
      }
    }
  }

  /**
   * Handles cleanup on component destruction
   */
  ngOnDestroy(): void {
    this._snackBar.dismiss();
  }
}