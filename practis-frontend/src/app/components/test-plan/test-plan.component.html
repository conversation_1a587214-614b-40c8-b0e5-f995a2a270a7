<div class="content">
  <div class="bottom-section">
    <!-- Header section with title and version badge -->
    <div class="header-section d-flex justify-content-between align-items-center mb-4">
      <h2>
        <span class="text-primary">Test Plans</span>
      </h2>

      <div class="d-flex align-items-center">
        <span *ngIf="version?.name" class="version-badge me-4">
          Version: {{version?.name}}
        </span>

        <button mat-raised-button color="primary" (click)="createNewTestPlan()">
          <mat-icon>add_circle</mat-icon> Add New Test Plan
        </button>
      </div>
    </div>

    <!-- Test plans list section -->
    <div class="test-plans-section mb-4">
      <ng-container *ngIf="!getFilteredTestPlans().length">
        <mat-card class="bottom-card cardWithShadow">
          <mat-card-content>
            <div class="no-top-section">
              <h3>No test plans found for the selected version</h3>
            </div>
          </mat-card-content>
        </mat-card>
      </ng-container>

      <ng-container *ngIf="getFilteredTestPlans().length">
        <mat-accordion>
          <mat-expansion-panel *ngFor="let testPlan of getFilteredTestPlans()" [expanded]="testPlan.expanded">
            <!-- Test plan header -->
            <mat-expansion-panel-header>
              <mat-panel-title>
                {{testPlan.name || 'Unnamed'}}
              </mat-panel-title>
              <mat-panel-description>
                <div class="d-flex justify-content-end w-100">
                  <ng-container *ngIf="testPlan.uuid">
                    <button mat-icon-button color="warn"
                            (click)="$event.stopPropagation(); deleteTestPlan(testPlan)"
                            matTooltip="Delete Test Plan">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </ng-container>
                  <ng-container *ngIf="!testPlan.uuid">
                    <span class="temporary-indicator" matTooltip="New test plan - not saved yet">
                      <mat-icon color="accent">fiber_new</mat-icon>
                    </span>
                  </ng-container>
                </div>
              </mat-panel-description>
            </mat-expansion-panel-header>

            <!-- Test plan details -->
            <div class="test-plan-details">
              <div class="row custom-row">
                <div class="col-md-12">
                  <!-- Name and Manage Test Cases fields -->
                  <div class="form-row d-flex mb-4">
                    <div class="col-md-6 pe-3">
                      <div class="field-label">Name</div>
                      <mat-form-field appearance="outline" class="w-100" color="primary">
                        <input matInput [(ngModel)]="testPlan.name" placeholder="Name" required>
                      </mat-form-field>
                    </div>
                    <div class="col-md-6 ps-3">
                      <div class="field-label text-center">Manage Test Cases</div>
                      <button mat-raised-button color="primary" class="w-100" (click)="openTestCaseSelector(testPlan)">
                        <mat-icon>assignment</mat-icon> Manage Test Cases
                      </button>
                    </div>
                  </div>

                  <!-- Description field -->
                  <div class="form-group">
                    <div class="field-label">Description</div>
                    <mat-form-field appearance="outline" class="w-100" color="primary">
                      <textarea matInput rows="9" [(ngModel)]="testPlan.description" placeholder="Description"></textarea>
                    </mat-form-field>
                  </div>
                </div>
              </div>

              <!-- Test case selection UI -->
              <div class="test-case-selection" *ngIf="testPlan.showTestCaseSelection">
                <h3 class="mb-4 text-center">Manage Test Cases for {{ testPlan.name }}</h3>

                <!-- Search bar -->
                <div class="row justify-content-center mb-4">
                  <div class="col-md-10">
                    <div class="search-container">
                      <mat-form-field appearance="outline" class="search-field w-100">
                        <mat-label>Search test cases</mat-label>
                        <input matInput [(ngModel)]="searchTerm" placeholder="Search by name, category, or description"
                               (keyup.enter)="searchTestCases()">
                        <button *ngIf="searchTerm" matSuffix mat-icon-button aria-label="Clear" (click)="clearSearch()">
                          <mat-icon>close</mat-icon>
                        </button>
                        <button matSuffix mat-icon-button aria-label="Search" (click)="searchTestCases()">
                          <mat-icon>search</mat-icon>
                        </button>
                      </mat-form-field>
                    </div>

                    <div *ngIf="isSearching" class="search-results-info mb-3">
                      <mat-chip-set>
                        <mat-chip color="primary" selected>
                          Search results for: "{{searchTerm}}"
                          <mat-icon matChipRemove (click)="clearSearch()">cancel</mat-icon>
                        </mat-chip>
                      </mat-chip-set>
                    </div>
                  </div>
                </div>

                <!-- Test cases drag & drop columns -->
                <div class="row justify-content-center">
                  <!-- Available test cases -->
                  <div class="col-md-5 mb-4 mx-2">
                    <div class="test-case-column">
                      <h4 class="title-with-button">
                        <span class="title-text">
                          <mat-icon class="me-2">list</mat-icon>
                          Available Test Cases
                        </span>
                        <button mat-stroked-button color="primary" (click)="toggleSelectAll()" [disabled]="availableTestCases.length === 0 && !allSelected">
                          <mat-icon class="me-1">{{ allSelected ? 'playlist_remove' : 'playlist_add' }}</mat-icon>
                          {{ allSelected ? 'Unselect All' : 'Select All' }}
                        </button>
                      </h4>
                      <div
                        cdkDropList
                        #availableList="cdkDropList"
                        [cdkDropListData]="filteredAvailableTestCases"
                        [cdkDropListConnectedTo]="[selectedList]"
                        [cdkDropListSortingDisabled]="true"
                        class="test-case-list"
                        (cdkDropListDropped)="onDrop($event)"
                        id="availableList"
                      >
                        <div
                          *ngFor="let testCase of filteredAvailableTestCases"
                          class="test-case-box"
                          cdkDrag
                          (click)="showTestCaseDetails(testCase)"
                        >
                          <div class="test-case-placeholder" *cdkDragPlaceholder></div>
                          <div class="test-case-content">
                            <div class="test-case-header">
                              <span class="test-case-name">{{ testCase.name }}</span>
                              <span class="test-case-priority" [ngClass]="'priority-' + testCase.priority">
                                P{{ testCase.priority }}
                              </span>
                            </div>
                            <div class="test-case-description">{{ testCase.description | slice:0:50 }}{{ (testCase.description?.length || 0) > 50 ? '...' : '' }}</div>
                          </div>
                        </div>
                        <div *ngIf="filteredAvailableTestCases.length === 0" class="empty-list-message">
                          {{ availableTestCases.length > 0 ? 'No matching test cases found' : 'No more test cases available' }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Selected test cases -->
                  <div class="col-md-5 mb-4 mx-2">
                    <div class="test-case-column">
                      <h4>
                        <mat-icon class="me-2">fact_check</mat-icon>
                        Selected Test Cases
                      </h4>
                      <div
                        cdkDropList
                        #selectedList="cdkDropList"
                        [cdkDropListData]="selectedTestCases"
                        [cdkDropListConnectedTo]="[availableList]"
                        [cdkDropListSortingDisabled]="true"
                        class="test-case-list"
                        (cdkDropListDropped)="onDrop($event)"
                      >
                        <div
                          *ngFor="let testCase of selectedTestCases"
                          class="test-case-box"
                          cdkDrag
                          (click)="showTestCaseDetails(testCase)"
                        >
                          <div class="test-case-placeholder" *cdkDragPlaceholder></div>
                          <div class="test-case-content">
                            <div class="test-case-header">
                              <span class="test-case-name">{{ testCase.name }}</span>
                              <span class="test-case-priority" [ngClass]="'priority-' + testCase.priority">
                                P{{ testCase.priority }}
                              </span>
                            </div>
                            <div class="test-case-description">{{ testCase.description | slice:0:50 }}{{ (testCase.description?.length || 0) > 50 ? '...' : '' }}</div>
                          </div>
                        </div>
                        <div *ngIf="selectedTestCases.length === 0" class="empty-list-message">
                          Drag test cases here
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Test case details view -->
                <div class="row justify-content-center mb-4" *ngIf="selectedTestCase">
                  <div class="col-md-10">
                    <div class="test-case-details-panel">
                      <h4>Test Case Details</h4>
                      <div class="test-case-details">
                        <div class="detail-row">
                          <strong>Name:</strong> {{ selectedTestCase.name }}
                        </div>
                        <div class="detail-row">
                          <strong>Priority: </strong>
                          <span class="test-case-priority" [ngClass]="'priority-' + selectedTestCase.priority">P{{ selectedTestCase.priority }}</span>
                        </div>
                        <div class="detail-row">
                          <strong>Status:</strong> {{ selectedTestCase.status }}
                        </div>
                        <div class="detail-row">
                          <strong>Category:</strong> {{ selectedTestCase.category }}
                        </div>
                        <div class="detail-row">
                          <strong>Attack Technique:</strong> {{ selectedTestCase.attack_technique }}
                        </div>
                        <div class="detail-row">
                          <strong>Description:</strong>
                          <p class="description-text">{{ selectedTestCase.description }}</p>
                        </div>
                        <div class="detail-row" *ngIf="selectedTestCase.recommendations">
                          <strong>Recommendations:</strong>
                          <p class="description-text">{{ selectedTestCase.recommendations }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Action buttons -->
              <div class="row custom-row mt-3">
                <div class="col-12 text-right">
                  <button mat-raised-button color="warn" class="mr-2" (click)="cancelUpdate(testPlan)">
                    Cancel
                  </button>
                  <button mat-raised-button color="primary" (click)="saveTestPlan(testPlan)">
                    {{ testPlan.uuid ? 'Update' : 'Create' }}
                  </button>
                </div>
              </div>
            </div>
          </mat-expansion-panel>
        </mat-accordion>
      </ng-container>
    </div>
  </div>
</div>
