// Base theme variables
:root {
  --primary: #3f51b5;
  --accent: #ff4081;
  --warn: #f44336;
  
  --bg-main: #f5f5f5;
  --bg-card: #ffffff;
  --bg-panel: #ffffff;
  --bg-list: #ffffff;
  --text-primary: rgba(0, 0, 0, 0.87);
  --text-secondary: rgba(0, 0, 0, 0.54);
  --border-color: #e0e0e0;
}

// Main layout
.content {
  padding: 20px;
  
  .bottom-section {
    margin-top: 20px;
  }
  
  .header-section {
    h2 {
      margin: 0;
      font-weight: 500;
      .text-primary {
        color: var(--primary);
      }
    }
  }
}

// Expansion panel styles
mat-expansion-panel {
  margin-bottom: 16px;
  background-color: var(--bg-panel);
  
  mat-expansion-panel-header {
    height: 64px;
    
    mat-panel-title {
      font-size: 16px;
      font-weight: 500;
      color: var(--text-primary);
    }
    
    mat-panel-description {
      color: var(--text-secondary);
      justify-content: flex-end;
      align-items: center;
      
      .d-flex {
        width: 100%;
      }
    }
  }
  
  .test-plan-details {
    padding: 16px 0;

    .detail-row .version-info {
      background-color: var(--primary);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      display: inline-block;
      font-weight: 500;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
      opacity: 1 !important;
    }
  }
}

// Form elements
.form-group {
  margin-bottom: 20px;
  
  .field-label {
    font-size: 13px;
    color: var(--text-secondary);
    margin-bottom: 5px;
    font-weight: 500;
  }
}

.form-row {
  margin-left: -15px;
  margin-right: -15px;
  width: calc(100% + 30px);
  
  .col-md-6 {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .field-label.text-center {
      text-align: center;
      width: 100%;
    }
    
    button {
      margin-top: 4px;
      height: 52px;
    }
  }
}

// Test case selection area
.test-case-selection {
  background-color: var(--bg-main);
  border-radius: 6px;
  padding: 24px;
  margin-top: 20px;
  border: 1px solid var(--border-color);
  
  h3 {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 20px;
  }
  
  .search-container {
    margin-bottom: 10px;
    
    .search-field {
      margin-bottom: 0;
    }
  }
  
  .search-results-info {
    margin-top: 10px;
    
    mat-chip-set {
      display: flex;
      flex-wrap: wrap;
    }
  }
}

// Test case column styles
.test-case-column {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  
  h4 {
    padding: 16px;
    margin: 0;
    font-weight: 500;
    font-size: 16px;
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-color);
    
    .mat-icon {
      margin-right: 8px !important;
      vertical-align: middle !important;
    }
    
    .title-text {
      display: flex !important;
      align-items: center !important;
    }
  }
}

// Test case list container
.test-case-list {
  min-height: 350px;
  max-height: 450px;
  overflow-y: auto;
  padding: 10px;
  flex-grow: 1;
  
  .empty-list-message {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    color: var(--text-secondary);
    font-style: italic;
  }
}

// Test case box item
.test-case-box {
  margin-bottom: 10px;
  border-radius: 6px;
  cursor: move;
  transition: all 0.2s ease;
  border-left: 4px solid transparent;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .test-case-content {
    padding: 12px 15px;
  }
  
  .test-case-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
    
    .test-case-name {
      font-weight: 500;
      font-size: 15px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  
  .test-case-description {
    font-size: 13px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 1.3;
  }
}

// Priority indicators
.test-case-priority {
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 12px;
  font-weight: bold;
  min-width: 30px;
  text-align: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  
  &.priority-1 {
    background-color: #f44336 !important;
    color: white !important;
  }
  
  &.priority-2 {
    background-color: #ff9800 !important;
    color: white !important;
  }
  
  &.priority-3 {
    background-color: #ffc107 !important;
    color: white !important;
  }
  
  &.priority-4 {
    background-color: #8bc34a !important;
    color: white !important;
  }
  
  &.priority-5 {
    background-color: #4caf50 !important;
    color: white !important;
  }
}

// Drag and drop styles
.cdk-drag-preview {
  box-sizing: border-box;
  box-shadow: 0 5px 10px rgba(0,0,0,0.2);
  border-radius: 6px;
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.test-case-placeholder {
  border: 2px dashed var(--primary);
  border-radius: 6px;
  margin: 0;
  min-height: 60px;
}

// Column alignment
.row.justify-content-center {
  display: flex;
  align-items: stretch;
  
  .col-md-5 {
    display: flex;
    flex-direction: column;
  }
  
  .test-case-column {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    
    background-color: #ffffff !important;
    
    h4 {
      background-color: #f0f0f0 !important;
      border-bottom: 2px solid var(--primary) !important;
      color: var(--primary) !important;
    }
  }
  
  .test-case-list {
    flex-grow: 1;
  }
}

// Dark theme
:host-context(.dark-theme), body.dark-theme {
  --primary: #7986cb;
  --accent: #ff80ab;
  --warn: #ef5350;
  
  --bg-main: #1e1e1e;
  --bg-card: #2d2d2d;
  --bg-panel: #2d2d2d;
  --bg-list: #333333;
  
  --text-primary: rgba(255, 255, 255, 0.87);
  --text-secondary: rgba(255, 255, 255, 0.6);
  --border-color: #555555;
  
  .test-case-column {
    background-color: #2d2d2d !important;
    border: 1px solid #555555 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
  }
  
  .test-case-list {
    background-color: #333333 !important;
    border: 1px solid #555555 !important;
  }
  
  .test-case-box {
    background-color: #424242 !important;
    color: rgba(255, 255, 255, 0.87) !important;
    border: 1px solid #555555 !important;
    
    &:hover {
      background-color: #505050 !important;
    }
  }
  
  .cdk-drag-preview {
    background-color: #424242 !important;
    color: white !important;
  }
  
  
  .test-case-column h4 {
    background-color: #333333 !important; 
    color: #ffffff !important;
    border-bottom: 2px solid #7986cb !important;
  }
}

// Light theme specific overrides
body:not(.dark-theme) {
  .test-case-column {
    background-color: #ffffff !important;
    border: 1px solid #c0c0c0 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  }
  
  .test-case-list {
    background-color: #f8f8f8 !important;
    border: 1px solid #c0c0c0 !important;
    border-radius: 6px !important;
    padding: 12px !important;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08) !important;
  }
  
  .test-case-box {
    background-color: #ffffff !important;
    color: rgba(0, 0, 0, 0.87) !important;
    border: 1px solid #c0c0c0 !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    margin-bottom: 8px !important;
    
    &:hover {
      background-color: #f0f0f0 !important;
      box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15) !important;
      transform: translateY(-1px) !important;
    }
  }
  
  .cdk-drag-preview {
    background-color: #ffffff !important;
    color: rgba(0, 0, 0, 0.87) !important;
    border: 1px solid #c0c0c0 !important;
  }
}

// Version badge styles
.version-badge,
span.version-badge,
div.version-badge,
.header-section .version-badge,
.me-4.version-badge {
  background-color: var(--primary) !important;
  color: white !important;
  padding: 6px 12px !important;
  border-radius: 16px !important;
  font-size: 0.85rem !important;
  font-weight: 500 !important;
  display: inline-block !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
  opacity: 1 !important;
  transition: none !important;
}

// Version badge header fixes
.header-section .version-badge.me-4,
.header-section span.version-badge,
span.version-badge.me-4 {
  background-color: #3f51b5 !important;
  color: white !important;
  padding: 6px 12px !important;
  border-radius: 16px !important;
  font-size: 0.85rem !important;
  font-weight: 500 !important;
  display: inline-block !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
  opacity: 1 !important;
  border: none !important;
}

span[class*="version-badge"] {
  background: #3f51b5 !important;
  background-color: #3f51b5 !important;
  color: white !important;
}

body .content .bottom-section .header-section .d-flex.align-items-center span.version-badge.me-4,
app-test-plan .header-section span.version-badge.me-4 {
  background: #3f51b5 !important;
  color: white !important;
  display: inline-block !important;
  padding: 6px 12px !important;
}

// Test case details panel
.test-case-details-panel {
  background-color: var(--bg-card);
  border-radius: 8px;
  padding: 16px;
  margin-top: 20px;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  h4 {
    color: var(--primary);
    margin-top: 0;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
    font-weight: 500;
  }
  
  .test-case-details {
    .detail-row {
      margin-bottom: 10px;
      
      strong {
        font-weight: 500;
        color: var(--text-primary);
      }
      
      .description-text {
        margin-top: 5px;
        white-space: pre-line;
      }
    }
  }
}

// Action buttons
button.mat-raised-button {
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
  
  &.mat-primary {
    background-color: var(--primary);
  }
  
  &.mat-warn {
    background-color: var(--warn);
  }
  
  mat-icon {
    vertical-align: middle;
    margin-right: 4px;
  }
}

// Global version badge fixes
.version-badge, 
.version-text,
.version-info,
[class*="version-"],
span.version-badge,
div.version-badge,
.header-section .version-badge,
.me-4.version-badge {
  background-color: var(--primary) !important;
  color: white !important;
  padding: 4px 10px !important;
  border-radius: 12px !important;
  font-size: 0.85rem !important;
  font-weight: 500 !important;
  display: inline-block !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2) !important;
  opacity: 1 !important;
  visibility: visible !important;
  z-index: 10 !important;
  position: relative !important;
}

// Drag and drop specific fixes
.cdk-drag-preview {
  box-sizing: border-box !important;
  box-shadow: 0 5px 10px rgba(0,0,0,0.3) !important;
  border-radius: 6px !important;
  opacity: 1 !important;
  
  body.dark-theme & {
    background-color: #424242 !important;
    color: white !important;
    border: 1px solid #555555 !important;
  }
  
  body:not(.dark-theme) & {
    background-color: #ffffff !important;
    color: rgba(0, 0, 0, 0.87) !important;
    border: 1px solid #c0c0c0 !important;
  }
  
  .test-case-content {
    opacity: 1 !important;
  }
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1) !important;
  opacity: 1 !important;
}

.test-case-box.cdk-drag-placeholder {
  opacity: 0 !important;
}

.test-case-placeholder {
  border: 2px dashed var(--primary) !important;
  border-radius: 6px !important;
  margin: 0 !important;
  min-height: 60px !important;
  background-color:var(--primary) !important;
}

// Dark theme column header fixes
body.dark-theme .test-case-column h4,
:host-context(body.dark-theme) .test-case-column h4 {
  background-color: #1e1e1e !important; 
  color: #ffffff !important;
  border-bottom: 2px solid #7986cb !important;
}

body.dark-theme .test-case-column,
:host-context(body.dark-theme) .test-case-column {
  background-color: transparent !important;
  border: 1px solid #444444 !important;
}

body.dark-theme .test-case-list,
:host-context(body.dark-theme) .test-case-list {
  background-color: #2d2d2d !important;
  border: 1px solid #444444 !important;
  
  .empty-list-message {
    color: rgba(255, 255, 255, 0.5) !important;
  }
}

body.dark-theme .test-case-box,
:host-context(body.dark-theme) .test-case-box {
  background-color: #333333 !important;
  border: 1px solid #444444 !important;
  
  &:hover {
    background-color: #444444 !important;
  }
}

html body.dark-theme .test-case-column h4,
html body.dark-theme .test-case-column .title-with-button,
:host-context(html body.dark-theme) .test-case-column h4 {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
  border-bottom: 2px solid #7986cb !important;
}

// Browser-specific fixes
@-moz-document url-prefix() {
  .version-badge, 
  .version-text,
  .version-info,
  [class*="version-"] {
    display: inline-block !important;
    background-color: var(--primary) !important;
  }
  
  .cdk-drag-preview {
    opacity: 1 !important;
  }
}

@media not all and (min-resolution:.001dpcm) { 
  @supports (-webkit-appearance:none) {
    .version-badge, 
    .version-text,
    .version-info,
    [class*="version-"] {
      -webkit-appearance: none;
      appearance: none;
      background-color: var(--primary) !important;
      color: white !important;
    }
  }
}

