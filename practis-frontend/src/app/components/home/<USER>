<!-- dashboard.component.html -->
<div class="dashboard-container">
    <div class="left-column">
        <!-- SutProvider Section -->
        <div class="top-section-section">
            <h2>
                <span class="text-primary">SUT Provider:</span>
                <button *ngIf="!sut_provider" mat-raised-button color="primary" class="add-button"
                    (click)="openAddSutProviderForm()">
                    Add SUT provider
                </button>
                <button *ngIf="sut_provider" mat-raised-button color="primary" class="edit-button"
                    (click)="openEditSutProviderForm(sut_provider)">
                    Edit SUT provider
                </button>
            </h2>
            <mat-card class="top-section-card cardWithShadow">
                <mat-card-content *ngIf="!sut_provider">
                    <div class="no-top-section">
                        <h3>No SUT provider found</h3>
                    </div>
                </mat-card-content>
                <mat-card-content *ngIf="sut_provider">
                    <div class="top-section-info">
                        <h3>{{sut_provider.name}}</h3>
                        <div class="content-all">
                            <div class="content-left">
                                <div [innerHTML]="safeDescriptions['sut_provider']"></div>
                                <br />
                                <p>Address: {{sut_provider.fullAddress}}</p><br />
                            </div>
                            <div class="content-right" *ngIf="sut_provider.images && sut_provider.images.length > 0">
                                <!-- <img [src]="sut_provider.images ? utilsService.getDefaultImage(sut_provider.images) : ''"
                                    alt="SutProvider logo" class="align-middle m-2 object-cover" width="100%"> -->
                                <ng-image-slider [images]="sliderImages['sut_provider']" #nav [manageImageRatio]="true"
                                    [imagePopup]="true"
                                    [imageSize]="{width: 205, height: 100, space: 3}"></ng-image-slider>
                            </div>
                        </div>
                        <div class="mat-elevation-z1 responsive-table">
                            <table mat-table [dataSource]="sut_provider.contacts || []" class="contact-table">
                                <!-- Name Column -->
                                <ng-container matColumnDef="name">
                                    <th mat-header-cell *matHeaderCellDef> Name </th>
                                    <td mat-cell *matCellDef="let contact"> {{contact.name}} </td>
                                </ng-container>

                                <!-- Phone Number Column -->
                                <ng-container matColumnDef="phone">
                                    <th mat-header-cell *matHeaderCellDef> Phone </th>
                                    <td mat-cell *matCellDef="let contact"> {{contact.phone}} </td>
                                </ng-container>

                                <!-- Email Column -->
                                <ng-container matColumnDef="mail">
                                    <th mat-header-cell *matHeaderCellDef> Mail </th>
                                    <td mat-cell *matCellDef="let contact"> {{contact.mail}} </td>
                                </ng-container>

                                <!-- Description Column -->
                                <ng-container matColumnDef="description">
                                    <th mat-header-cell *matHeaderCellDef> Description </th>
                                    <td mat-cell *matCellDef="let contact"> {{contact.description}} </td>
                                </ng-container>

                                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                            </table>
                        </div>
                    </div>
                </mat-card-content>
            </mat-card>
        </div>

        <!-- SUTs Section -->
        <div class="bottom-section">
            <h2>
                <span class="text-primary">SUTs:</span>
                <button *ngIf="sut_provider" mat-raised-button color="primary" class="new-bottom-button"
                    (click)="openAddSutForm()">
                    New SUT
                </button>
            </h2>

            <div class="bottom-cards">
                <ng-container *ngIf="!suts || suts.length === 0">
                    <mat-card class="bottom-card cardWithShadow">
                        <mat-card-content>
                            <div class="no-top-section">
                                <h3>No SUTs found</h3>
                            </div>
                        </mat-card-content>
                    </mat-card>
                </ng-container>
                <ng-container *ngIf="suts && suts.length > 0">
                    <mat-card *ngFor="let sut of suts" class="bottom-card cardWithShadow">
                        <div class="title-buttons">
                            <h4><a class="text-primary no-underline" [routerLink]="['/sut', sut.uuid]">{{sut.name}}</a>
                            </h4>
                            <div>
                                <button mat-icon-button (click)="openEditSutForm(sut)">
                                    <mat-icon color="primary">edit</mat-icon>
                                </button>
                                <button mat-icon-button (click)="deleteSut(sut)">
                                    <mat-icon color="warn">delete</mat-icon>
                                </button>
                            </div>
                        </div>
                        <mat-card-content>
                            <div class="content-all">
                                <div class="content-left">
                                    <div *ngIf="sut.uuid" [innerHTML]="safeDescriptions[sut.uuid]"></div>
                                </div>
                                <div class="content-right" *ngIf="sut.images && sut.images.length > 0">
                                    <ng-image-slider *ngIf="sut.uuid" [images]="sliderImages[sut.uuid]" #nav
                                        [manageImageRatio]="true" [imagePopup]="true"
                                        [imageSize]="{width: 200, height: 100, space: 2}"></ng-image-slider>
                                </div>
                            </div>
                        </mat-card-content>
                    </mat-card>
                </ng-container>

            </div>
        </div>
    </div>
    <div class="right-column">
        <!-- Dashboard Section -->
        <div class="dashboard-section">
            <h2>
                <span class="text-primary">Dashboard:</span>
            </h2>
            <mat-card class="dashboard-card cardWithShadow">
                <mat-card-content>
                    <h4>Vulnerability</h4>
                    <div *ngIf="!vulnerabilityData || vulnerabilityData.length === 0" class="no-vulnerability">
                        <h3>No vulnerability data.</h3>
                    </div>
                    <div class="chart-wrapper" *ngIf="vulnerabilityData && vulnerabilityData.length > 0">
                        <ngx-charts-bar-vertical-stacked [results]="vulnerabilityData" [xAxis]="true" [yAxis]="true"
                            [legend]="true" [showXAxisLabel]="true" [showYAxisLabel]="true" xAxisLabel="SUT/Version"
                            yAxisLabel="Count" [barPadding]="8" [legendPosition]="legendPos">
                        </ngx-charts-bar-vertical-stacked>
                    </div>
                    <br />
                    <p>&nbsp;</p>
                    <h4>Tests</h4>
                    <div *ngIf="!testData || testData.length === 0" class="no-vulnerability">
                        <h3>No test run data.</h3>
                    </div>
                    <div class="chart-wrapper" *ngIf="testData && testData.length > 0">
                        <!-- <ngx-charts-line-chart [results]="testData" [xAxis]="true" [yAxis]="true" [legend]="true"
                            [showXAxisLabel]="true" [showYAxisLabel]="true" xAxisLabel="Date" yAxisLabel="Tests"
                            [yScaleMin]="0" [legendPosition]="legendPos">
                        </ngx-charts-line-chart> -->
                        <ngx-charts-bar-vertical-stacked [results]="testData" [xAxis]="true" [yAxis]="true"
                            [legend]="true" [showXAxisLabel]="true" [showYAxisLabel]="true" xAxisLabel="Test Run"
                            yAxisLabel="Count" [barPadding]="8" [legendPosition]="legendPos">
                        </ngx-charts-bar-vertical-stacked>
                    </div>
                    <br />
                    <p>&nbsp;</p>
                </mat-card-content>
            </mat-card>
        </div>
    </div>
</div>