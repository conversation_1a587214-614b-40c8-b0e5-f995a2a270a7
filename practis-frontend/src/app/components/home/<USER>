import { Component, OnDestroy, OnInit } from '@angular/core';
import { MaterialModule } from 'app/material.module';
import { LegendPosition } from '@swimlane/ngx-charts';
import { SutProvider } from 'app/models/sut-provider';
import { SutProviderComponent } from '../sut-provider/sut-provider.component';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { SutProviderService } from 'app/services/sut-provider.service';
import { Sut } from 'app/models/sut';
import { SutService } from 'app/services/sut.service';
import { UtilsService } from 'app/utils/utils';
import { SutAddEditComponent } from '../sut-add-edit/sut-add-edit.component';
import { SafeHtml } from '@angular/platform-browser';
import { MenuService } from 'app/services/menu.service';
import { DashboardService } from 'app/services/dashboard.service';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [MaterialModule],
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss',
})
export class HomeComponent implements OnInit, OnDestroy {
  legendPos: LegendPosition = LegendPosition.Below;
  sut_provider?: SutProvider;
  displayedColumns: string[] = ['name', 'phone', 'mail', 'description'];
  suts?: Sut[];
  vulnerabilityData?: any;
  testData?: any;
  safeDescriptions: { [key: string]: SafeHtml } = {};
  sliderImages: { [key: string]: Array<Object> } = {};

  constructor(
    private _dialog: MatDialog,
    private _sutProviderService: SutProviderService,
    private _sutService: SutService,
    private _dashboardService: DashboardService,
    public _snackBar: MatSnackBar,
    private _utilsService: UtilsService,
    private _menuService: MenuService
  ) {}

  ngOnInit(): void {
    this.getSutProvider();
    this.getSutList();
    this.getDashboardData();
  }

  //get sut provider
  getSutProvider() {
    this._sutProviderService.getSutProviderList().subscribe({
      next: (res) => {
        //console.log(res);
        if (res.length != 0) {
          this.sut_provider = new SutProvider(res[0]);
          this.setImagesSutProvider();
        }
      },
      error: console.log,
    });
  }
  getDashboardData() {
    this._dashboardService.getDashboardData().subscribe({
      next: (res) => {
        this.vulnerabilityData = res.vulnerabilityData || [];
        this.testData = res.testData || [];
      },
      error: console.log,
    });
  }

  setImagesSutProvider() {
    this.safeDescriptions['sut_provider'] =
      this._utilsService.getSafeDescription(this.sut_provider?.description);
    this.sliderImages['sut_provider'] = this.sut_provider?.images
      ? this._utilsService.getSliderImages(this.sut_provider.images)
      : [];
  }

  getSutList() {
    this._sutService.getSutList().subscribe({
      next: (res) => {
        //console.log(res);
        this.suts = res;
        this.suts.forEach((sut) => {
          if (sut.uuid) {
            this.safeDescriptions[sut.uuid] =
              this._utilsService.getSafeDescription(sut.description);
            this.sliderImages[sut.uuid] = sut.images
              ? this._utilsService.getSliderImages(sut.images)
              : [];
          }
        });
      },
      error: console.log,
    });
  }

  //add form
  openAddSutProviderForm() {
    const dialogRef = this._dialog.open(SutProviderComponent);
    dialogRef.afterClosed().subscribe({
      next: (val) => {
        if (val) {
          this.sut_provider = new SutProvider(val);
          this.setImagesSutProvider();
        }
      },
    });
  }

  //edit form
  openEditSutProviderForm(sut_provider: SutProvider) {
    const dialogRef = this._dialog.open(SutProviderComponent, {
      data: sut_provider,
    });
    dialogRef.afterClosed().subscribe({
      next: (val) => {
        if (val) {
          this.sut_provider = new SutProvider(val);
          this.setImagesSutProvider();
          this._menuService.getMenuList();
        }
      },
    });
  }

  ngOnDestroy(): void {
    this._snackBar.dismiss();
  }

  openAddSutForm(): void {
    const dialogRef = this._dialog.open(SutAddEditComponent, {
      data: { sut_provider: this.sut_provider?.uuid },
    });
    dialogRef.afterClosed().subscribe({
      next: (val) => {
        if (val) {
          this.getSutList();
          this._menuService.getMenuList();
        }
      },
    });
  }
  openEditSutForm(sut: Sut): void {
    const dialogRef = this._dialog.open(SutAddEditComponent, {
      data: { sut: sut, sut_provider: this.sut_provider?.uuid },
    });
    dialogRef.afterClosed().subscribe({
      next: (val) => {
        if (val) {
          this.getSutList();
          this._menuService.getMenuList();
        }
      },
    });
  }

  //delete sut
  deleteSut(sut: Sut) {
    const backup = this.suts ? [...this.suts] : []; // Create a shallow copy of the data for backup
    this.suts = this.suts?.filter((m) => m.uuid !== sut.uuid); // Remove sut from data source

    // Open a snackbar with an undo option
    const snackBarRef = this._snackBar.open(
      `Sut '${sut.name}' will be deleted`,
      'Undo',
      { duration: 3000 }
    );

    // Handle snackbar dismissal
    snackBarRef.afterDismissed().subscribe((res) => {
      if (!res.dismissedByAction) {
        // If user did not click 'Undo', proceed with deletion
        this._sutService.deleteSut(sut.uuid as string).subscribe({
          next: (response: any) => {
            this._menuService.getMenuList();
            this._snackBar.open('Sut deleted!', 'Done', { duration: 3000 });
          },
          error: (error) => {
            //console.error('Error deleting sut:', error);
            //Restore data on error
            //204 No content should not return message (Backend needs to correct that)
            this.suts = backup;
            this._snackBar.open(
              'Failed to delete sut. Please try again.',
              'Close',
              { duration: 5000 }
            );
          },
        });
      } else {
        // If 'Undo' was clicked, restore the original data
        this.suts = backup;
      }
    });
  }
}
