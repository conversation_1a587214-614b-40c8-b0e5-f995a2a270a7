import { Component, Inject, OnInit } from '@angular/core';
import { MaterialModule } from 'app/material.module';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { SutProvider } from 'app/models/sut-provider';
import { ImageFile } from 'app/models/image-file';
import { SutProviderService } from 'app/services/sut-provider.service';
import { UtilsService } from 'app/utils/utils';

@Component({
  selector: 'app-sut-provider',
  standalone: true,
  imports: [MaterialModule],
  templateUrl: './sut-provider.component.html',
  styleUrl: './sut-provider.component.scss',
})
export class SutProviderComponent implements OnInit {
  form: FormGroup;
  imageFiles: ImageFile[] = [];
  isLoading = false;

  constructor(
    private _fb: FormBuilder,
    private _dialogRef: MatDialogRef<SutProviderComponent>,
    private _sutProviderService: SutProviderService,
    @Inject(MAT_DIALOG_DATA) public data: SutProvider,
    public _snackBar: MatSnackBar,
    public utilsService: UtilsService
  ) {
    this.form = this._fb.group({
      name: ['', Validators.required],
      description: [''],
      address: this._fb.group({
        street: [''],
        city: [''],
        state: [''],
        zip: [''],
      }),
      contacts: this._fb.array([]),
    });
  }

  // Getter for the parameters FormArray
  get contacts(): FormArray {
    return this.form.get('contacts') as FormArray;
  }

  // Add a new key-value pair to the parameters array
  addContact() {
    const contactGroup = this._fb.group({
      name: ['', Validators.required],
      mail: ['', Validators.required],
      phone: [''],
      description: [''],
    });
    this.contacts.push(contactGroup);
  }

  // Remove from the contacts array
  removeContact(index: number) {
    if (this.contacts.length > 1) {
      this.contacts.removeAt(index);
    }
  }

  ngOnInit(): void {
    if (this.data) {
      // If editing, initialize the form with asset data
      this.initializeForm(this.data);
    } else {
      // If adding new, add an initial empty parameter row
      this.addContact();
    }
  }

  // Populate form fields for editing an existing asset
  private initializeForm(data: SutProvider): void {
    this.isLoading = true;
    this.form.patchValue({
      name: data.name,
      description: data.description?.replace(/\\n/g, '\n'),
      address: {
        street: data?.address?.street,
        city: data?.address?.city,
        state: data?.address?.state,
        zip: data?.address?.zip,
      },
    });

    if (this.data?.images && this.data?.images.length > 0) {
      this.imageFiles = this.data.images.map((image) => new ImageFile(image));
      if (!this.imageFiles.some((image) => image.default)) {
        this.imageFiles[0].default = true;
      }
    }

    // Parse parameters JSON string to populate FormArray with key-value pairs
    if (data.contacts) {
      data.contacts.forEach((contact) => {
        const contactGroup = this._fb.group({
          name: [contact.name, Validators.required],
          mail: [contact.mail, Validators.required],
          phone: [contact.phone],
          description: [contact.description],
        });
        this.contacts.push(contactGroup);
      });
    }
    // Manually update form validity after patching values to ensure button reflects the correct state
    this.markFormGroupTouchedAndDirty(this.form);
    this.isLoading = false;
  }

  // Function to mark all controls as touched and dirty
  private markFormGroupTouchedAndDirty(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      if (control instanceof FormArray) {
        // Recursively apply to each FormGroup in the FormArray
        (control as FormArray).controls.forEach((arrayControl) => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouchedAndDirty(arrayControl);
          }
        });
      } else if (control instanceof FormGroup) {
        this.markFormGroupTouchedAndDirty(control);
      } else {
        control.markAsTouched();
        control.markAsDirty();
      }
    });
  }

  onImageSelect(event: any) {
    const images = event.target.files;
    if (images) {
      const newImages = Array.from(images).map((file, index) => {
        const reader = new FileReader();
        const imageFile = new ImageFile({
          uploadFile: file as File,
          preview: '',
          default: this.imageFiles.length === 0 && index === 0,
        });

        reader.onload = (e: any) => {
          imageFile.preview = e.target.result;
        };
        reader.readAsDataURL(file as Blob);

        return imageFile;
      });

      this.imageFiles.push(...newImages);
    }
  }

  removeImage(index: number) {
    const removedImage = this.imageFiles[index];
    this.imageFiles.splice(index, 1);

    if (removedImage.default && this.imageFiles.length > 0) {
      this.imageFiles[0].default = true;
    }
  }

  setImageAsDefault(index: number) {
    this.imageFiles.forEach((image) => (image.default = false));
    this.imageFiles[index].default = true;
  }

  onFormSubmit() {
    if (this.form.valid) {
      // Create FormData to handle image uploads
      const formData = new FormData();

      Object.keys(this.form.value).forEach((key) => {
        formData.append(key, JSON.stringify(this.form.value[key]));
      });

      let images: { uuid: string; default: number }[] = [];
      this.imageFiles.forEach((image, index) => {
        let img = {
          uuid: image.uuid || '',
          default: image.default ? 1 : 0,
        };
        images.push(img);

        if (image.uploadFile) {
          formData.append('files', image.uploadFile);
        }
      });
      formData.append('images', JSON.stringify(images));
      //console.log(formData);

      //Uncomment later when backend work correctly
      if (this.data && this.data.uuid) {
        this._sutProviderService
          .updateSutProvider(this.data.uuid, formData)
          .subscribe({
            next: (val: any) => {
              //console.log(val);
              this._snackBar.open('SutProvider detail updated!', 'Done', {
                duration: 3000,
              });
              this._dialogRef.close(val);
            },
            error: (err: any) => {
              console.error(err);
            },
          });
      } else {
        this._sutProviderService.addSutProvider(formData).subscribe({
          next: (val: any) => {
            this._snackBar.open('SutProvider added successfully', 'Done', {
              duration: 3000,
            });
            this._dialogRef.close(val);
          },
          error: (err: any) => {
            console.error(err);
          },
        });
      }
    }
  }
}
