<div mat-dialog-title>
    <h4>{{data ? 'Update': 'Add'}} SUT Provider</h4>
</div>

<div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
</div>

<form [formGroup]="form" class="form" novalidate>
    <div mat-dialog-content class="content sutprovider-form">
        <div class="row custom-row">
            <div class="col-12 col-sm-12">
                <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Name *</mat-label>
                <mat-form-field appearance="outline" class="w-100" color="primary">
                    <input matInput formControlName="name" required />
                    <mat-error *ngIf="form.get('name')?.hasError('required')" class="no-pad">
                        Name is required
                    </mat-error>
                </mat-form-field>

                <!-- Address Section -->
                <div formGroupName="address" class="mb-4">
                    <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Address</mat-label>
                    <div class="row custom-row">
                        <div class="col-12 col-sm-6">
                            <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Street Address</mat-label>
                            <mat-form-field appearance="outline" class="w-100" color="primary">
                                <input matInput formControlName="street" />
                            </mat-form-field>
                        </div>
                        <div class="col-12 col-sm-6">
                            <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Zip Code</mat-label>
                            <mat-form-field appearance="outline" class="w-100" color="primary">
                                <input matInput formControlName="zip" />
                            </mat-form-field>
                        </div>
                        <div class="col-12 col-sm-6">
                            <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">City</mat-label>
                            <mat-form-field appearance="outline" class="w-100" color="primary">
                                <input matInput formControlName="city" />
                            </mat-form-field>
                        </div>
                        <div class="col-12 col-sm-6">
                            <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">State / Province</mat-label>
                            <mat-form-field appearance="outline" class="w-100" color="primary">
                                <input matInput formControlName="state" />
                            </mat-form-field>
                        </div>
                    </div>
                </div>

                <!-- Images -->
                <div class="image-section">
                    <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Images</mat-label>
                    <button mat-stroked-button color="primary" (click)="fileInput.click()">
                        <mat-icon>upload</mat-icon>
                        Upload Images
                    </button>
                    <input #fileInput type="file" hidden (change)="onImageSelect($event)" multiple accept="image/*">

                    <div class="image-list" *ngIf="imageFiles.length > 0">
                        <mat-card *ngFor="let image of imageFiles; let i = index" [class.default-image]="image.default">
                            <mat-card-content class="image-item">
                                <img *ngIf="image.file" [src]="utilsService.getImage(image.file)" alt="Image"
                                    class="image-preview">
                                <img *ngIf="image.preview" [src]="image.preview" alt="Image" class="image-preview">
                                <span class="image-name">{{ image.file ? image.file.split('/').pop() :
                                    image.uploadFile?.name
                                    }}</span>

                                <div class="image-actions">
                                    <button mat-button color="primary" (click)="setImageAsDefault(i)"
                                        [disabled]="image.default" class="btn-default">
                                        {{ image.default ? 'Default' : 'Set as Default' }}
                                    </button>
                                    <button mat-icon-button color="warn" (click)="removeImage(i)">
                                        <mat-icon>delete</mat-icon>
                                    </button>
                                </div>
                            </mat-card-content>
                        </mat-card>
                    </div>
                </div>


                <!-- Description Field -->
                <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Description</mat-label>
                <!-- <quill-editor matInput formControlName="description"></quill-editor> -->
                <mat-form-field appearance="outline" class="w-100" color="primary">
                    <textarea matInput formControlName="description"></textarea>
                </mat-form-field>
                <div class="bottom-section">
                    <h2 style="margin-top: 2px !important;">
                        <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Contacts</mat-label>
                        <button mat-raised-button color="accent" type="button" (click)="addContact()">
                            Add contact
                        </button>
                    </h2>
                </div>

                <div formArrayName="contacts" class="responsive-table">
                    <!-- Table structure for Key and Value inputs -->
                    <table>
                        <!-- Table Header with labels for Key and Value -->
                        <thead>
                            <tr>
                                <th><mat-label>Name*</mat-label></th>
                                <th><mat-label>Mail*</mat-label></th>
                                <th><mat-label>Phone</mat-label></th>
                                <th><mat-label>Description</mat-label></th>
                                <th><mat-label></mat-label></th>
                            </tr>
                        </thead>

                        <!-- Table Body where input rows are dynamically created -->
                        <tbody>
                            <tr *ngFor="let contact of contacts.controls; let i = index" [formGroupName]="i">
                                <td>
                                    <mat-form-field appearance="outline" class="key-input">
                                        <input matInput formControlName="name" required />
                                        <mat-error *ngIf="contact.get('name')?.hasError('required')">
                                            Name is required
                                        </mat-error>
                                    </mat-form-field>
                                </td>
                                <td>
                                    <mat-form-field appearance="outline" class="value-input">
                                        <input matInput formControlName="mail" required />
                                        <mat-error *ngIf="contact.get('mail')?.hasError('required')">
                                            Mail is required
                                        </mat-error>
                                    </mat-form-field>
                                </td>
                                <td>
                                    <mat-form-field appearance="outline" class="value-input">
                                        <input matInput formControlName="phone" />
                                    </mat-form-field>
                                </td>
                                <td>
                                    <mat-form-field appearance="outline" class="value-input">
                                        <input matInput formControlName="description" />
                                    </mat-form-field>
                                </td>

                                <!-- Remove Row Button -->
                                <td>
                                    <button mat-icon-button color="warn" (click)="removeContact(i)"
                                        aria-label="Remove contact" class="remove-button">
                                        <mat-icon>delete</mat-icon>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>

    <div mat-dialog-actions class="action">
        <div class="button-container">
            <button mat-raised-button color="primary" type="submit"
                [disabled]="form.pristine || form.invalid || form.pending" (click)="onFormSubmit()">{{data ? 'Update':
                'Save'}}</button>
            <button mat-raised-button type="button" [mat-dialog-close]="false">Cancel</button><br />
        </div><br /><br />
    </div>
</form>