.test-case-table-header {
  display: flex;
  background-color: #f5f5f5;
  color: #333;
  font-weight: bold;
  padding: 10px;
  border-radius: 4px 4px 0 0;
  margin-bottom: 5px;
}

.test-case-row {
  display: flex;
  padding: 10px;
  border-bottom: 1px solid #eee;
  align-items: center;
  background-color: white;
  color: #333;
}

.name-col {
  flex: 3;
  padding-right: 10px;
}

.priority-col, .status-col {
  flex: 1;
  padding-right: 10px;
}

.action-col {
  flex: 1;
  display: flex;
  align-items: center;
}

.test-case-table-header .action-col {
  justify-content: center;
}

.test-case-row .action-col {
  justify-content: flex-end;
}

.test-case-details {
  margin-top: 10px;
  margin-bottom: 20px;
  padding-left: 10px;
}

.test-case-row:hover {
  background-color: #f9f9f9;
  color: #333;
}

.test-case-row:hover .expansion-indicator {
  opacity: 1;
}

.test-case-row:hover .temporary-indicator {
  opacity: 1;
}

.test-case-container {
  margin-bottom: 5px;
  position: relative;
}

.test-case-details-container {
  display: none;
}

.test-case-details-container.visible {
  display: block;
}

.field-label {
  font-weight: 500;
  margin-bottom: 5px;
  color: #333;
  font-size: 0.9rem;
}

.mat-expansion-panel {
  background-color: white !important;
  color: #333 !important;
}

.mat-expansion-panel-header {
  background-color: #f5f5f5 !important;
}

.mat-expansion-panel-header-title,
.mat-expansion-panel-header-description {
  color: #333 !important;
}

.mat-card {
  background-color: white !important;
  color: #333 !important;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
  color: rgba(0, 0, 0, 0.12) !important;
}

::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
  color: #3f51b5 !important;
}

::ng-deep .mat-form-field-label {
  color: rgba(0, 0, 0, 0.6) !important;
}

::ng-deep .mat-input-element {
  color: #333 !important;
}

::ng-deep .mat-select-value {
  color: #333 !important;
}

::ng-deep .mat-expansion-panel-header-description {
  justify-content: space-between;
  align-items: center;
}

.expansion-indicator {
  opacity: 0.5;
  font-size: 20px;
  width: 20px;
  height: 20px;
  line-height: 20px;
  margin-left: 4px;
}

.temporary-indicator {
  margin-right: 8px;
  opacity: 0.7;
}

.search-container {
  display: flex;
  align-items: flex-start; 
  margin-bottom: 16px;
  gap: 10px;
}

.search-field {
  flex: 1;
}

.search-container button.mat-raised-button {
  margin-top: 4px;
  height: 40px; 
}

::ng-deep .search-field {
  .mat-form-field-label-wrapper {
    top: -0.5em;
  }
  
  .mat-form-field-label {
    margin-left: 0;
  }
  
  .mat-form-field-infix {
    padding: 0.75em 0;
    width: auto;
  }
  
  .mat-input-element {
    margin-top: 0.5em;
  }
}

.search-results-info {
  margin-top: 8px;
}

:host-context(.dark-theme) {
  .test-case-table-header {
    background-color: #383838;
    color: white;
  }
  
  .test-case-row {
    background-color: #303030;
    color: white;
    border-bottom-color: #555;
  }
  
  .test-case-row:hover {
    background-color: #424242;
    color: white;
  }
  
  .field-label {
    color: rgba(255, 255, 255, 0.7);
  }
  
  ::ng-deep {
    .mat-form-field-appearance-outline .mat-form-field-outline {
      color: rgba(255, 255, 255, 0.3);
    }
    
    .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
      color: #7986cb;
    }
    
    .mat-expansion-panel {
      background-color: #303030 !important;
      color: white !important;
    }
    
    .mat-expansion-panel-header {
      background-color: #383838 !important;
    }
    
    .mat-expansion-panel-header-title,
    .mat-expansion-panel-header-description {
      color: white !important;
    }
    
    .mat-card {
      background-color: #424242 !important;
      color: white !important;
    }
    
    .mat-form-field-label {
      color: rgba(255, 255, 255, 0.7) !important;
    }
    
    .mat-input-element {
      color: white !important;
    }
    
    .mat-select-value {
      color: white !important;
    }
  }
}

body:not(.dark-theme) {
  .test-case-table-header {
    background-color: #f5f5f5 !important;
    color: #333 !important;
  }
  
  .test-case-row {
    background-color: white !important;
    color: #333 !important;
    border-bottom-color: #eee !important;
  }
  
  .test-case-row:hover {
    background-color: #f9f9f9 !important;
    color: #333 !important;
  }
  
  .field-label {
    color: #333 !important;
  }
  
  .mat-expansion-panel {
    background-color: white !important;
    color: #333 !important;
  }
  
  .mat-expansion-panel-header {
    background-color: #f5f5f5 !important;
  }
  
  .mat-expansion-panel-header-title,
  .mat-expansion-panel-header-description {
    color: #333 !important;
  }
  
  .mat-card {
    background-color: white !important;
    color: #333 !important;
  }
  
  ::ng-deep .mat-form-field-label {
    color: rgba(0, 0, 0, 0.6) !important;
  }
  
  ::ng-deep .mat-input-element {
    color: #333 !important;
  }
  
  ::ng-deep .mat-select-value {
    color: #333 !important;
  }
}

@media (prefers-color-scheme: dark) {
  body:not(.light-theme) {
    .test-case-table-header {
      background-color: #383838;
      color: white;
    }
    
    .test-case-row {
      background-color: #303030;
      color: white;
      border-bottom-color: #555;
    }
    
    .test-case-row:hover {
      background-color: #424242;
      color: white;
    }
    
    .field-label {
      color: rgba(255, 255, 255, 0.7);
    }
  }
}