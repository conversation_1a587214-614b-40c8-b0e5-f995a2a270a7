import { Component, OnInit,OnDestroy, Output, Input, EventEmitter } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FormsModule } from '@angular/forms';
import { MaterialModule } from 'app/material.module';
import { forkJoin } from 'rxjs';
import { Version } from 'app/models/version';
import { Sut } from 'app/models/sut';
import { ComponentModel } from 'app/models/component';
import { SubcomponentModel } from 'app/models/subcomponent';
import { PortModel } from 'app/models/port';
import { VulnerabilityService } from 'app/services/vulnerability.service';
import { RequirementService } from 'app/services/requirement.service';
import { TestCaseService } from 'app/services/test-case.service';
import { Vulnerability } from 'app/models/vulnerability';
import { Requirement } from 'app/models/requirement';
import { TestCase } from 'app/models/test-case';
import { TestCasePriority, TestCaseStatus } from 'app/models/enum-types';

// Interfaces for TestCase with expanded for UI
interface TestCaseWithUIData extends TestCase {
  expanded?: boolean;
  isNew?: boolean;
}

// Interface for Vulnerability with test cases
interface VulnerabilityWithTestCases extends Vulnerability {
  testCases?: TestCaseWithUIData[];
  expanded?: boolean;
}

// Interface for Requirement with test cases
interface RequirementWithTestCases extends Requirement {
  testCases?: TestCaseWithUIData[];
  expanded?: boolean;
}

@Component({
  selector: 'app-test-case',
  standalone: true,
  imports: [MaterialModule],
  templateUrl: './test-case.component.html',
  styleUrl: './test-case.component.scss'
})
export class TestCaseComponent implements OnInit, OnDestroy {

  // Properties for the component
  vulnerabilities?: Vulnerability[];
  requirements?: Requirement[];
  testCases?: TestCase[];

  vulnerabilitiesWithTestCases?: VulnerabilityWithTestCases[]; // Vulnerabilities with test cases
  requirementsWithTestCases?: RequirementWithTestCases[]; // Requirements with test cases

  // Inputs for the component
  sut?: Sut;
  version?: Version;
  component?: ComponentModel;
  subcomponent?: SubcomponentModel;
  port?: PortModel;
  private _dataInput?: any;

  // Enums for dropdowns
  testCasePriorities = Object.values(TestCasePriority);
  testCaseStatuses = Object.values(TestCaseStatus);

  // Inputs for search
  searchTerm: string = '';
  filteredTestCases?: TestCase[];

  // Flag to track if there's an unsaved test case
  hasUnsavedTestCase: boolean = false;


  // Event emitter for the action process
  @Output() public actionProcess: EventEmitter<any> = new EventEmitter<any>();

  // Obtain the data input (sut, version, component, subcomponent, port)
  get dataInput(): any | undefined {
    return this._dataInput;
  }
  @Input() set dataInput(value: any | undefined) {
    this._dataInput = value;
    if (value) {
      this.sut = value.sut ? value.sut : null;
      this.version = value.version ? value.version : null;
      this.component = value.component ? value.component : null;
      this.subcomponent = value.subcomponent ? value.subcomponent : null;
      this.port = value.port ? value.port : null;

      this.refresh();
    }
  }

  constructor(
    private _vulnerabilityService: VulnerabilityService,
    private _requirementService: RequirementService,
    private _testCaseService: TestCaseService,
    private _snackBar: MatSnackBar,
  ) {}

  ngOnInit(): void {
    this.refresh();
  }

  refresh() {
    forkJoin({
      testCases: this._testCaseService.getTestCaseList(),
      vulnerabilities: this._vulnerabilityService.getVulnerabilityList(),
      requirements: this._requirementService.getRequirementList()
    }).subscribe({
      next: (results) => {
        this.vulnerabilities = results.vulnerabilities;
        this.requirements = results.requirements;

        // Transform test cases by mapping their properties
        this.testCases = results.testCases.map(testCase => {
          // Create a new TestCase instance with existing properties
          const vulnerabilityId = testCase.vulnerability && 'uuid' in testCase.vulnerability
            ? testCase.vulnerability.uuid
            : undefined;
          const requirementId = testCase.requirement && 'uuid' in testCase.requirement
            ? testCase.requirement.uuid
            : undefined;

          return new TestCase({
            uuid: testCase.uuid,
            name: testCase.name,
            attack_technique: testCase.attack_technique,
            category: testCase.category,
            description: testCase.description,
            priority: testCase.priority,
            recommendations: testCase.recommendations,
            source: testCase.source,
            status: testCase.status,
            vulnerability: testCase.vulnerability ||
                          (vulnerabilityId ?
                            this.vulnerabilities?.find(v => v.uuid === vulnerabilityId) :
                            undefined),
            requirement: testCase.requirement ||
                        (requirementId ?
                          this.requirements?.find(r => r.uuid === requirementId) :
                          undefined),
            testPlan: undefined,
            attachments: testCase.attachments || []
          });
        });

        // Initialize filtered test cases
        this.filteredTestCases = this.testCases;

        // Organize test cases by vulnerability and requirement
        this.organizeTestCasesByVulnerability();
        this.organizeTestCasesByRequirement();
      },
      error: (err) => {
        console.error('Error loading data:', err);
      }
    });
  }

  // Obtain the list of test cases
  getTestCaseList() {
    this._testCaseService.getTestCaseList().subscribe({
      next: (res) => {
        this.testCases = res.map((testCase) => {
          const tc = new TestCase(testCase);
          return tc;
        });
      }
    });
  }

  // Obtain the list of vulnerabilities
  getVulnerabilityList() {
    this._vulnerabilityService.getVulnerabilityList().subscribe({
      next: (res) => {
        this.vulnerabilities = res;
      }
    });
  }

  // Obtain the list of requirements
  getRequirementList() {
    this._requirementService.getRequirementList().subscribe({
      next: (res) => {
        this.requirements = res;
      }
    });
  }

  // Organize test cases by requirement
  organizeTestCasesByRequirement() {
    if (!this.requirements || !this.filteredTestCases) {
      return;
    }

    this.requirementsWithTestCases = this.requirements.map(requirement => {
      // Check if we already have this requirement with expanded state
      const existingReq = this.requirementsWithTestCases?.find(r => r.uuid === requirement.uuid);

      // Filter test cases related to this requirement
      let relatedTestCases = this.filteredTestCases?.filter(tc => {
        return tc.requirement?.uuid === requirement.uuid;
      }).map(tc => {
        // Check if we already have this test case with expanded state
        const existingTC = existingReq?.testCases?.find(t =>
          (t.uuid && t.uuid === tc.uuid) ||
          (!t.uuid && !tc.uuid && (t as TestCaseWithUIData).isNew === true)
        );

        // Determine if this is a new test case
        const isNew = (tc as TestCaseWithUIData).isNew || existingTC?.isNew || false;

        return {
          ...tc,
          // If it's a new test case, always expand it
          expanded: isNew ? true : (existingTC?.expanded || false),
          isNew: isNew
        } as TestCaseWithUIData;
      }) || [];

      // Sort the test cases to put new ones at the top
      relatedTestCases.sort((a, b) => {
        // If a is new and b is not, a comes first
        if ((a as TestCaseWithUIData).isNew && !(b as TestCaseWithUIData).isNew) {
          return -1;
        }
        // If b is new and a is not, b comes first
        if (!(a as TestCaseWithUIData).isNew && (b as TestCaseWithUIData).isNew) {
          return 1;
        }
        // Otherwise, keep the original order
        return 0;
      });

      return {
        ...requirement,
        testCases: relatedTestCases,
        expanded: existingReq?.expanded || false
      };
    });

    // Only show requirements with test cases when filtering
    if (this.searchTerm && this.searchTerm.trim() !== '') {
      this.requirementsWithTestCases = this.requirementsWithTestCases.filter(
        r => r.testCases && r.testCases.length > 0
      );
    }
  }

  // Modify organizeTestCasesByVulnerability to better trace issues
  organizeTestCasesByVulnerability() {
    if (!this.vulnerabilities || !this.filteredTestCases) {
      return;
    }

    // Map through vulnerabilities and filter test cases
    this.vulnerabilitiesWithTestCases = this.vulnerabilities.map(vulnerability => {
      // Check if we already have this vulnerability with expanded state
      const existingVuln = this.vulnerabilitiesWithTestCases?.find(v => v.uuid === vulnerability.uuid);

      let relatedTestCases = this.filteredTestCases?.filter(tc => {
        // Check if the test case is related to the vulnerability
        const isRelated = tc.vulnerability?.uuid === vulnerability.uuid;

        return isRelated;

      }).map(tc => {
        // Check if we already have this test case with expanded state
        const existingTC = existingVuln?.testCases?.find(t =>
          (t.uuid && t.uuid === tc.uuid) ||
          (!t.uuid && !tc.uuid && (t as TestCaseWithUIData).isNew === true)
        );

        // Determine if this is a new test case
        const isNew = (tc as TestCaseWithUIData).isNew || existingTC?.isNew || false;

        return {
          ...tc,
          // If it's a new test case, always expand it
          expanded: isNew ? true : (existingTC?.expanded || false),
          isNew: isNew
        } as TestCaseWithUIData;
      }) || [];

      // Sort the test cases to put new ones at the top
      relatedTestCases.sort((a, b) => {
        // If a is new and b is not, a comes first
        if ((a as TestCaseWithUIData).isNew && !(b as TestCaseWithUIData).isNew) {
          return -1;
        }
        // If b is new and a is not, b comes first
        if (!(a as TestCaseWithUIData).isNew && (b as TestCaseWithUIData).isNew) {
          return 1;
        }
        // Otherwise, keep the original order
        return 0;
      });

      return {
        ...vulnerability,
        testCases: relatedTestCases,
        risk_uuids: vulnerability.risk_uuids,
        expanded: existingVuln?.expanded || false
      };
    });

    // Only show vulnerabilities with test cases when filtering
    if (this.searchTerm && this.searchTerm.trim() !== '') {
      this.vulnerabilitiesWithTestCases = this.vulnerabilitiesWithTestCases.filter(
        v => v.testCases && v.testCases.length > 0
      );
    }
  }

  // Filter vulnerabilities based on selected component, subcomponent or port
  getFilteredVulnerability(): VulnerabilityWithTestCases[] {
    // If no vulnerabilities with test cases, return empty array
    if (!this.vulnerabilitiesWithTestCases) {
      return [];
    }

    // If no component is selected, return all vulnerabilities
    if (!this.component) {
      return this.vulnerabilitiesWithTestCases;
    }

    // No need to sample vulnerabilities anymore

    // Filter vulnerabilities based on selected component, subcomponent or port
    const filtered = this.vulnerabilitiesWithTestCases.filter(vulnerability => {
      // If no subcomponent or port is selected, return all vulnerabilities linked to main component
      if ((this.component !== null) && (this.subcomponent === null) && (this.port === null)) {
        // If vulnerability has no component, don't include it
        if (!vulnerability.component) {
          return false;
        }

        // Get IDs, handling potential undefined and different types
        let vulnCompId, selectedCompId;

        // Check what's actually available in the vulnerability component
        if (typeof vulnerability.component === 'string') {
          vulnCompId = vulnerability.component;
        } else if (vulnerability.component.id) {
          vulnCompId = String(vulnerability.component.id);
        } else if ((vulnerability.component as any).uuid) {
          vulnCompId = String((vulnerability.component as any).uuid);
        } else {
          console.error('Cannot identify component ID in vulnerability:', vulnerability.name);
          return false;
        }

        // Check what's available in the selected component
        if (this.component?.id) {
          selectedCompId = String(this.component?.id);
        } else if ((this.component as any).uuid) {
          selectedCompId = String((this.component as any).uuid);
        } else {
          console.error('Cannot identify ID in selected component');
          return false;
        }


        return vulnCompId === selectedCompId;
      } else {
        // If a subcomponent is selected, filter by subcomponent
        if (this.subcomponent && vulnerability.subcomponent) {
          let vulnSubcompId, selectedSubcompId;

          if (typeof vulnerability.subcomponent === 'string') {
            vulnSubcompId = vulnerability.subcomponent;
          } else if (vulnerability.subcomponent.id) {
            vulnSubcompId = String(vulnerability.subcomponent.id);
          } else if ((vulnerability.subcomponent as any).uuid) {
            vulnSubcompId = String((vulnerability.subcomponent as any).uuid);
          } else {
            return false;
          }

          if (this.subcomponent.id) {
            selectedSubcompId = String(this.subcomponent.id);
          } else if ((this.subcomponent as any).uuid) {
            selectedSubcompId = String((this.subcomponent as any).uuid);
          } else {
            return false;
          }

          return vulnSubcompId === selectedSubcompId;
        } else if (this.port && vulnerability.port) {
          let vulnPortId, selectedPortId;

          if (typeof vulnerability.port === 'string') {
            vulnPortId = vulnerability.port;
          } else if (vulnerability.port.id) {
            vulnPortId = String(vulnerability.port.id);
          } else if ((vulnerability.port as any).uuid) {
            vulnPortId = String((vulnerability.port as any).uuid);
          } else {
            return false;
          }

          if (this.port.id) {
            selectedPortId = String(this.port.id);
          } else if ((this.port as any).uuid) {
            selectedPortId = String((this.port as any).uuid);
          } else {
            return false;
          }

          return vulnPortId === selectedPortId;
        } else {
          return false;
        }
      }
    });

    return filtered;
  }

  // Create a new test case for a vulnerability
  createNewTestCaseForVulnerability(vulnerability: VulnerabilityWithTestCases) {
    // Check if there's already an unsaved test case
    if (this.hasUnsavedTestCase) {
      this._snackBar.open('Please save or cancel the current test case before adding a new one', 'Close', {
        duration: 4000
      });
      return;
    }

    // Create the test case with default values
    const newTestCase = new TestCase({
      name: 'Unnamed',
      attack_technique: '',
      category: '',
      description: '',
      priority: TestCasePriority.P1,
      recommendations: '',
      source: '',
      status: TestCaseStatus.Proposed,
      vulnerability: vulnerability,
      requirement: undefined,
      testPlan: undefined
    });

    // Add custom properties for UI
    const newTestCaseWithUI = {
      ...newTestCase,
      isNew: true, // Mark as a new test case
      expanded: true // Auto-expand the form
    } as TestCaseWithUIData;

    // Add to the test cases array at the beginning
    if (this.testCases) {
      this.testCases.unshift(newTestCaseWithUI); // Add at the beginning instead of the end
    }

    // Set the flag to indicate there's an unsaved test case
    this.hasUnsavedTestCase = true;

    this.organizeTestCasesByVulnerability();

    // Find the vulnerability and set it to expanded
    const vulnWithTC = this.vulnerabilitiesWithTestCases?.find(v => v.uuid === vulnerability.uuid);
    if (vulnWithTC) {
      vulnWithTC.expanded = true;

      // Also make sure the new test case is expanded
      const newTC = vulnWithTC.testCases?.find(tc => (tc as TestCaseWithUIData).isNew === true);
      if (newTC) {
        newTC.expanded = true;
      }
    }
  }

  // Create a new test case for a requirement
  createNewTestCaseForRequirement(requirement: RequirementWithTestCases) {
    // Check if there's already an unsaved test case
    if (this.hasUnsavedTestCase) {
      this._snackBar.open('Please save or cancel the current test case before adding a new one', 'Close', {
        duration: 4000
      });
      return;
    }

    const newTestCase = new TestCase({
      name: 'Unnamed',
      attack_technique: '',
      category: '',
      description: '',
      priority: TestCasePriority.P1,
      recommendations: '',
      source: '',
      status: TestCaseStatus.Proposed,
      vulnerability: undefined,
      requirement: requirement,
      testPlan: undefined
    });

    const newTestCaseWithUI = {
      ...newTestCase,
      isNew: true,
      expanded: true
    } as TestCaseWithUIData;

    if (this.testCases) {
      this.testCases.unshift(newTestCaseWithUI); // Add at the beginning instead of the end
    }

    // Set the flag to indicate there's an unsaved test case
    this.hasUnsavedTestCase = true;

    // Re-organize test cases by requirement
    this.organizeTestCasesByRequirement();

    // Find the requirement and set it to expanded
    const reqWithTC = this.requirementsWithTestCases?.find(r => r.uuid === requirement.uuid);
    if (reqWithTC) {
      reqWithTC.expanded = true;

      // Also make sure the new test case is expanded
      const newTC = reqWithTC.testCases?.find(tc => (tc as TestCaseWithUIData).isNew === true);
      if (newTC) {
        newTC.expanded = true;
      }
    }
  }

  // Save the test case
  saveTestCase(testCase: TestCase) {

    // Check if the test case is valid
    const validationResult = this.validateTestCase(testCase);
    if (!validationResult.isValid && validationResult.error) {
      this._snackBar.open(validationResult.error, 'Close', {
        duration: 4000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    // Create a clean object with just primitive values and IDs for the API
    const apiFormData = {
      name: testCase.name,
      attack_technique: testCase.attack_technique,
      category: testCase.category,
      description: testCase.description,
      priority: testCase.priority,
      recommendations: testCase.recommendations,
      source: testCase.source,
      status: testCase.status,
      vulnerability: testCase.vulnerability?.uuid,
      requirement: testCase.requirement?.uuid,
      test_plan_id: testCase.testPlan,
      attachments: testCase.attachments || []
    };

    if (!testCase.uuid) {
      // Add new test case
      this._testCaseService.addTestCase(apiFormData as any).subscribe({
        next: (res) => {
          // Create a new test case with the UUID returned
          const newTestCase = new TestCase({
            uuid: res.uuid,
            name: testCase.name,
            attack_technique: testCase.attack_technique,
            category: testCase.category,
            description: testCase.description,
            priority: testCase.priority,
            recommendations: testCase.recommendations,
            source: testCase.source,
            status: testCase.status,
            vulnerability: testCase.vulnerability,
            requirement: testCase.requirement,
            testPlan: testCase.testPlan
          });

          // Update the local test cases array
          if (this.testCases) {
            const index = this.testCases.findIndex(tc => tc === testCase);

            if (index !== -1) {
              this.testCases[index] = newTestCase;
            } else {
              this.testCases.push(newTestCase);
            }
          }

          this._snackBar.open('Test case added successfully', 'Close', {
            duration: 3000
          });

          // Collapse the form and clear isNew flag
          if ('expanded' in testCase) {
            (testCase as TestCaseWithUIData).expanded = false;
            (testCase as TestCaseWithUIData).isNew = false;
          }

          // Reset the unsaved test case flag
          this.hasUnsavedTestCase = false;

          // Full refresh to ensure correct data
          this.refresh();
        },
        error: (err) => {
          console.error('Error adding test case:', err);
          this._snackBar.open(`Error adding test case: ${err.message || 'Unknown error'}`, 'Close', {
            duration: 3000
          });
        }
      });
    } else {
      // Update existing test case
      this._testCaseService.updateTestCase(testCase.uuid, apiFormData as any).subscribe({
        next: (res) => {
          const updatedTestCase = new TestCase({
            ...res,
            vulnerability: testCase.vulnerability,
            requirement: testCase.requirement,
            testPlan: testCase.testPlan
          });

          // Update the local test cases array
          const index = this.testCases?.findIndex(tc => tc.uuid === updatedTestCase.uuid);
          if (index !== undefined && index !== -1 && this.testCases) {
            this.testCases[index] = updatedTestCase;
          }

          // Collapse the form after saving and clear isNew flag
          if ('expanded' in testCase) {
            (testCase as TestCaseWithUIData).expanded = false;
            (testCase as TestCaseWithUIData).isNew = false;
          }

          // Reset the unsaved test case flag
          this.hasUnsavedTestCase = false;

          this._snackBar.open('Test case updated successfully', 'Close', {
            duration: 3000
          });

          this.refresh();
        },
        error: (err) => {
          console.error('Error updating test case:', err);
          this._snackBar.open(`Error updating test case`, 'Close', {
            duration: 3000
          });
        }
      });
    }
  }

  // Delete a test case
  deleteTestCase(testCase: TestCase) {
    if (confirm('Are you sure you want to delete this test case?')) {

      // Check if the test case has an UUID
      if (testCase.uuid !== undefined && testCase.uuid !== null) {
        this._testCaseService.deleteTestCase(testCase.uuid).subscribe({
          next: () => {

            // Update the local test cases array
            if (this.testCases) {
              this.testCases = this.testCases.filter(tc => tc.uuid !== testCase.uuid);
            }

            // Re-organize test cases by vulnerability and requirement
            this.organizeTestCasesByVulnerability();
            this.organizeTestCasesByRequirement();

            this._snackBar.open('Test case deleted successfully', 'Close', {
              duration: 3000
            });
          },
          error: (err) => {
            console.error('Error deleting test case:', err);
            this._snackBar.open(`Error deleting test case: ${err.message || 'Unknown error'}`, 'Close', {
              duration: 3000
            });
          }
        });
      } else {
        // For new test cases (without UUID), we need to find them by reference or by isNew flag
        if (this.testCases) {
          // Check if the test case is new
          const isNew = (testCase as TestCaseWithUIData).isNew;

          // Use filter to remove the test case from the local array
          this.testCases = this.testCases.filter(tc => {
            // If it's the exact same object reference
            if (tc === testCase) {
              return false;
            }

            // Or if it's a new test case with the same parent (vulnerability or requirement)
            if ((tc as TestCaseWithUIData).isNew && !tc.uuid) {
              // Check if it has the same parent vulnerability
              if (testCase.vulnerability && tc.vulnerability &&
                  testCase.vulnerability.uuid === tc.vulnerability.uuid) {
                return false;
              }

              // Check if it has the same parent requirement
              if (testCase.requirement && tc.requirement &&
                  testCase.requirement.uuid === tc.requirement.uuid) {
                return false;
              }
            }

            return true;
          });

          // If the test case was new, reset the unsaved test case flag
          if (isNew) {
            this.hasUnsavedTestCase = false;
          }
        }

        // Re-organize test cases by vulnerability and requirement
        this.organizeTestCasesByVulnerability();
        this.organizeTestCasesByRequirement();

        this._snackBar.open('Test case removed', 'Close', {
          duration: 3000
        });
      }
    }
  }

  // Verify that the test case has been completed totally
  validateTestCase(testCase: TestCase): { isValid: boolean; error: string | null } {

    if (!testCase.name || testCase.name.trim() === '') {
      return { isValid: false, error: 'Test Case Name is required' };
    }

    if (!testCase.category || testCase.category.trim() === '') {
      return { isValid: false, error: 'Category is required' };
    }

    if (!testCase.attack_technique || testCase.attack_technique.trim() === '') {
      return { isValid: false, error: 'Attack technique is required' };
    }

    // Not implemented on the backend for the moment
    //if (!testCase.source || testCase.source.trim() === '') {
    //  return { isValid: false, error: 'Source is required' };
    //}

    if (!testCase.description || testCase.description.trim() === '') {
      return { isValid: false, error: 'Description is required' };
    }

    return { isValid: true, error: null };
  }

  // Toggle expanded property for a test case
  toggleExpanded(testCase: TestCaseWithUIData) {
    testCase.expanded = !testCase.expanded;
  }

  // Cancel a new test case
  cancelTestCase(testCase: TestCase) {
    // First, determine if this is a vulnerability or requirement test case
    const isVulnerabilityTestCase = !!testCase.vulnerability;
    const parentUuid = isVulnerabilityTestCase
      ? testCase.vulnerability?.uuid
      : testCase.requirement?.uuid;

    // Remove the test case from the array
    if (this.testCases) {
      this.testCases = this.testCases.filter(tc => tc !== testCase);

      // Re-organize test cases
      this.organizeTestCasesByVulnerability();
      this.organizeTestCasesByRequirement();

      // Find the parent panel and update its test cases
      if (isVulnerabilityTestCase && parentUuid) {
        const vulnWithTC = this.vulnerabilitiesWithTestCases?.find(v => v.uuid === parentUuid);
        if (vulnWithTC && vulnWithTC.testCases) {
          // Remove the test case from the parent's test cases array
          vulnWithTC.testCases = vulnWithTC.testCases.filter(tc => tc !== testCase);
        }
      } else if (parentUuid) {
        const reqWithTC = this.requirementsWithTestCases?.find(r => r.uuid === parentUuid);
        if (reqWithTC && reqWithTC.testCases) {
          // Remove the test case from the parent's test cases array
          reqWithTC.testCases = reqWithTC.testCases.filter(tc => tc !== testCase);
        }
      }

      // Force change detection by triggering a refresh
      setTimeout(() => {
        this.refresh();
      }, 0);

      // Reset the unsaved test case flag
      this.hasUnsavedTestCase = false;

      this._snackBar.open('Test case cancelled', 'Close', {
        duration: 3000
      });
    }
  }

  // Search test cases based on the search term
  searchTestCases() {
    if (!this.searchTerm || this.searchTerm.trim() === '') {
      this.filteredTestCases = this.testCases;
    } else {
      const searchTermLower = this.searchTerm.toLowerCase().trim();
      this.filteredTestCases = this.testCases?.filter(tc =>
        tc.name?.toLowerCase().includes(searchTermLower)
      );
    }

    // Re-organize test cases with the filtered list
    this.organizeTestCasesByVulnerability();
    this.organizeTestCasesByRequirement();
  }

  // Clear search
  clearSearch() {
    this.searchTerm = '';
    this.filteredTestCases = this.testCases;
    this.organizeTestCasesByVulnerability();
    this.organizeTestCasesByRequirement();
  }

  ngOnDestroy(): void {
    this._snackBar.dismiss();
  }
}