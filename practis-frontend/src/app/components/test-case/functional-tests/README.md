# Test Case Component - Functional Tests Documentation

This document describes the functional tests for the Test Case component in the Angular frontend application.


![alt text](test-plan.png)


## Table of Contents

- [Test Case Component - Functional Tests Documentation](#test-case-component---functional-tests-documentation)
  - [Table of Contents](#table-of-contents)
  - [Test Scenarios](#test-scenarios)
    - [1. Viewing Test Cases by Element (ID-1)](#1-viewing-test-cases-by-element-id-1)
    - [2. Viewing Test Cases by Element - No Test Cases (ID-2)](#2-viewing-test-cases-by-element---no-test-cases-id-2)
    - [3. Viewing Test Cases by Element - No Vulnerabilities (ID-3)](#3-viewing-test-cases-by-element---no-vulnerabilities-id-3)
    - [4. Viewing Test Cases by Element - No Requirements (ID-4)](#4-viewing-test-cases-by-element---no-requirements-id-4)
    - [5. Adding a Test Case (ID-5)](#5-adding-a-test-case-id-5)
    - [6. Adding a Test Case - Incomplete Form (ID-6)](#6-adding-a-test-case---incomplete-form-id-6)
    - [7. Modifying a Test Case (ID-7)](#7-modifying-a-test-case-id-7)
    - [8. Deleting a Test Case (ID-8)](#8-deleting-a-test-case-id-8)


## Test Scenarios

### 1. Viewing Test Cases by Element (ID-1)

**Prerequisites:**
- At least one SUT, version, component, vulnerability or requirement, and several test cases must be encoded
- Preferably have a sub-component and a port (not mandatory)

**Steps:**
1. Navigate to the "Tests" section
2. Go to the "Test Cases" sub-category
3. Select an element (component, sub-component, or port)

**Expected Result:**
Test cases linked to the selected element appear, organized by their associated vulnerabilities and requirements.

### 2. Viewing Test Cases by Element - No Test Cases (ID-2)

**Prerequisites:**
- At least one SUT, version, component, vulnerability or requirement must be encoded
- Select an element with no test cases

**Steps:**
1. Navigate to the "Tests" section
2. Go to the "Test Cases" sub-category
3. Select an element (component, sub-component, or port) with no test cases

**Expected Result:**
Vulnerabilities and requirements linked to the element are visible but display the message "No test cases found for this vulnerability / requirement".

### 3. Viewing Test Cases by Element - No Vulnerabilities (ID-3)

**Prerequisites:**
- At least one SUT, version, component must be encoded
- Select an element with no vulnerabilities

**Steps:**
1. Navigate to the "Tests" section
2. Go to the "Test Cases" sub-category
3. Select an element (component, sub-component, or port) with no vulnerabilities

**Expected Result:**
The message "No vulnerabilities found for the selected component / subcomponent / port" is displayed.

### 4. Viewing Test Cases by Element - No Requirements (ID-4)

**Prerequisites:**
- At least one SUT, version, component must be encoded
- Select an element with no requirements

**Steps:**
1. Navigate to the "Tests" section
2. Go to the "Test Cases" sub-category
3. Select an element (component, sub-component, or port) with no requirements

**Expected Result:**
The message "No requirements found for the selected component / subcomponent / port" is displayed.

### 5. Adding a Test Case (ID-5)

**Prerequisites:**
- At least one SUT, version, component, vulnerability or requirement, and several test cases must be encoded

**Steps:**
1. Navigate to the "Tests" section
2. Go to the "Test Cases" sub-category
3. Select an element (component, sub-component, or port)
4. Choose a vulnerability or requirement
5. Select "Add a test case"
6. Fill in the form with:
   - Name: Test Case A
   - Category: Category 2
   - Priority: 2
   - Status: PROPOSED
   - Attack technique: Attack technique
   - Source: https://linux.org
   - Description: Test case A description
7. Click "Save"

**Expected Result:**
A success message "Test case added successfully" appears and the newly created test case appears in the list with the entered data.

### 6. Adding a Test Case - Incomplete Form (ID-6)

**Prerequisites:**
- At least one SUT, version, component, vulnerability or requirement must be encoded

**Steps:**
1. Navigate to the "Tests" section
2. Go to the "Test Cases" sub-category
3. Select an element (component, sub-component, or port)
4. Choose a vulnerability or requirement
5. Select "Add a test case"
6. Fill in the form with:
   - Name: Test Case B
   - Category: Category 3
   - Priority: 3
   - Status: PROPOSED
   - Attack technique: Attack technique
   - Source: https://linux.org
   - Description: (leave empty)
7. Click "Save"

**Expected Result:**
Form validation prevents submission and an error message "Description is required" is displayed.

### 7. Modifying a Test Case (ID-7)

**Prerequisites:**
- At least one SUT, version, component, vulnerability or requirement, and several test cases must be encoded

**Steps:**
1. Navigate to the "Tests" section
2. Go to the "Test Cases" sub-category
3. Select an element (component, sub-component, or port)
4. Choose a vulnerability or requirement with test cases
5. Expand its details using the dropdown menu
6. Click on the pencil icon labeled "Update" on hover
7. Modify the "Category" field to "Category 10"
8. Click "Update" at the end of the form

**Expected Result:**
A success message "Test case updated successfully" appears and the test case is updated with "Category 10" displayed in the Category field.

### 8. Deleting a Test Case (ID-8)

**Prerequisites:**
- At least one SUT, version, component, vulnerability or requirement, and several test cases must be encoded

**Steps:**
1. Navigate to the "Tests" section
2. Go to the "Test Cases" sub-category
3. Select an element (component, sub-component, or port)
4. Choose a vulnerability or requirement with test cases
5. Expand its details using the dropdown menu
6. Click on the trash icon labeled "Delete" on hover
7. Confirm deletion

**Expected Result:**
A success message "Test case deleted successfully" appears and the test case is removed from the list of test cases for the selected vulnerability or requirement.