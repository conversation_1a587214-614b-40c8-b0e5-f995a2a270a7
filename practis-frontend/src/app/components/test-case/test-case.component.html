<div class="content">
  <div class="bottom-section">
    <h2>
      <span class="text-primary">Test Cases</span>
    </h2>

    <!-- Search Bar -->
    <div class="search-container mb-4">
      <mat-form-field appearance="outline" class="search-field">
        <mat-label>Search a test case</mat-label>
        <input matInput [(ngModel)]="searchTerm" placeholder="Search a test case by name"
               (keyup.enter)="searchTestCases()">
        <button *ngIf="searchTerm" matSuffix mat-icon-button aria-label="Clear" (click)="clearSearch()">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>
      <button mat-raised-button color="primary" (click)="searchTestCases()">
        <mat-icon>search</mat-icon> Search
      </button>
    </div>

    <!-- Search results notification when filtering is active -->
    <div *ngIf="searchTerm && searchTerm.trim() !== ''" class="search-results-info mb-3">
      <mat-chip-set>
        <mat-chip color="primary" selected>
          Search results for: "{{searchTerm}}"
          <mat-icon matChipRemove (click)="clearSearch()">cancel</mat-icon>
        </mat-chip>
      </mat-chip-set>
    </div>

    <!-- Vulnerabilities Section -->
    <div class="vulnerabilities-section mb-4">
      <h3>Vulnerabilities</h3>
      <ng-container *ngIf="!getFilteredVulnerability().length">
        <mat-card class="bottom-card cardWithShadow">
          <mat-card-content>
            <div class="no-top-section">
              <h3>No vulnerabilities found for the selected component</h3>
            </div>
          </mat-card-content>
        </mat-card>
      </ng-container>

      <ng-container *ngIf="getFilteredVulnerability().length">
        <mat-accordion>
          <mat-expansion-panel *ngFor="let vulnerabilityWithTC of getFilteredVulnerability()" [expanded]="vulnerabilityWithTC.expanded">
            <mat-expansion-panel-header>
              <mat-panel-title>
                {{vulnerabilityWithTC.name}}
              </mat-panel-title>
              <mat-panel-description>
                {{vulnerabilityWithTC.testCases?.length || 0}} test case{{(vulnerabilityWithTC.testCases?.length || 0) > 1 ? 's' : ''}}
                <button mat-icon-button color="primary"
                        (click)="$event.stopPropagation(); createNewTestCaseForVulnerability(vulnerabilityWithTC)"
                        matTooltip="Add New Test Case">
                  <mat-icon>add_circle</mat-icon>
                </button>
              </mat-panel-description>
            </mat-expansion-panel-header>


            <ng-container *ngIf="vulnerabilityWithTC.testCases?.length; else noTestCases">
              <!-- Table Header -->
              <div class="test-case-table-header">
                <div class="name-col">Name</div>
                <div class="priority-col">Priority</div>
                <div class="status-col">Status</div>
                <div class="action-col">Action</div>
              </div>

              <!-- Table Rows for Vulnerabilities -->
              <div *ngFor="let testCase of vulnerabilityWithTC.testCases" class="test-case-container">
                <!-- Main Row -->
                <div class="test-case-row" (click)="toggleExpanded(testCase)" style="cursor: pointer;">
                  <div class="name-col">
                    <!-- Display name or "Unnamed" if there is no name -->
                    {{ testCase.name || 'Unnamed' }}
                  </div>
                  <div class="priority-col">{{testCase.priority}}</div>
                  <div class="status-col">{{testCase.status}}</div>
                  <div class="action-col">
                    <!-- Show edit/delete buttons only for existing test cases (with UUID) -->
                    <ng-container *ngIf="testCase.uuid">
                      <button mat-icon-button color="primary" matTooltip="Edit Test Case"
                              (click)="$event.stopPropagation(); toggleExpanded(testCase)">
                        <mat-icon>edit</mat-icon>
                      </button>
                      <button mat-icon-button color="warn" matTooltip="Delete Test Case"
                              (click)="$event.stopPropagation(); deleteTestCase(testCase)">
                        <mat-icon>delete</mat-icon>
                      </button>
                    </ng-container>
                    <!-- For temporary cases, just show an indicator -->
                    <ng-container *ngIf="!testCase.uuid">
                      <span class="temporary-indicator" matTooltip="New test case - not saved yet">
                        <mat-icon color="accent">fiber_new</mat-icon>
                      </span>
                    </ng-container>
                    <!-- Expansion indicator -->
                    <mat-icon class="expansion-indicator">{{testCase.expanded ? 'expand_less' : 'expand_more'}}</mat-icon>
                  </div>
                </div>

                <!-- Expanded Details -->
                <div [class.visible]="testCase.expanded" class="test-case-details-container">
                  <mat-card class="bottom-card cardWithShadow mt-2" *ngIf="testCase.expanded">
                    <mat-card-content>
                      <div class="row custom-row">
                        <div class="col-12">
                          <div class="form-group">
                            <div class="field-label">Name</div>
                            <mat-form-field appearance="outline" class="w-100" color="primary">
                              <input matInput [(ngModel)]="testCase.name" required>
                            </mat-form-field>
                          </div>
                        </div>
                      </div>

                      <div class="row custom-row">
                        <div class="col-12 col-sm-4">
                          <div class="form-group">
                            <div class="field-label">Category</div>
                            <mat-form-field appearance="outline" class="w-100" color="primary">
                              <input matInput [(ngModel)]="testCase.category" required>
                            </mat-form-field>
                          </div>
                        </div>
                        <div class="col-12 col-sm-4">
                          <div class="form-group">
                            <div class="field-label">Priority</div>
                            <mat-form-field appearance="outline" class="w-100" color="primary">
                              <mat-select [(ngModel)]="testCase.priority">
                                <mat-option *ngFor="let priority of testCasePriorities" [value]="priority">
                                  {{priority}}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>
                        </div>
                        <div class="col-12 col-sm-4">
                          <div class="form-group">
                            <div class="field-label">Status</div>
                            <mat-form-field appearance="outline" class="w-100" color="primary">
                              <mat-select [(ngModel)]="testCase.status">
                                <mat-option *ngFor="let status of testCaseStatuses" [value]="status">
                                  {{status}}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>
                        </div>
                      </div>
                      <div class="row custom-row mt-2">
                        <div class="col-12 col-sm-4">
                          <div class="form-group">
                            <div class="field-label">Attack technique</div>
                            <mat-form-field appearance="outline" class="w-100" color="primary">
                              <input matInput [(ngModel)]="testCase.attack_technique" required>
                            </mat-form-field>
                          </div>
                        </div>
                        <div class="col-12 col-sm-8">
                          <div class="form-group">
                            <div class="field-label">Source</div>
                            <mat-form-field appearance="outline" class="w-100" color="primary">
                              <input matInput [(ngModel)]="testCase.source" required>
                            </mat-form-field>
                          </div>
                        </div>
                      </div>
                      <div class="row custom-row mt-2">
                        <div class="col-12">
                          <div class="form-group">
                            <div class="field-label">Description</div>
                            <mat-form-field appearance="outline" class="w-100" color="primary">
                              <textarea matInput rows="4" [(ngModel)]="testCase.description" required></textarea>
                            </mat-form-field>
                          </div>
                        </div>
                      </div>
                      <div class="row custom-row mt-3">
                        <div class="col-12 text-right">
                          <!-- Cancel button for new test cases -->
                          <button *ngIf="!testCase.uuid" mat-raised-button color="warn" class="mr-2" (click)="cancelTestCase(testCase)">
                            Cancel
                          </button>
                          <button mat-raised-button color="primary" (click)="saveTestCase(testCase)">
                            {{ !testCase.uuid ? 'Create' : 'Update' }}
                          </button>
                        </div>
                      </div>
                    </mat-card-content>
                  </mat-card>
                </div>
              </div>
            </ng-container>

            <ng-template #noTestCases>
              <div class="no-data-message">
                No test cases found for this vulnerability.
              </div>
            </ng-template>
          </mat-expansion-panel>
        </mat-accordion>
      </ng-container>
    </div>

    <!-- Requirements Section -->
    <div class="requirements-section">
      <h3>Requirements</h3>
      <ng-container *ngIf="!requirementsWithTestCases?.length">
        <mat-card class="bottom-card cardWithShadow">
          <mat-card-content>
            <div class="no-top-section">
              <h3>No requirements found for the selected component.</h3>
            </div>
          </mat-card-content>
        </mat-card>
      </ng-container>

      <ng-container *ngIf="requirementsWithTestCases?.length">
        <mat-accordion>
          <mat-expansion-panel *ngFor="let requirementWithTC of requirementsWithTestCases" [expanded]="requirementWithTC.expanded">
            <mat-expansion-panel-header>
              <mat-panel-title>
                {{requirementWithTC.name}}
              </mat-panel-title>
              <mat-panel-description>
                {{requirementWithTC.testCases?.length || 0}} test case{{(requirementWithTC.testCases?.length || 0) > 1 ? 's' : ''}}

                <button mat-icon-button color="primary"
                        (click)="$event.stopPropagation(); createNewTestCaseForRequirement(requirementWithTC)"
                        matTooltip="Add New Test Case">
                  <mat-icon>add_circle</mat-icon>
                </button>
              </mat-panel-description>
            </mat-expansion-panel-header>


            <ng-container *ngIf="requirementWithTC.testCases?.length; else noTestCasesReq">
              <!-- Table Header -->
              <div class="test-case-table-header">
                <div class="name-col">Name</div>
                <div class="priority-col">Priority</div>
                <div class="status-col">Status</div>
                <div class="action-col">Action</div>
              </div>

              <!-- Table Rows for Requirements -->
              <div *ngFor="let testCase of requirementWithTC.testCases" class="test-case-container">
                <!-- Main Row -->
                <div class="test-case-row" (click)="toggleExpanded(testCase)" style="cursor: pointer;">
                  <div class="name-col">
                    <!-- Display name or "Unnamed" -->
                    {{ testCase.name || 'Unnamed' }}
                  </div>
                  <div class="priority-col">{{testCase.priority}}</div>
                  <div class="status-col">{{testCase.status}}</div>
                  <div class="action-col">
                    <!-- Show edit/delete buttons only for existing test cases (with UUID) -->
                    <ng-container *ngIf="testCase.uuid">
                      <button mat-icon-button color="primary" matTooltip="Edit Test Case"
                              (click)="$event.stopPropagation(); toggleExpanded(testCase)">
                        <mat-icon>edit</mat-icon>
                      </button>
                      <button mat-icon-button color="warn" matTooltip="Delete Test Case"
                              (click)="$event.stopPropagation(); deleteTestCase(testCase)">
                        <mat-icon>delete</mat-icon>
                      </button>
                    </ng-container>
                    <!-- For temporary cases, just show an indicator -->
                    <ng-container *ngIf="!testCase.uuid">
                      <span class="temporary-indicator" matTooltip="New test case - not saved yet">
                        <mat-icon color="accent">fiber_new</mat-icon>
                      </span>
                    </ng-container>
                    <!-- Expansion indicator -->
                    <mat-icon class="expansion-indicator">{{testCase.expanded ? 'expand_less' : 'expand_more'}}</mat-icon>
                  </div>
                </div>

                <!-- Expanded Details -->
                <div [class.visible]="testCase.expanded" class="test-case-details-container">
                  <mat-card class="bottom-card cardWithShadow mt-2" *ngIf="testCase.expanded">
                    <mat-card-content>
                      <div class="row custom-row">
                        <div class="col-12">
                          <div class="form-group">
                            <div class="field-label">Name</div>
                            <mat-form-field appearance="outline" class="w-100" color="primary">
                              <input matInput [(ngModel)]="testCase.name">
                            </mat-form-field>
                          </div>
                        </div>
                      </div>

                      <div class="row custom-row">
                        <div class="col-12 col-sm-4">
                          <div class="form-group">
                            <div class="field-label">Category</div>
                            <mat-form-field appearance="outline" class="w-100" color="primary">
                              <input matInput [(ngModel)]="testCase.category">
                            </mat-form-field>
                          </div>
                        </div>
                        <div class="col-12 col-sm-4">
                          <div class="form-group">
                            <div class="field-label">Priority</div>
                            <mat-form-field appearance="outline" class="w-100" color="primary">
                              <mat-select [(ngModel)]="testCase.priority">
                                <mat-option *ngFor="let priority of testCasePriorities" [value]="priority">
                                  {{priority}}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>
                        </div>
                        <div class="col-12 col-sm-4">
                          <div class="form-group">
                            <div class="field-label">Status</div>
                            <mat-form-field appearance="outline" class="w-100" color="primary">
                              <mat-select [(ngModel)]="testCase.status">
                                <mat-option *ngFor="let status of testCaseStatuses" [value]="status">
                                  {{status}}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>
                        </div>
                      </div>
                      <div class="row custom-row mt-2">
                        <div class="col-12 col-sm-4">
                          <div class="form-group">
                            <div class="field-label">Attack technique</div>
                            <mat-form-field appearance="outline" class="w-100" color="primary">
                              <input matInput [(ngModel)]="testCase.attack_technique">
                            </mat-form-field>
                          </div>
                        </div>
                        <div class="col-12 col-sm-8">
                          <div class="form-group">
                            <div class="field-label">Source</div>
                            <mat-form-field appearance="outline" class="w-100" color="primary">
                              <input matInput [(ngModel)]="testCase.source">
                            </mat-form-field>
                          </div>
                        </div>
                      </div>
                      <div class="row custom-row mt-2">
                        <div class="col-12">
                          <div class="form-group">
                            <div class="field-label">Description</div>
                            <mat-form-field appearance="outline" class="w-100" color="primary">
                              <textarea matInput rows="4" [(ngModel)]="testCase.description"></textarea>
                            </mat-form-field>
                          </div>
                        </div>
                      </div>
                      <div class="row custom-row mt-3">
                        <div class="col-12 text-right">
                          <!-- Cancel button for new test cases -->
                          <button *ngIf="!testCase.uuid" mat-raised-button color="warn" class="mr-2" (click)="cancelTestCase(testCase)">
                            Cancel
                          </button>
                          <button mat-raised-button color="primary" (click)="saveTestCase(testCase)">
                            {{ !testCase.uuid ? 'Create' : 'Update' }}
                          </button>
                        </div>
                      </div>
                    </mat-card-content>
                  </mat-card>
                </div>
              </div>
            </ng-container>

            <ng-template #noTestCasesReq>
              <div class="no-data-message">
                No test cases found for this requirement.
              </div>
            </ng-template>
          </mat-expansion-panel>
        </mat-accordion>
      </ng-container>
    </div>
  </div>
</div>