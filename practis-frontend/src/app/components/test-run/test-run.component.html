<div class="container">
  <!-- Title and version -->
  <div class="header-section d-flex justify-content-between align-items-center mb-4">
    <h2>
      <span class="text-primary">Test Runs</span>
    </h2>
    <div class="d-flex align-items-center">
      <span *ngIf="version?.name" class="version-badge me-4">
        Version: {{version?.name}}
      </span>
    </div>
  </div>

  <!-- Warning message when no test plans are available for the selected version -->
  <div *ngIf="!getFilteredTestPlans().length" class="no-plans">
    <mat-card class="bottom-card cardWithShadow">
      <mat-card-content>
        <div class="no-top-section">
          <h3>No test plans found for the selected version</h3>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Test plans and their test runs -->
  <div *ngIf="getFilteredTestPlans().length || getTestRunsWithoutPlan().length" class="plans-container">
    <mat-accordion>
      <!-- Special panel for test runs without a test plan -->
      <mat-expansion-panel *ngIf="getTestRunsWithoutPlan().length">
        <mat-expansion-panel-header>
          <mat-panel-title>No Test Plan</mat-panel-title>
          <mat-panel-description>
            {{getTestRunsWithoutPlan().length}} test run{{getTestRunsWithoutPlan().length > 1 ? 's' : ''}}
            <button mat-icon-button color="primary"
                    (click)="$event.stopPropagation(); createNewTestRun()"
                    matTooltip="Add New Test Run">
              <mat-icon>add_circle</mat-icon>
            </button>
          </mat-panel-description>
        </mat-expansion-panel-header>

        <!-- Test runs without a test plan -->
        <div class="test-runs">
          <mat-card *ngFor="let run of getTestRunsWithoutPlan()" class="test-run-card" [ngClass]="{'expanded': run.expanded}">
            <mat-card-header (click)="toggleExpanded(run)">
              <mat-card-title>{{ run.name }}</mat-card-title>
              <mat-card-subtitle>
                <div *ngIf="run.start_date">Started: {{ formatDate(run.start_date) }}</div>
                <div *ngIf="run.description" class="description-preview">{{ run.description | slice:0:50 }}{{ run.description.length > 50 ? '...' : '' }}</div>
              </mat-card-subtitle>
              <div class="card-actions">
                <button mat-icon-button color="warn" (click)="$event.stopPropagation(); deleteTestRun(run)" matTooltip="Delete Test Run">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </mat-card-header>
            <mat-card-content *ngIf="run.expanded">
              <!-- Form for the test run -->
              <div class="run-info">
                <mat-form-field appearance="outline" class="w-100 mb-3">
                  <mat-label>Test Run Name</mat-label>
                  <input matInput [(ngModel)]="run.name" placeholder="Enter a name for this test run">
                </mat-form-field>

                <mat-form-field appearance="outline" class="w-100 mb-3">
                  <mat-label>Description</mat-label>
                  <textarea matInput [(ngModel)]="run.description" placeholder="Enter a description for this test run" rows="4"></textarea>
                </mat-form-field>

                <mat-form-field appearance="outline" class="w-100 mb-3">
                  <mat-label>Test Plan (Optional)</mat-label>
                  <mat-select [(ngModel)]="run.plan" [compareWith]="compareTestPlans">
                    <mat-option [value]="null">None</mat-option>
                    <mat-option *ngFor="let plan of getFilteredTestPlans()" [value]="plan">
                      {{ plan.name }}
                    </mat-option>
                  </mat-select>
                  <mat-hint>Selecting a test plan will allow you to easily add its test cases to this run</mat-hint>
                </mat-form-field>

                <div class="row mb-3">
                  <div class="col-md-6">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Start Date</mat-label>
                      <input matInput [matDatepicker]="startPicker" [(ngModel)]="run.start_date" placeholder="Start date">
                      <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
                      <mat-datepicker #startPicker></mat-datepicker>
                    </mat-form-field>
                  </div>
                  <div class="col-md-6">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>End Date</mat-label>
                      <input matInput [matDatepicker]="endPicker" [(ngModel)]="run.end_date" placeholder="End date">
                      <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
                      <mat-datepicker #endPicker></mat-datepicker>
                    </mat-form-field>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-md-12">
                    <button mat-raised-button color="primary" class="w-100" (click)="openTestCaseSelector(run)">
                      <mat-icon>assignment</mat-icon> Manage Test Cases
                    </button>
                  </div>
                </div>
              </div>

              <!-- Test case selection UI -->
              <div class="test-case-selection" *ngIf="run.showTestCaseSelection">
                <!-- Test case selection content (same as in the regular test run cards) -->
                <h3 class="mb-4 text-center">Manage Test Cases for {{ run.name }}</h3>

                <!-- Search bar -->
                <div class="row justify-content-center mb-4">
                  <div class="col-md-10">
                    <div class="search-container">
                      <mat-form-field appearance="outline" class="search-field w-100">
                        <mat-label>Search test cases</mat-label>
                        <input matInput [(ngModel)]="searchTerm" placeholder="Search by name, category, or description"
                               (keyup.enter)="searchTestCases()">
                        <button *ngIf="searchTerm" matSuffix mat-icon-button aria-label="Clear" (click)="clearSearch()">
                          <mat-icon>close</mat-icon>
                        </button>
                        <button matSuffix mat-icon-button aria-label="Search" (click)="searchTestCases()">
                          <mat-icon>search</mat-icon>
                        </button>
                      </mat-form-field>
                    </div>

                    <div *ngIf="isSearching" class="search-results-info mb-3">
                      <mat-chip-set>
                        <mat-chip color="primary" selected>
                          Search results for: "{{searchTerm}}"
                          <button matChipRemove (click)="clearSearch()">
                            <mat-icon>cancel</mat-icon>
                          </button>
                        </mat-chip>
                      </mat-chip-set>
                    </div>
                  </div>
                </div>

                <!-- Test cases drag & drop columns -->
                <div class="row justify-content-center">
                  <!-- Available test cases -->
                  <div class="col-md-5 mb-4 mx-2">
                    <div class="test-case-column">
                      <h4 class="title-with-button">
                        <span class="title-text">
                          <mat-icon class="me-2">list</mat-icon>
                          Available Test Cases
                        </span>
                        <div class="button-group">
                          <button mat-stroked-button color="primary" (click)="toggleSelectAll()" [disabled]="availableTestCases.length === 0 && !allSelected">
                            <mat-icon class="me-1">{{ allSelected ? 'playlist_remove' : 'playlist_add' }}</mat-icon>
                            {{ allSelected ? 'Unselect All' : 'Select All' }}
                          </button>
                          <button *ngIf="run.plan" mat-stroked-button color="accent" (click)="selectAllFromTestPlan()" [disabled]="availableTestCases.length === 0">
                            <mat-icon class="me-1">playlist_add_check</mat-icon>
                            Select Plan Cases
                          </button>
                        </div>
                      </h4>
                      <div
                        cdkDropList
                        #availableList="cdkDropList"
                        [cdkDropListData]="filteredAvailableTestCases"
                        [cdkDropListConnectedTo]="[selectedList]"
                        [cdkDropListSortingDisabled]="true"
                        class="test-case-list"
                        (cdkDropListDropped)="onDrop($event)"
                        id="availableList"
                      >
                        <div
                          *ngFor="let testCase of filteredAvailableTestCases"
                          class="test-case-box"
                          cdkDrag
                          (click)="showTestCaseDetails(testCase)"
                        >
                          <div class="test-case-placeholder" *cdkDragPlaceholder></div>
                          <div class="test-case-content">
                            <div class="test-case-header">
                              <span class="test-case-name">{{ testCase.name }}</span>
                              <span class="test-case-priority" [ngClass]="'priority-' + testCase.priority">
                                P{{ testCase.priority }}
                              </span>
                            </div>
                            <div class="test-case-description">{{ testCase.description | slice:0:50 }}{{ (testCase.description?.length || 0) > 50 ? '...' : '' }}</div>
                          </div>
                        </div>
                        <div *ngIf="filteredAvailableTestCases.length === 0" class="empty-list-message">
                          {{ availableTestCases.length > 0 ? 'No matching test cases found' : 'No more test cases available' }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Selected test cases -->
                  <div class="col-md-5 mb-4 mx-2">
                    <div class="test-case-column">
                      <h4>
                        <mat-icon class="me-2">fact_check</mat-icon>
                        Selected Test Cases
                      </h4>
                      <div
                        cdkDropList
                        #selectedList="cdkDropList"
                        [cdkDropListData]="selectedTestCases"
                        [cdkDropListConnectedTo]="[availableList]"
                        [cdkDropListSortingDisabled]="true"
                        class="test-case-list"
                        (cdkDropListDropped)="onDrop($event)"
                        id="selectedList"
                      >
                        <div
                          *ngFor="let testCase of selectedTestCases"
                          class="test-case-box"
                          cdkDrag
                          (click)="showTestCaseDetails(testCase)"
                        >
                          <div class="test-case-placeholder" *cdkDragPlaceholder></div>
                          <div class="test-case-content">
                            <div class="test-case-header">
                              <span class="test-case-name">{{ testCase.name }}</span>
                              <span class="test-case-priority" [ngClass]="'priority-' + testCase.priority">
                                P{{ testCase.priority }}
                              </span>
                            </div>
                            <div class="test-case-description">{{ testCase.description | slice:0:50 }}{{ (testCase.description?.length || 0) > 50 ? '...' : '' }}</div>
                          </div>
                        </div>
                        <div *ngIf="selectedTestCases.length === 0" class="empty-list-message">
                          Drag test cases here
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Test case details view -->
                <div class="row justify-content-center mb-4" *ngIf="selectedTestCase">
                  <div class="col-md-10">
                    <div class="test-case-details-panel">
                      <h4>Test Case Details</h4>
                      <div class="test-case-details">
                        <div class="detail-row">
                          <strong>Name:</strong> {{ selectedTestCase.name }}
                        </div>
                        <div class="detail-row">
                          <strong>Priority: </strong>
                          <span class="test-case-priority" [ngClass]="'priority-' + selectedTestCase.priority">P{{ selectedTestCase.priority }}</span>
                        </div>
                        <div class="detail-row">
                          <strong>Status:</strong> {{ selectedTestCase.status }}
                        </div>
                        <div class="detail-row">
                          <strong>Category:</strong> {{ selectedTestCase.category }}
                        </div>
                        <div class="detail-row">
                          <strong>Attack Technique:</strong> {{ selectedTestCase.attack_technique }}
                        </div>
                        <div class="detail-row">
                          <strong>Description:</strong>
                          <p class="description-text">{{ selectedTestCase.description }}</p>
                        </div>
                        <div class="detail-row" *ngIf="selectedTestCase.recommendations">
                          <strong>Recommendations:</strong>
                          <p class="description-text">{{ selectedTestCase.recommendations }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Action buttons -->
              <div class="button-row">
                <button mat-raised-button color="warn" class="mr-2" (click)="cancelUpdate(run)">
                  Cancel
                </button>
                <button mat-raised-button color="primary" (click)="saveTestRun(run)">
                  {{ run.uuid ? 'Update' : 'Create' }}
                </button>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-expansion-panel>

      <!-- Regular test plans -->
      <mat-expansion-panel *ngFor="let plan of getFilteredTestPlans()">
        <mat-expansion-panel-header>
          <mat-panel-title>{{ plan.name || 'Unnamed' }}</mat-panel-title>
          <mat-panel-description>
            {{plan.uuid && getTestRunsForPlan(plan.uuid).length || 0}} test run{{(plan.uuid && getTestRunsForPlan(plan.uuid).length || 0) > 1 ? 's' : ''}}
            <button mat-icon-button color="primary"
                    (click)="$event.stopPropagation(); createNewTestRun(plan)"
                    matTooltip="Add New Test Run">
              <mat-icon>add_circle</mat-icon>
            </button>
          </mat-panel-description>
        </mat-expansion-panel-header>

        <!-- Test runs for this test plan -->
        <div class="test-runs">
          <div *ngIf="plan.uuid && !getTestRunsForPlan(plan.uuid).length" class="no-runs">
            No test runs available for this plan.
          </div>

          <mat-card *ngFor="let run of plan.uuid ? getTestRunsForPlan(plan.uuid) : []" class="test-run-card" [ngClass]="{'expanded': run.expanded}">
            <mat-card-header (click)="toggleExpanded(run)">
              <mat-card-title>{{ run.name }}</mat-card-title>
              <mat-card-subtitle>
                <div *ngIf="run.start_date">Started: {{ formatDate(run.start_date) }}</div>
                <div *ngIf="run.description" class="description-preview">{{ run.description | slice:0:50 }}{{ run.description.length > 50 ? '...' : '' }}</div>
              </mat-card-subtitle>
              <div class="card-actions">
                <button mat-icon-button color="warn" (click)="$event.stopPropagation(); deleteTestRun(run)" matTooltip="Delete Test Run">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </mat-card-header>
            <mat-card-content *ngIf="run.expanded">
              <!-- Form for the test run -->
              <div class="run-info">
                <mat-form-field appearance="outline" class="w-100 mb-3">
                  <mat-label>Test Run Name</mat-label>
                  <input matInput [(ngModel)]="run.name" placeholder="Enter a name for this test run">
                </mat-form-field>

                <mat-form-field appearance="outline" class="w-100 mb-3">
                  <mat-label>Description</mat-label>
                  <textarea matInput [(ngModel)]="run.description" placeholder="Enter a description for this test run" rows="4"></textarea>
                </mat-form-field>

                <mat-form-field appearance="outline" class="w-100 mb-3">
                  <mat-label>Test Plan (Optional)</mat-label>
                  <mat-select [(ngModel)]="run.plan" [compareWith]="compareTestPlans">
                    <mat-option [value]="null">None</mat-option>
                    <mat-option *ngFor="let plan of getFilteredTestPlans()" [value]="plan">
                      {{ plan.name }}
                    </mat-option>
                  </mat-select>
                  <mat-hint>Selecting a test plan will allow you to easily add its test cases to this run</mat-hint>
                </mat-form-field>

                <div class="row mb-3">
                  <div class="col-md-6">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>Start Date</mat-label>
                      <input matInput [matDatepicker]="startPicker" [(ngModel)]="run.start_date" placeholder="Start date">
                      <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
                      <mat-datepicker #startPicker></mat-datepicker>
                    </mat-form-field>
                  </div>
                  <div class="col-md-6">
                    <mat-form-field appearance="outline" class="w-100">
                      <mat-label>End Date</mat-label>
                      <input matInput [matDatepicker]="endPicker" [(ngModel)]="run.end_date" placeholder="End date">
                      <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
                      <mat-datepicker #endPicker></mat-datepicker>
                    </mat-form-field>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-md-12">
                    <button mat-raised-button color="primary" class="w-100" (click)="openTestCaseSelector(run)">
                      <mat-icon>assignment</mat-icon> Manage Test Cases
                    </button>
                  </div>
                </div>
              </div>

              <!-- Test case selection UI -->
              <div class="test-case-selection" *ngIf="run.showTestCaseSelection">
                <h3 class="mb-4 text-center">Manage Test Cases for {{ run.name }}</h3>

                <!-- Search bar -->
                <div class="row justify-content-center mb-4">
                  <div class="col-md-10">
                    <div class="search-container">
                      <mat-form-field appearance="outline" class="search-field w-100">
                        <mat-label>Search test cases</mat-label>
                        <input matInput [(ngModel)]="searchTerm" placeholder="Search by name, category, or description"
                               (keyup.enter)="searchTestCases()">
                        <button *ngIf="searchTerm" matSuffix mat-icon-button aria-label="Clear" (click)="clearSearch()">
                          <mat-icon>close</mat-icon>
                        </button>
                        <button matSuffix mat-icon-button aria-label="Search" (click)="searchTestCases()">
                          <mat-icon>search</mat-icon>
                        </button>
                      </mat-form-field>
                    </div>

                    <div *ngIf="isSearching" class="search-results-info mb-3">
                      <mat-chip-set>
                        <mat-chip color="primary" selected>
                          Search results for: "{{searchTerm}}"
                          <button matChipRemove (click)="clearSearch()">
                            <mat-icon>cancel</mat-icon>
                          </button>
                        </mat-chip>
                      </mat-chip-set>
                    </div>
                  </div>
                </div>

                <!-- Test cases drag & drop columns -->
                <div class="row justify-content-center">
                  <!-- Available test cases -->
                  <div class="col-md-5 mb-4 mx-2">
                    <div class="test-case-column">
                      <h4 class="title-with-button">
                        <span class="title-text">
                          <mat-icon class="me-2">list</mat-icon>
                          Available Test Cases
                        </span>
                        <div class="button-group">
                          <button mat-stroked-button color="primary" (click)="toggleSelectAll()" [disabled]="availableTestCases.length === 0 && !allSelected">
                            <mat-icon class="me-1">{{ allSelected ? 'playlist_remove' : 'playlist_add' }}</mat-icon>
                            {{ allSelected ? 'Unselect All' : 'Select All' }}
                          </button>
                          <button *ngIf="run.plan" mat-stroked-button color="accent" (click)="selectAllFromTestPlan()" [disabled]="availableTestCases.length === 0">
                            <mat-icon class="me-1">playlist_add_check</mat-icon>
                            Select Plan Cases
                          </button>
                        </div>
                      </h4>
                      <div
                        cdkDropList
                        #availableList="cdkDropList"
                        [cdkDropListData]="filteredAvailableTestCases"
                        [cdkDropListConnectedTo]="[selectedList]"
                        [cdkDropListSortingDisabled]="true"
                        class="test-case-list"
                        (cdkDropListDropped)="onDrop($event)"
                        id="availableList"
                      >
                        <div
                          *ngFor="let testCase of filteredAvailableTestCases"
                          class="test-case-box"
                          cdkDrag
                          (click)="showTestCaseDetails(testCase)"
                        >
                          <div class="test-case-placeholder" *cdkDragPlaceholder></div>
                          <div class="test-case-content">
                            <div class="test-case-header">
                              <span class="test-case-name">{{ testCase.name }}</span>
                              <span class="test-case-priority" [ngClass]="'priority-' + testCase.priority">
                                P{{ testCase.priority }}
                              </span>
                            </div>
                            <div class="test-case-description">{{ testCase.description | slice:0:50 }}{{ (testCase.description?.length || 0) > 50 ? '...' : '' }}</div>
                          </div>
                        </div>
                        <div *ngIf="filteredAvailableTestCases.length === 0" class="empty-list-message">
                          {{ availableTestCases.length > 0 ? 'No matching test cases found' : 'No more test cases available' }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Selected test cases -->
                  <div class="col-md-5 mb-4 mx-2">
                    <div class="test-case-column">
                      <h4>
                        <mat-icon class="me-2">fact_check</mat-icon>
                        Selected Test Cases
                      </h4>
                      <div
                        cdkDropList
                        #selectedList="cdkDropList"
                        [cdkDropListData]="selectedTestCases"
                        [cdkDropListConnectedTo]="[availableList]"
                        [cdkDropListSortingDisabled]="true"
                        class="test-case-list"
                        (cdkDropListDropped)="onDrop($event)"
                        id="selectedList"
                      >
                        <div
                          *ngFor="let testCase of selectedTestCases"
                          class="test-case-box"
                          cdkDrag
                          (click)="showTestCaseDetails(testCase)"
                        >
                          <div class="test-case-placeholder" *cdkDragPlaceholder></div>
                          <div class="test-case-content">
                            <div class="test-case-header">
                              <span class="test-case-name">{{ testCase.name }}</span>
                              <span class="test-case-priority" [ngClass]="'priority-' + testCase.priority">
                                P{{ testCase.priority }}
                              </span>
                            </div>
                            <div class="test-case-description">{{ testCase.description | slice:0:50 }}{{ (testCase.description?.length || 0) > 50 ? '...' : '' }}</div>
                          </div>
                        </div>
                        <div *ngIf="selectedTestCases.length === 0" class="empty-list-message">
                          Drag test cases here
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Test case details view -->
                <div class="row justify-content-center mb-4" *ngIf="selectedTestCase">
                  <div class="col-md-10">
                    <div class="test-case-details-panel">
                      <h4>Test Case Details</h4>
                      <div class="test-case-details">
                        <div class="detail-row">
                          <strong>Name:</strong> {{ selectedTestCase.name }}
                        </div>
                        <div class="detail-row">
                          <strong>Priority: </strong>
                          <span class="test-case-priority" [ngClass]="'priority-' + selectedTestCase.priority">P{{ selectedTestCase.priority }}</span>
                        </div>
                        <div class="detail-row">
                          <strong>Status:</strong> {{ selectedTestCase.status }}
                        </div>
                        <div class="detail-row">
                          <strong>Category:</strong> {{ selectedTestCase.category }}
                        </div>
                        <div class="detail-row">
                          <strong>Attack Technique:</strong> {{ selectedTestCase.attack_technique }}
                        </div>
                        <div class="detail-row">
                          <strong>Description:</strong>
                          <p class="description-text">{{ selectedTestCase.description }}</p>
                        </div>
                        <div class="detail-row" *ngIf="selectedTestCase.recommendations">
                          <strong>Recommendations:</strong>
                          <p class="description-text">{{ selectedTestCase.recommendations }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Action buttons -->
              <div class="button-row">
                <button mat-raised-button color="warn" class="mr-2" (click)="cancelUpdate(run)">
                  Cancel
                </button>
                <button mat-raised-button color="primary" (click)="saveTestRun(run)">
                  {{ run.uuid ? 'Update' : 'Create' }}
                </button>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-expansion-panel>
    </mat-accordion>
  </div>
</div>
