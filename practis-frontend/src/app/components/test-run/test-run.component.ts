import { Component, OnInit, OnDestroy, Output, Input, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CdkDragDrop, transferArrayItem, CdkDropList, CdkDrag, CdkDragPlaceholder } from '@angular/cdk/drag-drop';

import { MatCardModule } from '@angular/material/card';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatChipsModule } from '@angular/material/chips';

import { Version } from '../../models/version';
import { Sut } from '../../models/sut';
import { ComponentModel } from '../../models/component';
import { SubcomponentModel } from '../../models/subcomponent';
import { PortModel } from '../../models/port';
import { TestPlan } from '../../models/test-plan';
import { TestRun } from '../../models/test-run';
import { TestCase } from '../../models/test-case';
import { TestExecution } from '../../models/test-execution';
import { TestPlanService } from '../../services/test-plan.service';
import { TestRunService } from '../../services/test-run.service';
import { TestCaseService } from '../../services/test-case.service';
import { TestExecutionService } from '../../services/test-execution.service';
import { VersionService } from '../../services/version.service';
import { forkJoin, of } from 'rxjs';

// Interface for TestRun with UI properties
interface TestRunWithUIData extends TestRun {
  expanded?: boolean;
  showTestCaseSelection?: boolean;
  isNew?: boolean;
}

@Component({
  selector: 'app-test-run',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatExpansionModule,
    MatIconModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatOptionModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatChipsModule,
    CdkDropList,
    CdkDrag,
    CdkDragPlaceholder
  ],
  templateUrl: './test-run.component.html',
  styleUrls: ['./test-run.component.scss']
})
export class TestRunComponent implements OnInit, OnDestroy {

  // Base variables
  testPlans: TestPlan[] = [];
  testRuns: TestRunWithUIData[] = [];

  // Test case management
  availableTestCases: TestCase[] = [];
  filteredAvailableTestCases: TestCase[] = [];
  selectedTestCases: TestCase[] = [];
  selectedTestCase?: TestCase;
  currentTestRun?: TestRunWithUIData;
  testExecutions: TestExecution[] = [];

  // Search functionality
  searchTerm: string = '';
  isSearching: boolean = false;
  allSelected: boolean = false;

  // Context properties
  sut?: Sut;
  version?: Version;
  component?: ComponentModel;
  subcomponent?: SubcomponentModel;
  port?: PortModel;
  private _dataInput?: any;

  // Output events
  @Output() actionProcess: EventEmitter<any> = new EventEmitter<any>();

  // Obtain the data input (sut, version, component, subcomponent, port)
  get dataInput(): any | undefined {
    return this._dataInput;
  }

  // Getter and Setter for dataInput
  @Input() set dataInput(value: any | undefined) {
    this._dataInput = value;

    if (value) {
      this.sut = value.sut ? value.sut : null;
      let versionId: string | undefined;

      // Get the version from different sources
      if (value.version) {
        this.version = value.version;
        this.refresh();
      }
      // Check if there is a component
      else if (value.component) {

        // Get the version from the component
        if (value.component.version) {
          this.version = value.component.version;
          this.refresh();
        } else if (value.component.version_id) {
          this._versionService.getVersionById(value.component.version_id).subscribe({
            next: (versionData) => {
              this.version = versionData;
              this.refresh();
            },
            error: (err: any) => {
              console.error('Error fetching version details:', err);
            }
          });
        }
      }
      // Check if there is a subcomponent
      else if (value.subcomponent) {

        // Get the version from the subcomponent
        if (value.subcomponent.version) {
          this.version = value.subcomponent.version;
          this.refresh();
        }
        else if (value.subcomponent.component && value.subcomponent.component.version) {
          this.version = value.subcomponent.component.version;
          this.refresh();
        }
        else if (value.subcomponent.component && value.subcomponent.component.version_id) {
          this._versionService.getVersionById(value.subcomponent.component.version_id).subscribe({
            next: (versionData) => {
              this.version = versionData;
              this.refresh();
            },
            error: (err: any) => {
              console.error('Error fetching version details:', err);
            }
          });
        }
      }
      // Check if there is a port
      else if (value.port && value.port.subcomponent && value.port.subcomponent.component) {

        // Get the version from the port
        if (value.port.subcomponent.component.version) {
          this.version = value.port.subcomponent.component.version;
          this.refresh();
        }
        else if (value.port.subcomponent.component.version_id) {
          this._versionService.getVersionById(value.port.subcomponent.component.version_id).subscribe({
            next: (versionData) => {
              this.version = versionData;
              this.refresh();
            },
            error: (err: any) => {
              console.error('Error fetching version details:', err);
            }
          });
        }
      }

      // Set the component, subcomponent, and port
      this.component = value.component ? value.component : null;
      this.subcomponent = value.subcomponent ? value.subcomponent : null;
      this.port = value.port ? value.port : null;
    }
  }
  // Constructor
  constructor(
    private _testPlanService: TestPlanService,
    private _testRunService: TestRunService,
    private _testCaseService: TestCaseService,
    private _testExecutionService: TestExecutionService,
    private _versionService: VersionService,
    private _snackBar: MatSnackBar
  ) {}

  /**
   * Initializes the component and loads initial test plans and test runs
   */
    ngOnInit() {
    this.refresh();
  }

  /**
   * Loads test plans and test runs from the backend service
   */
  refresh() {
    forkJoin({
      testPlans: this._testPlanService.getTestPlanList(),
      testRuns: this._testRunService.getTestRunList()
    }).subscribe({
      next: (results) => {
        this.testPlans = results.testPlans;
        this.testRuns = results.testRuns.map(run => {
          // Parse dates from API
          return {
            ...run,
            expanded: false,
            // Ensure dates are properly parsed as Date objects
            start_date: run.start_date ? new Date(run.start_date) : undefined,
            end_date: run.end_date ? new Date(run.end_date) : undefined
          } as TestRunWithUIData;
        });
      },
      error: (err: any) => {
        console.error('Error loading data:', err);
        this._snackBar.open('Error loading test runs', 'Close', {
          duration: 3000
        });
      }
    });
  }

  /**
   * Creates a new test run with default values
   * and automatically opens it for editing
   * @param testPlan Optional test plan to associate with the new test run
   */
  createNewTestRun(testPlan?: TestPlan) {
    // Check if a version is selected
    if (!this.version || !this.version.uuid) {
      this._snackBar.open('Cannot create test run: No valid version selected', 'Close', {
        duration: 4000
      });
      return;
    }

    // Check if there's already an unsaved test run
    const hasUnsavedTestRun = this.testRuns.some(tr => tr.isNew === true);
    if (hasUnsavedTestRun) {
      this._snackBar.open('Please save or cancel the current test run before adding a new one', 'Close', {
        duration: 4000
      });
      return;
    }

    // Create a new test run with default values
    // Get today's date without time components
    const today = new Date();
    const todayDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    const newTestRun = new TestRun({
      name: 'Unnamed',
      description: '',
      start_date: todayDate,
      version: this.version, // Add the current version
      plan: testPlan ? testPlan : undefined
    });

    // Add the test run to the list
    const newTestRunWithUI = {
      ...newTestRun,
      expanded: true,
      isNew: true
    } as TestRunWithUIData;

    // Add the new test run to the beginning of the array
    this.testRuns.unshift(newTestRunWithUI);

    // Automatically open the new panel
    setTimeout(() => {
      const panels = document.querySelectorAll('mat-expansion-panel');
      if (panels && panels.length > 0) {
        const firstPanel = panels[0] as any;
        if (firstPanel && firstPanel.open) {
          firstPanel.open();
        }
      }
    }, 100);
  }

  /**
   * Saves a test run - either creates a new one or updates an existing one
   * @param testRun The test run to save
   */
  saveTestRun(testRun: TestRunWithUIData) {
    // Check if a version is selected
    if (!this.version || !this.version.uuid) {
      this._snackBar.open('Cannot save test run: No valid version selected', 'Close', {
        duration: 4000
      });
      return;
    }

    // Validate test run data
    const validationResult = this.validateTestRun(testRun);
    if (!validationResult.isValid && validationResult.error) {
      this._snackBar.open(validationResult.error, 'Close', {
        duration: 3000
      });
      return;
    }

    // Check if the test plan is new or existing with uuid
    if (!testRun.uuid) {
      // Format dates for API
      const formattedStartDate = testRun.start_date ? this.formatDateForAPI(testRun.start_date) : null;
      const formattedEndDate = testRun.end_date ? this.formatDateForAPI(testRun.end_date) : null;

      // Use a type assertion to any to avoid TypeScript errors
      const createData: any = {
        name: testRun.name,
        description: testRun.description,
        version: this.version.uuid, // Add the current version UUID
        plan: testRun.plan ? (typeof testRun.plan === 'string' ? testRun.plan : testRun.plan.uuid) : null,
        start_date: formattedStartDate,
        end_date: formattedEndDate
      };

      console.log('Creating test run with plan:', testRun.plan);

      // Create a new test run
      this._testRunService.addTestRun(createData).subscribe({
        next: (res: any) => {
          // Update local the test runs array
          const updatedRun: TestRunWithUIData = {
            uuid: res.uuid,
            name: testRun.name,
            description: testRun.description,
            // Parse dates from string if they come back as strings
            start_date: testRun.start_date ? new Date(testRun.start_date) : undefined,
            end_date: testRun.end_date ? new Date(testRun.end_date) : undefined,
            version: this.version, // Add the current version
            plan: testRun.plan,
            expanded: true
          };

          const index = this.testRuns.findIndex(run => run === testRun);
          if (index !== -1) {
            this.testRuns[index] = updatedRun;
          }

          // Clear the isNew flag
          (testRun as TestRunWithUIData).isNew = false;

          // Close test case selection if it was open
          if (this.currentTestRun === testRun) {
            (testRun as TestRunWithUIData).showTestCaseSelection = false;
            this.currentTestRun = undefined;
            this.selectedTestCase = undefined;
          }

          this._snackBar.open('Test run created successfully', 'Close', {
            duration: 3000
          });

          // Refresh to update the UI with the new test run
          // This will move the test run to the correct group if the plan was changed
          this.refresh();
        },
        error: (err: any) => {
          console.error('Error creating test run:', err);
          let errorMsg = 'Unknown error';
          if (err.error) {
            errorMsg = typeof err.error === 'object' ? JSON.stringify(err.error) : err.error;
          } else if (err.message) {
            errorMsg = err.message;
          }
          this._snackBar.open(`Error creating test run: ${errorMsg}`, 'Close', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    } else {
      const formattedStartDate = testRun.start_date ? this.formatDateForAPI(testRun.start_date) : null;
      const formattedEndDate = testRun.end_date ? this.formatDateForAPI(testRun.end_date) : null;

      // Use a type assertion to any to avoid TypeScript errors
      const updateData: any = {
        name: testRun.name,
        description: testRun.description,
        version: this.version.uuid, // Add the current version UUID
        plan: testRun.plan ? (typeof testRun.plan === 'string' ? testRun.plan : testRun.plan.uuid) : null,
        start_date: formattedStartDate,
        end_date: formattedEndDate
      };

      console.log('Updating test run with plan:', testRun.plan);

      this._testRunService.updateTestRun(testRun.uuid, updateData).subscribe({
        next: (_: any) => {
          // Close test case selection if it was open
          if (this.currentTestRun === testRun) {
            (testRun as TestRunWithUIData).showTestCaseSelection = false;
            this.currentTestRun = undefined;
            this.selectedTestCase = undefined;
          }

          this._snackBar.open('Test run updated successfully', 'Close', {
            duration: 3000
          });

          // Refresh to update the UI with the updated test run
          // This will move the test run to the correct group if the plan was changed
          this.refresh();
        },
        error: (err: any) => {
          console.error('Error updating test run:', err);
          let errorMsg = 'Unknown error';
          if (err.error) {
            errorMsg = typeof err.error === 'object' ? JSON.stringify(err.error) : err.error;
          } else if (err.message) {
            errorMsg = err.message;
          }
          this._snackBar.open(`Error updating test run: ${errorMsg}`, 'Close', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  /**
   * Delete a test run.
   * @param testRun The test run to delete
   */
  deleteTestRun(testRun: TestRunWithUIData) {
    if (confirm('Are you sure you want to delete this test run ?')) {
      if (testRun.uuid) {
        this._testRunService.deleteTestRun(testRun.uuid).subscribe({
          next: () => {
            this.testRuns = this.testRuns.filter(run => run.uuid !== testRun.uuid);

            this._snackBar.open('Test run deleted successfully', 'Close', {
              duration: 3000
            });
          },
          error: (err: any) => {
            console.error('Error deleting test run:', err);
            this._snackBar.open(`Error deleting test run: ${err.message || 'Unknown error'}`, 'Close', {
              duration: 3000
            });
          }
        });
      } else {
        this.testRuns = this.testRuns.filter(run => run !== testRun);
        this._snackBar.open('Test run removed', 'Close', {
          duration: 3000
        });
      }
    }
  }


  // Toggle expanded property for a test run
  toggleExpanded(testRun: TestRunWithUIData) {
    testRun.expanded = !testRun.expanded;
  }

  // Cancel the update of a test run
  cancelUpdate(testRun: TestRunWithUIData) {
    // If the test run is new and has no UUID, remove it from the list
    if (!testRun.uuid) {
      this.testRuns = this.testRuns.filter(run => run !== testRun);
    } else {
      // If the test run has test case selection open, close it
      if (testRun.showTestCaseSelection) {
        testRun.showTestCaseSelection = false;
        this.currentTestRun = undefined;
        this.selectedTestCase = undefined;
      }
      this.refresh();
    }
  }

  /**
   * Validate the test run data
   * @param testRun The test run data to validate
   * @returns An object containing the validation result and error message (if any)
   */
  validateTestRun(testRun: TestRun): { isValid: boolean; error: string | null } {
    // Validate required fields
    if (!testRun.name || testRun.name.trim() === '') {
      return { isValid: false, error: 'Test Run Name is required' };
    }

    // Validate date range if both dates are provided
    if (testRun.start_date && testRun.end_date) {
      // Create date objects without time components to compare dates only
      const startDate = new Date(testRun.start_date);
      const endDate = new Date(testRun.end_date);

      // Reset time components to midnight for proper date comparison
      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());

      if (endDateOnly < startDateOnly) {
        return { isValid: false, error: 'End date cannot be before start date' };
      }
    }

    return { isValid: true, error: null };
  }

  // Get filtered test plans based on the selected version
  getFilteredTestPlans(): TestPlan[] {
    if (!this.testPlans || !this.testPlans.length) {
      return [];
    }

    // If no version is selected, return all test plans
    if (!this.version || !this.version.uuid) {
      return this.testPlans;
    }


    let filteredPlans = this.testPlans.filter(testPlan => {
      if (!testPlan.uuid) {
        return true;
      }

      if (!testPlan.version) {
        return true;
      }

      if (typeof testPlan.version === 'string') {
        return testPlan.version === this.version?.uuid;
      } else if (testPlan.version.uuid) {
        return testPlan.version.uuid === this.version?.uuid;
      }

      return true;
    });

    return filteredPlans;
  }

  /**
   * Get filtered test runs based on the selected version and test plans
   * @returns Array of filtered test runs
   */
  getFilteredTestRuns(): TestRunWithUIData[] {
    if (!this.testRuns) {
      return [];
    }

    // If no version is selected, return all test runs
    if (!this.version || !this.version.uuid) {
      return [];
    }

    // Get the filtered test plans
    const filteredPlans = this.getFilteredTestPlans();

    return this.testRuns.filter(run => {
      // First, check if the test run has the correct version
      if (run.version) {
        const runVersionId = typeof run.version === 'string' ? run.version : (run.version.uuid || '');
        if (runVersionId !== this.version?.uuid) {
          return false;
        }
      } else {
        // If no version is specified, skip this test run
        return false;
      }

      // If the test run has no plan, include it (it will go in the "No Test Plan" group)
      if (!run.plan) {
        return true;
      }

      // If the test run has a plan, check if it's in the filtered plans
      const planId = typeof run.plan === 'string' ? run.plan : (run.plan.uuid || '');
      return filteredPlans.some(plan => plan.uuid === planId);
    });
  }

  /**
   * Get filtered test runs based on the test plan ID
   * @param planId The UUID of the test plan to filter by, or 'no-plan' for test runs without a plan
   * @returns Array of test runs associated with the specified plan
   */
  getTestRunsForPlan(planId: string): TestRunWithUIData[] {
    if (!this.testRuns || !this.testRuns.length) {
      return [];
    }

    // Special case for test runs without a plan
    if (planId === 'no-plan') {
      return this.getTestRunsWithoutPlan();
    }

    const filteredRuns = this.testRuns.filter(run => {
      // Check if the test run has a plan, if not, return false
      if (!run.plan) {
        return false;
      }

      // Handle both string UUID and object reference
      if (typeof run.plan === 'string') {
        return run.plan === planId;
      } else if (run.plan.uuid) {
        return run.plan.uuid === planId;
      }

      return false;
    });

    return filteredRuns;
  }

  /**
   * Get test runs that don't have an associated test plan
   * @returns Array of test runs without a test plan
   */
  getTestRunsWithoutPlan(): TestRunWithUIData[] {
    if (!this.testRuns || !this.testRuns.length) {
      return [];
    }

    // Filter for test runs with the correct version and no plan
    return this.testRuns.filter(run => {
      // Check if the test run has the correct version
      if (run.version) {
        const runVersionId = typeof run.version === 'string' ? run.version : (run.version.uuid || '');
        if (runVersionId !== this.version?.uuid) {
          return false;
        }
      } else {
        return false;
      }

      // Return true if the test run has no plan
      return !run.plan;
    });
  }

  /**
   * Format a date for display (DD-MM-YYYY)
   * @param date The date to format
   * @returns Formatted date string
   */
  formatDate(date?: Date | string): string {
    if (!date) {
      return 'N/A';
    }

    const d = new Date(date);
    // Add timezone offset to ensure the date is displayed correctly
    const localDate = new Date(d.getTime() + d.getTimezoneOffset() * 60000);
    return `${localDate.getDate().toString().padStart(2, '0')}-${(localDate.getMonth() + 1).toString().padStart(2, '0')}-${localDate.getFullYear()}`;
  }

  /**
   * Format a date for API (YYYY-MM-DD)
   * @param date The date to format
   * @returns Formatted date string
   */
  formatDateForAPI(date: Date | string): string {
    // If it's already a string in YYYY-MM-DD format, return it
    if (typeof date === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(date)) {
      return date;
    }

    // If it's an ISO string, extract the date part
    if (typeof date === 'string' && date.includes('T')) {
      const datePart = date.split('T')[0];
      if (/^\d{4}-\d{2}-\d{2}$/.test(datePart)) {
        return datePart;
      }
    }

    // Otherwise, convert to Date and format
    const d = new Date(date);

    // Format as YYYY-MM-DD
    const year = d.getFullYear();
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const day = d.getDate().toString().padStart(2, '0');

    const result = `${year}-${month}-${day}`;
    return result;
  }

  /**
   * Opens the test case selector for a test run
   * @param testRun The test run to manage test cases for
   */
  openTestCaseSelector(testRun: TestRunWithUIData) {
    // Check if we're already managing test cases for this test run
    if (this.currentTestRun === testRun && testRun.showTestCaseSelection) {
      testRun.showTestCaseSelection = false;
      this.currentTestRun = undefined;
      this.selectedTestCase = undefined;
      return;
    }

    // Close any other open test case selectors
    if (this.currentTestRun && this.currentTestRun !== testRun) {
      this.currentTestRun.showTestCaseSelection = false;
    }

    // Set the current test run and show the test case selection
    this.currentTestRun = testRun;
    testRun.showTestCaseSelection = true;

    // Reset test case lists
    this.selectedTestCases = [];
    this.availableTestCases = [];
    this.filteredAvailableTestCases = [];
    this.selectedTestCase = undefined;
    this.searchTerm = '';
    this.isSearching = false;
    this.allSelected = false;

    // Important: Reset the test executions array to avoid mixing executions from different test runs
    this.testExecutions = [];

    // If the test run has a plan but it's a string ID, fetch the actual plan
    if (testRun.plan && typeof testRun.plan === 'string') {
      this._testPlanService.getTestPlanById(testRun.plan).subscribe({
        next: (plan) => {
          testRun.plan = plan;
          // Load test cases and test executions
          this.loadTestCasesForTestRun(testRun);
        },
        error: (err) => {
          console.error('Error fetching test plan:', err);
          // Still load test cases even if plan fetch fails
          this.loadTestCasesForTestRun(testRun);
        }
      });
    } else {
      // Load test cases and test executions
      this.loadTestCasesForTestRun(testRun);
    }
  }

  /**
   * Loads test cases for a test run
   * @param testRun The test run to load test cases for
   */
  loadTestCasesForTestRun(testRun: TestRunWithUIData) {
    // First, get all test cases related to the current component, subcomponent, or port
    forkJoin({
      testCases: this._testCaseService.getTestCaseList(),
      testExecutions: testRun.uuid ? this._testExecutionService.getTestExecutionsForRun(testRun.uuid) : of([])
    }).subscribe({
      next: (results) => {
        // Filter test cases based on component, subcomponent, or port
        const allTestCases = results.testCases;

        // Get test cases from the test plan if available
        let testPlanCases: TestCase[] = [];
        if (testRun.plan) {
          if (typeof testRun.plan !== 'string') {
            testPlanCases = testRun.plan.test_cases || [];
            console.log('Found test plan with test cases:', testPlanCases.length);
          } else {
            console.log('Test plan is a string ID, not an object:', testRun.plan);
          }
        }

        // Get test cases that are already part of the test run via test executions
        const testExecutions = results.testExecutions || [];
        // Store only the test executions for the current test run
        this.testExecutions = testExecutions.filter(te => {
          const teRunId = typeof te.test_run === 'string' ? te.test_run : te.test_run?.uuid;
          return teRunId === testRun.uuid;
        });

        // Extract test case IDs from test executions (using the filtered list)
        const testCaseIdsInExecutions = this.testExecutions.map(te => {
          if (typeof te.test_case === 'string') {
            return te.test_case;
          } else if (te.test_case && te.test_case.uuid) {
            return te.test_case.uuid;
          }
          return null;
        }).filter(id => id !== null) as string[];

        // Find the actual test case objects for the selected test cases
        this.selectedTestCases = allTestCases.filter(tc =>
          testCaseIdsInExecutions.includes(tc.uuid as string)
        );

        // Filter available test cases to exclude those already selected
        const selectedIds = this.selectedTestCases.map(tc => tc.uuid);

        // Filter test cases based on component, subcomponent, or port
        this.availableTestCases = allTestCases.filter(tc => {
          // Skip if already selected
          if (selectedIds.includes(tc.uuid)) {
            return false;
          }

          // Include if in the test plan
          if (testPlanCases.some(ptc => {
            if (typeof ptc === 'string') {
              return ptc === tc.uuid;
            } else {
              return ptc.uuid === tc.uuid;
            }
          })) {
            return true;
          }

          // Include if related to the current component, subcomponent, or port
          if (tc.vulnerability) {
            const vuln = tc.vulnerability;

            // Check component
            if (this.component && vuln.component) {
              const vulnCompId = typeof vuln.component === 'string' ? vuln.component :
                                ((vuln.component as any).uuid || (vuln.component as any).id);
              const compId = (this.component as any).uuid || (this.component as any).id;
              if (vulnCompId === compId) {
                return true;
              }
            }

            // Check subcomponent
            if (this.subcomponent && vuln.subcomponent) {
              const vulnSubcompId = typeof vuln.subcomponent === 'string' ? vuln.subcomponent :
                                   ((vuln.subcomponent as any).uuid || (vuln.subcomponent as any).id);
              const subcompId = (this.subcomponent as any).uuid || (this.subcomponent as any).id;
              if (vulnSubcompId === subcompId) {
                return true;
              }
            }

            // Check port
            if (this.port && vuln.port) {
              const vulnPortId = typeof vuln.port === 'string' ? vuln.port :
                               ((vuln.port as any).uuid || (vuln.port as any).id);
              const portId = (this.port as any).uuid || (this.port as any).id;
              if (vulnPortId === portId) {
                return true;
              }
            }
          }

          return false;
        });

        // Update filtered available test cases
        this.filteredAvailableTestCases = [...this.availableTestCases];
      },
      error: (err) => {
        console.error('Error loading test cases:', err);
        this._snackBar.open('Error loading test cases', 'Close', {
          duration: 3000
        });
      }
    });
  }

  /**
   * Handles the drop event for drag and drop
   * @param event The drop event
   */
  onDrop(event: CdkDragDrop<TestCase[]>) {
    if (event.previousContainer === event.container) {
      // Same container, no need to do anything
      return;
    }

    // Move the item between containers
    transferArrayItem(
      event.previousContainer.data,
      event.container.data,
      event.previousIndex,
      event.currentIndex,
    );

    // If we're moving to the selected container, create a test execution
    if (event.container.id === 'selectedList' && this.currentTestRun?.uuid) {
      const testCase = event.container.data[event.currentIndex];
      this.createTestExecution(testCase, this.currentTestRun);
    }

    // If we're moving to the available container, delete the test execution
    if (event.container.id === 'availableList' && this.currentTestRun?.uuid) {
      const testCase = event.container.data[event.currentIndex];
      this.deleteTestExecutionForTestCase(testCase, this.currentTestRun);
    }
  }

  /**
   * Creates a test execution for a test case and test run
   * @param testCase The test case to create an execution for
   * @param testRun The test run to create an execution for
   */
  createTestExecution(testCase: TestCase, testRun: TestRun) {
    if (!testRun.uuid || !testCase.uuid) {
      console.error('Cannot create test execution: Missing test run or test case UUID');
      return;
    }

    const newExecution = new TestExecution({
      test_case: testCase.uuid,
      test_run: testRun.uuid,
      status: 'IDLE',
      date: new Date(),
      automated: false,
      execution_path: '',
      source: ''
    });

    this._testExecutionService.addTestExecution(newExecution).subscribe({
      next: (res) => {
        // Add the new execution to the list, but only if it's for the current test run
        // This prevents mixing test executions from different test runs
        if (this.currentTestRun && this.currentTestRun.uuid === testRun.uuid) {
          this.testExecutions.push({
            ...res,
            test_case: testCase,
            test_run: testRun
          });
        }
      },
      error: (err) => {
        console.error('Error creating test execution:', err);
        this._snackBar.open('Error creating test execution', 'Close', {
          duration: 3000
        });
      }
    });
  }

  /**
   * Deletes a test execution for a test case and test run
   * @param testCase The test case to delete the execution for
   * @param testRun The test run to delete the execution for
   */
  deleteTestExecutionForTestCase(testCase: TestCase, testRun: TestRun) {
    if (!testRun.uuid || !testCase.uuid) {
      console.error('Cannot delete test execution: Missing test run or test case UUID');
      return;
    }

    // Find the test execution for this test case and test run
    // Make sure we're only looking at test executions for the current test run
    const execution = this.testExecutions.find(te => {
      const teCaseId = typeof te.test_case === 'string' ? te.test_case : te.test_case?.uuid;
      const teRunId = typeof te.test_run === 'string' ? te.test_run : te.test_run?.uuid;
      return teCaseId === testCase.uuid && teRunId === testRun.uuid;
    });

    if (!execution || !execution.uuid) {
      console.error('Cannot find test execution to delete');
      return;
    }

    this._testExecutionService.deleteTestExecution(execution.uuid).subscribe({
      next: () => {
        // Remove the execution from the list
        this.testExecutions = this.testExecutions.filter(te => te.uuid !== execution.uuid);
      },
      error: (err) => {
        console.error('Error deleting test execution:', err);
        this._snackBar.open('Error deleting test execution', 'Close', {
          duration: 3000
        });
      }
    });
  }

  /**
   * Toggles selection of all available test cases
   */
  toggleSelectAll() {
    if (this.allSelected) {
      // Move all selected test cases back to available
      this.availableTestCases = [...this.availableTestCases, ...this.selectedTestCases];

      // Delete all test executions for the current test run
      if (this.currentTestRun?.uuid) {
        this.selectedTestCases.forEach(tc => {
          this.deleteTestExecutionForTestCase(tc, this.currentTestRun!);
        });
      }

      this.selectedTestCases = [];
      this.allSelected = false;
    } else {
      // Move all available test cases to selected
      this.selectedTestCases = [...this.selectedTestCases, ...this.filteredAvailableTestCases];

      // Create test executions for all newly selected test cases
      if (this.currentTestRun?.uuid) {
        this.filteredAvailableTestCases.forEach(tc => {
          this.createTestExecution(tc, this.currentTestRun!);
        });
      }

      this.availableTestCases = this.availableTestCases.filter(tc =>
        !this.filteredAvailableTestCases.some(ftc => ftc.uuid === tc.uuid)
      );
      this.filteredAvailableTestCases = [];
      this.allSelected = true;
    }
  }

  /**
   * Selects all test cases from the test plan
   */
  selectAllFromTestPlan() {
    if (!this.currentTestRun?.plan || typeof this.currentTestRun.plan === 'string') {
      this._snackBar.open('No test plan associated with this test run', 'Close', {
        duration: 3000
      });
      return;
    }

    const testPlan = this.currentTestRun.plan;
    if (!testPlan.test_cases || !testPlan.test_cases.length) {
      this._snackBar.open('No test cases in the associated test plan', 'Close', {
        duration: 3000
      });
      return;
    }

    // Get test cases from the test plan
    const testPlanCaseIds = testPlan.test_cases.map(tc => {
      if (typeof tc === 'string') {
        return tc;
      } else if (tc.uuid) {
        return tc.uuid;
      }
      return null;
    }).filter(id => id !== null) as string[];

    // Find test cases in available test cases that are in the test plan
    const testPlanCasesToSelect = this.availableTestCases.filter(tc =>
      testPlanCaseIds.includes(tc.uuid as string)
    );

    // Move these test cases to selected
    this.selectedTestCases = [...this.selectedTestCases, ...testPlanCasesToSelect];

    // Create test executions for all newly selected test cases
    if (this.currentTestRun?.uuid) {
      testPlanCasesToSelect.forEach(tc => {
        this.createTestExecution(tc, this.currentTestRun!);
      });
    }

    // Remove these test cases from available
    this.availableTestCases = this.availableTestCases.filter(tc =>
      !testPlanCasesToSelect.some(ptc => ptc.uuid === tc.uuid)
    );

    // Update filtered available test cases
    this.filteredAvailableTestCases = this.availableTestCases;

    this._snackBar.open(`Added ${testPlanCasesToSelect.length} test cases from the test plan`, 'Close', {
      duration: 3000
    });
  }

  /**
   * Shows details for a test case
   * @param testCase The test case to show details for
   */
  showTestCaseDetails(testCase: TestCase) {
    this.selectedTestCase = testCase;
  }

  /**
   * Searches for test cases based on the search term
   */
  searchTestCases() {
    if (!this.searchTerm.trim()) {
      this.clearSearch();
      return;
    }

    const searchTerm = this.searchTerm.toLowerCase();
    this.filteredAvailableTestCases = this.availableTestCases.filter(tc => {
      return (
        (tc.name && tc.name.toLowerCase().includes(searchTerm)) ||
        (tc.description && tc.description.toLowerCase().includes(searchTerm)) ||
        (tc.category && tc.category.toLowerCase().includes(searchTerm))
      );
    });

    this.isSearching = true;
  }

  /**
   * Clears the search term and resets the filtered test cases
   */
  clearSearch() {
    this.searchTerm = '';
    this.filteredAvailableTestCases = [...this.availableTestCases];
    this.isSearching = false;
  }

  /**
   * Compare function for test plans in the dropdown
   * @param plan1 First test plan
   * @param plan2 Second test plan
   * @returns True if the plans are the same
   */
  compareTestPlans(plan1: TestPlan | string | null, plan2: TestPlan | string | null): boolean {
    if (!plan1 || !plan2) {
      return plan1 === plan2;
    }

    const id1 = typeof plan1 === 'string' ? plan1 : plan1.uuid;
    const id2 = typeof plan2 === 'string' ? plan2 : plan2.uuid;

    return id1 === id2;
  }

  /**
   * Cleanup on component destruction
   */
  ngOnDestroy() {
    this._snackBar.dismiss();
  }
}
