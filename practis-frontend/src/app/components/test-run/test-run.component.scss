// Variables
:root {
  --primary: #3f51b5;
  --accent: #ff4081;
  --warn: #f44336;

  --bg-main: #f5f5f5;
  --bg-card: #ffffff;
  --bg-panel: #ffffff;
  --bg-list: #ffffff;
  --text-primary: rgba(0, 0, 0, 0.87);
  --text-secondary: rgba(0, 0, 0, 0.54);
  --border-color: #e0e0e0;
}

// Container général
.container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .header-section {
    h2 {
      margin: 0;
      font-weight: 500;
      .text-primary {
        color: var(--primary);
      }
    }

    .version-badge,
    span.version-badge,
    div.version-badge,
    .header-section .version-badge,
    .me-4.version-badge {
      background-color: #3f51b5 !important; // Couleur fixe pour visibilité en mode jour
      color: white !important;
      padding: 6px 12px !important;
      border-radius: 16px !important;
      font-size: 0.85rem !important;
      font-weight: 500 !important;
      display: inline-block !important;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
      opacity: 1 !important;
      transition: none !important;
    }
  }
}

.no-plans {
  margin-top: 20px;
}

// Style pour les plans
.plans-container {
  mat-expansion-panel {
    margin-bottom: 16px;
    background-color: var(--bg-panel);
    width: 100%; // Pour s'étendre sur toute la largeur disponible
    max-width: 1200px; // Comme dans test-plan component

    mat-expansion-panel-header {
      height: 64px;

      mat-panel-title {
        font-size: 16px;
        font-weight: 500;
        color: var(--text-primary);
        flex: 1;
      }

      mat-panel-description {
        color: var(--text-secondary);
        justify-content: space-between; // Pour centrer le texte entre le titre et le bouton
        display: flex;
        align-items: center;
        margin-left: 8px; // Espace entre le titre et le compteur

        button {
          margin-left: 8px; // Espace entre le compteur et le bouton
        }
      }
    }
  }
}

// Style pour les cards
.bottom-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.cardWithShadow {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
}

// Style pour les test runs
.test-runs {
  padding-top: 16px;

  .no-runs {
    text-align: center;
    padding: 20px;
    color: rgba(0, 0, 0, 0.54);
    font-style: italic;
  }

  .test-run-card {
    margin-bottom: 16px;
    transition: all 0.3s ease;
    border-radius: 8px;

    &.expanded {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    mat-card-header {
      cursor: pointer;
      padding-bottom: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-actions {
        margin-left: auto;
      }

      mat-card-subtitle {
        .description-preview {
          font-style: italic;
          margin-top: 4px;
          color: rgba(0, 0, 0, 0.6);
          font-size: 0.85rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 500px;
        }
      }
    }

    mat-card-content {
      padding: 16px;
      border-top: 1px solid rgba(0, 0, 0, 0.12);

      .run-info {
        p {
          margin: 8px 0;
        }

        .row {
          display: flex;
          margin-left: -15px;
          margin-right: -15px;
          flex-wrap: wrap;

          .col-md-6 {
            flex: 0 0 50%;
            max-width: 50%;
            padding-left: 15px;
            padding-right: 15px;
          }
        }

        .w-100 {
          width: 100%;
        }

        .mb-3 {
          margin-bottom: 1rem;
        }
      }

      .button-row {
        display: flex;
        justify-content: flex-end;
        margin-top: 16px;
      }
    }
  }
}

// Test case selection styles
.test-case-selection {
  margin-top: 20px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-container {
  margin-bottom: 15px;
}

.search-field {
  width: 100%;
}

.test-case-column {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;

  h4 {
    margin-top: 0;
    margin-bottom: 15px;
    display: flex;
    align-items: center;

    &.title-with-button {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;

      .title-text {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
      }

      .button-group {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        margin-bottom: 10px;
      }
    }
  }
}

.test-case-list {
  flex-grow: 1;
  min-height: 300px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow-y: auto;
  padding: 8px;
  background-color: #fafafa;
}

.test-case-box {
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: white;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .test-case-content {
    .test-case-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;

      .test-case-name {
        font-weight: bold;
        font-size: 14px;
      }

      .test-case-priority {
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;

        &.priority-1 {
          background-color: #f44336;
          color: white;
        }

        &.priority-2 {
          background-color: #ff9800;
          color: white;
        }

        &.priority-3 {
          background-color: #ffeb3b;
          color: black;
        }

        &.priority-4 {
          background-color: #4caf50;
          color: white;
        }

        &.priority-5 {
          background-color: #2196f3;
          color: white;
        }
      }
    }

    .test-case-description {
      font-size: 12px;
      color: #666;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.test-case-placeholder {
  background: #ccc;
  border: dotted 1px #999;
  min-height: 60px;
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.empty-list-message {
  padding: 20px;
  text-align: center;
  color: #999;
  font-style: italic;
}

.test-case-details-panel {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-top: 20px;

  h4 {
    margin-top: 0;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
  }

  .test-case-details {
    .detail-row {
      margin-bottom: 10px;

      strong {
        font-weight: bold;
        margin-right: 5px;
      }

      .description-text {
        margin-top: 5px;
        white-space: pre-line;
      }
    }
  }
}

// Utilitaires
.mr-2 {
  margin-right: 0.5rem !important;
}

// Dark theme
:host-context(.dark-theme), body.dark-theme {
  --primary: #7986cb;
  --accent: #ff80ab;
  --warn: #ef5350;

  --bg-main: #1e1e1e;
  --bg-card: #2d2d2d;
  --bg-panel: #2d2d2d;
  --bg-list: #333333;

  --text-primary: rgba(255, 255, 255, 0.87);
  --text-secondary: rgba(255, 255, 255, 0.6);
  --border-color: #555555;

  .plans-container {
    mat-expansion-panel {
      background-color: #2d2d2d !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;

      mat-expansion-panel-header {
        mat-panel-title {
          color: rgba(255, 255, 255, 0.87) !important;
        }

        mat-panel-description {
          color: rgba(255, 255, 255, 0.6) !important;
        }
      }
    }
  }

  .test-run-card {
    background-color: #333333 !important;
    border: 1px solid #555555 !important;

    mat-card-title {
      color: rgba(255, 255, 255, 0.87) !important;
    }

    mat-card-subtitle {
      color: rgba(255, 255, 255, 0.6) !important;

      .description-preview {
        color: rgba(255, 255, 255, 0.5) !important;
      }
    }

    mat-card-content {
      border-top-color: #555555 !important;
    }
  }

  .no-runs {
    color: rgba(255, 255, 255, 0.6) !important;
  }

  // Style spécial pour le badge de version en dark mode
  .version-badge,
  span.version-badge,
  div.version-badge,
  .header-section .version-badge,
  .me-4.version-badge {
    background-color: #7986cb !important; // Couleur primaire du dark mode
    color: rgba(255, 255, 255, 0.87) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
  }

  // Test case selection styles for dark theme
  .test-case-selection {
    background-color: #2d2d2d !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
  }

  .test-case-column {
    background-color: #333333 !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
  }

  .test-case-list {
    border-color: #555555 !important;
    background-color: #2d2d2d !important;
  }

  .test-case-box {
    border-color: #555555 !important;
    background-color: #333333 !important;

    &:hover {
      background-color: #3a3a3a !important;
    }

    .test-case-content {
      .test-case-header {
        .test-case-name {
          color: rgba(255, 255, 255, 0.87) !important;
        }
      }

      .test-case-description {
        color: rgba(255, 255, 255, 0.6) !important;
      }
    }
  }

  .test-case-placeholder {
    background: #444 !important;
    border-color: #666 !important;
  }

  .empty-list-message {
    color: rgba(255, 255, 255, 0.5) !important;
  }

  .test-case-details-panel {
    background-color: #333333 !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;

    h4 {
      border-bottom-color: #555555 !important;
      color: rgba(255, 255, 255, 0.87) !important;
    }

    .test-case-details {
      .detail-row {
        color: rgba(255, 255, 255, 0.7) !important;

        strong {
          color: rgba(255, 255, 255, 0.87) !important;
        }
      }
    }
  }
}
