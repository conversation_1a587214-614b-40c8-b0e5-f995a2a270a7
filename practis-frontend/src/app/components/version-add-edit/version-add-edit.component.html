<div mat-dialog-title>
    <h4 *ngIf="!onlyNotes">{{data ? 'Update': 'Add'}} Version</h4>
    <h4 *ngIf="onlyNotes">Update Version Notes</h4>
</div>

<div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
</div>

<form [formGroup]="form" class="form" novalidate>
    <div mat-dialog-content class="content">
        <div class="row custom-row">
            <div class="col-12 col-sm-12">
                <div [className]="onlyNotes? 'div-hide': 'div-show'">
                    <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Name *</mat-label>
                    <mat-form-field appearance="outline" class="w-100" color="primary">
                        <input matInput formControlName="name" required />
                        <mat-error *ngIf="form.get('name')?.hasError('required')" class="no-pad">
                            Name is required
                        </mat-error>
                    </mat-form-field>
                    <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Status *</mat-label>
                    <mat-form-field appearance="outline" class="w-100" color="primary">
                        <mat-select formControlName="status" required>
                            @for (status of versionStatuses; track status) {
                            <mat-option [value]="status">{{status}}</mat-option>
                            }
                        </mat-select>
                        <mat-error *ngIf="form.get('status')?.hasError('required')" class="no-pad">
                            Status required
                        </mat-error>
                    </mat-form-field>

                    <!-- Description Field -->
                    <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Description</mat-label>
                    <!-- <quill-editor matInput formControlName="description"></quill-editor> -->
                    <mat-form-field appearance="outline" class="w-100" color="primary">
                        <textarea matInput formControlName="description"></textarea>
                    </mat-form-field>


                    <!-- Images -->
                    <div class="image-section">
                        <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Images</mat-label>
                        <button mat-stroked-button color="primary" (click)="fileInput.click()">
                            <mat-icon>upload</mat-icon>
                            Upload Images
                        </button>
                        <input #fileInput type="file" hidden (change)="onImageSelect($event)" multiple accept="image/*">

                        <div class="image-list" *ngIf="imageFiles.length > 0">
                            <mat-card *ngFor="let image of imageFiles; let i = index"
                                [class.default-image]="image.default">
                                <mat-card-content class="image-item">
                                    <img *ngIf="image.file" [src]="utilsService.getImage(image.file)" alt="Image"
                                        class="image-preview">
                                    <img *ngIf="image.preview" [src]="image.preview" alt="Image" class="image-preview">
                                    <span class="image-name">{{ image.file ? image.file.split('/').pop() :
                                        image.uploadFile?.name
                                        }}</span>

                                    <div class="image-actions">
                                        <button mat-button color="primary" (click)="setImageAsDefault(i)"
                                            [disabled]="image.default" class="btn-default">
                                            {{ image.default ? 'Default' : 'Set as Default' }}
                                        </button>
                                        <button mat-icon-button color="warn" (click)="removeImage(i)">
                                            <mat-icon>delete</mat-icon>
                                        </button>
                                    </div>
                                </mat-card-content>
                            </mat-card>
                        </div>
                    </div>
                </div>

                <!-- Description Field -->
                <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Notes</mat-label>
                <!-- <quill-editor matInput formControlName="notes"></quill-editor> -->
                <mat-form-field appearance="outline" class="w-100" color="primary">
                    <textarea matInput formControlName="notes"></textarea>
                </mat-form-field>
            </div>
        </div>
    </div>

    <div mat-dialog-actions class="action">
        <div class="button-container">
            <button mat-raised-button color="primary" type="submit"
                [disabled]="form.pristine || form.invalid || form.pending" (click)="onFormSubmit()">{{data ? 'Update':
                'Save'}}</button>
            <button mat-raised-button type="button" [mat-dialog-close]="false">Cancel</button><br />
        </div><br /><br />
    </div>
</form>