import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MaterialModule } from 'app/material.module';
import { TestCaseComponent } from '../test-case/test-case.component';
import { TestPlanComponent } from '../test-plan/test-plan.component';
import { TestRunComponent } from '../test-run/test-run.component';
import { TestExecutionComponent } from '../test-execution/test-execution.component';

@Component({
  selector: 'app-test-menu',
  standalone: true,
  imports: [CommonModule, MaterialModule, TestCaseComponent, TestPlanComponent, TestRunComponent, TestExecutionComponent],
  templateUrl: './test-menu.component.html',
  styleUrl: './test-menu.component.scss'
})
export class TestMenuComponent {
  @Input() dataInput: any;
  @Output() actionProcess: EventEmitter<any> = new EventEmitter<any>();

  // Method to pass events from child components upward
  onActionProcess(event: any): void {
    this.actionProcess.emit(event);
  }
}
