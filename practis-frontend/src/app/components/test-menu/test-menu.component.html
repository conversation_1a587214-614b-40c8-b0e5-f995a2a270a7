<mat-tab-group animationDuration="0ms" dynamicHeight>
    <mat-tab label="Test Cases">
        <div class="content">
            <app-test-case [dataInput]="dataInput" (actionProcess)="onActionProcess($event)"></app-test-case>
        </div>
    </mat-tab>
    <mat-tab label="Test Plans">
        <div class="content">
            <app-test-plan [dataInput]="dataInput" (actionProcess)="onActionProcess($event)"></app-test-plan>
        </div>
    </mat-tab>
    <mat-tab label="Test Runs">
        <div class="content">
            <app-test-run [dataInput]="dataInput" (actionProcess)="onActionProcess($event)"></app-test-run>
        </div>
    </mat-tab>
    <mat-tab label="Test Executions">
        <div class="content">
            <app-test-execution [dataInput]="dataInput" (actionProcess)="onActionProcess($event)"></app-test-execution>
        </div>
    </mat-tab>
</mat-tab-group>
