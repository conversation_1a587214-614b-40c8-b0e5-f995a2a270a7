<div mat-dialog-title>
    <h4>{{data ? 'Update': 'Add'}} Requirement</h4>
</div>
<form [formGroup]="form" class="form" novalidate>
    <div mat-dialog-content class="content">
        <div class="row custom-row">
            <div class="col-12 col-sm-12">
                <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Name *</mat-label>
                <mat-form-field appearance="outline" class="w-100" color="primary">
                    <input matInput formControlName="name" required />
                    <mat-error *ngIf="form.get('name')?.hasError('required')" class="no-pad">
                        Name is required
                    </mat-error>
                </mat-form-field>

                <!-- Description Field -->
                <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Description</mat-label>
                <!-- <quill-editor matInput formControlName="description"></quill-editor> -->
                <mat-form-field appearance="outline" class="w-100" color="primary">
                    <textarea matInput formControlName="description"></textarea>
                </mat-form-field>
                <div class="row custom-row">
                    <div class="col-12 col-sm-6">
                        <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Priority
                            *</mat-label>
                        <mat-form-field appearance="outline" class="w-50" color="primary">
                            <mat-select formControlName="priority" required>
                                @for (priority of requirementPriorities; track priority) {
                                <mat-option [value]="priority">{{priority}}</mat-option>
                                }
                            </mat-select>
                            <mat-error *ngIf="form.get('priority')?.hasError('required')" class="no-pad">
                                Priority required
                            </mat-error>
                        </mat-form-field>
                    </div>
                    <div class="col-12 col-sm-6">
                        <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Status
                            *</mat-label>
                        <mat-form-field appearance="outline" class="w-50" color="primary">
                            <mat-select formControlName="status" required>
                                @for (status of requirementStatuses; track status) {
                                <mat-option [value]="status">{{status}}</mat-option>
                                }
                            </mat-select>
                            <mat-error *ngIf="form.get('status')?.hasError('required')" class="no-pad">
                                Status required
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div mat-dialog-actions class="action">
        <div class="button-container">
            <button mat-raised-button color="primary" type="submit"
                [disabled]="form.pristine || form.invalid || form.pending" (click)="onFormSubmit()">{{data ? 'Update':
                'Save'}}</button>
            <button mat-raised-button type="button" [mat-dialog-close]="false">Cancel</button><br />
        </div><br /><br />
    </div>
</form>