import { Component, Inject, OnInit } from '@angular/core';
import { MaterialModule } from 'app/material.module';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Requirement } from 'app/models/requirement';
import { RequirementService } from 'app/services/requirement.service';
import { RequirementStatus } from 'app/models/enum-types';
import { RequirementPriority } from 'app/models/enum-types';

@Component({
  selector: 'app-requirement-add-edit',
  standalone: true,
  imports: [MaterialModule],
  templateUrl: './requirement-add-edit.component.html',
  styleUrl: './requirement-add-edit.component.scss',
})
export class RequirementAddEditComponent implements OnInit {
  form: FormGroup;
  data?: Requirement;
  sut_version?: string;
  requirementStatuses = Object.values(RequirementStatus);
  requirementPriorities = Object.values(RequirementPriority);

  constructor(
    private _fb: FormBuilder,
    private _dialogRef: MatDialogRef<RequirementAddEditComponent>,
    private _requirementService: RequirementService,
    @Inject(MAT_DIALOG_DATA) public all_data: any,
    public _snackBar: MatSnackBar
  ) {
    this.form = this._fb.group({
      name: ['', Validators.required],
      description: [''],
      priority: [RequirementPriority.Low, Validators.required],
      status: [RequirementStatus.Pending, Validators.required],
    });
  }

  ngOnInit(): void {
    this.data = this.all_data.requirement;
    this.sut_version = this.all_data.sut_version;
    //console.log(this.all_data);

    if (this.data) {
      // If editing, initialize the form with asset data
      this.initializeForm(this.data);
    }
  }

  // Populate form fields for editing an existing asset
  private initializeForm(data: Requirement): void {
    console.log(data);
    this.form.patchValue({
      name: data.name,
      description: data.description?.replace(/\\n/g, '\n'),
      status: data.status || RequirementStatus.Pending,
      priority: data.priority || RequirementPriority.Low,
    });

    // Manually update form validity after patching values to ensure button reflects the correct state
    this.markFormGroupTouchedAndDirty(this.form);
  }

  // Function to mark all controls as touched and dirty
  private markFormGroupTouchedAndDirty(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      if (control instanceof FormArray) {
        // Recursively apply to each FormGroup in the FormArray
        (control as FormArray).controls.forEach((arrayControl) => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouchedAndDirty(arrayControl);
          }
        });
      } else if (control instanceof FormGroup) {
        this.markFormGroupTouchedAndDirty(control);
      } else {
        control.markAsTouched();
        control.markAsDirty();
      }
    });
  }

  onFormSubmit() {
    if (this.form.valid) {
      // Create FormData to handle image uploads
      const formData = new FormData();
      if (this.sut_version) {
        formData.append('sut_version', this.sut_version);
      }

      Object.keys(this.form.value).forEach((key) => {
        const value = this.form.value[key];

        // Check if value is an object (not null) and stringify only if necessary
        if (typeof value === 'object' && value !== null) {
          formData.append(key, JSON.stringify(value));
        } else {
          formData.append(key, value ?? ''); // Append empty string if value is null/undefined
        }
      });

      console.log(formData);

      //Uncomment later when backend work correctly
      if (this.data && this.data.uuid) {
        this._requirementService
          .updateRequirement(this.data.uuid, formData)
          .subscribe({
            next: (val: any) => {
              //console.log(val);
              this._snackBar.open('Requirement detail updated!', 'Done', {
                duration: 3000,
              });
              this._dialogRef.close(val);
            },
            error: (err: any) => {
              console.error(err);
            },
          });
      } else {
        this._requirementService.addRequirement(formData).subscribe({
          next: (val: any) => {
            this._snackBar.open('Requirement added successfully', 'Done', {
              duration: 3000,
            });
            this._dialogRef.close(val);
          },
          error: (err: any) => {
            console.error(err);
          },
        });
      }
    }
  }
}
