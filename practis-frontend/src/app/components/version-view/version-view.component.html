<mat-tab-group animationDuration="0ms" dynamicHeight #tabGroupAll [selectedIndex]="selectedTabAllIndex">
    <mat-tab label="Inputs">
        <div>
            <mat-tab-group animationDuration="0ms" dynamicHeight #tabGroup [selectedIndex]="selectedTabIndex"
                (selectedTabChange)="onTabChange($event)">
                <mat-tab label="SUT">
                </mat-tab>
                <mat-tab label="Objectives">
                    <div class="content">
                        <h2>
                            <span class="text-primary">Objective Details</span>
                        </h2>
                        <form [formGroup]="formObjective" class="form" novalidate>
                            <div class="row custom-row">
                                <div class="col-12 col-sm-12">
                                    <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Objectives
                                        *</mat-label>
                                    <mat-form-field appearance="outline" class="w-50" color="primary">
                                        <mat-select formControlName="objectives" required>
                                            @for (objectiveType of objectiveTypes; track objectiveType) {
                                            <mat-option [value]="objectiveType">{{objectiveType}}</mat-option>
                                            }
                                        </mat-select>
                                        <mat-error *ngIf="formObjective.get('objectives')?.hasError('required')"
                                            class="no-pad">
                                            Objectives required
                                        </mat-error>
                                    </mat-form-field>

                                    <!-- Description Field -->
                                    <mat-label
                                        class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Description*</mat-label>
                                    <quill-editor matInput formControlName="description"></quill-editor>
                                    <mat-error *ngIf="formObjective.get('description')?.hasError('required')"
                                        class="no-pad">
                                        Description required
                                    </mat-error>
                                </div>

                                <div class="col-12 col-sm-12 right no-pad">
                                    <button mat-raised-button color="primary" type="submit"
                                        [disabled]="formObjective.pristine || formObjective.invalid || formObjective.pending"
                                        (click)="onFormObjectiveSubmit()">{{version?.objectives?.[0]?.uuid ? 'Update':
                                        'Save'}}</button>
                                    <button mat-raised-button type="button"
                                        [mat-dialog-close]="false">Cancel</button>&nbsp;&nbsp;<br />
                                </div><br /><br />
                            </div>
                        </form>
                    </div>
                </mat-tab>
                <mat-tab label="Scope">
                    <div class="content">
                        <h2>
                            <span class="text-primary">Scope Details</span>
                        </h2>
                        <form [formGroup]="formScope" class="form" novalidate>
                            <div class="row custom-row">
                                <div class="col-12 col-sm-12">
                                    <!-- Scope Field -->
                                    <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Scope*</mat-label>
                                    <quill-editor matInput formControlName="scope"></quill-editor>
                                    <mat-error *ngIf="formScope.get('scope')?.hasError('required')" class="no-pad">
                                        Scope required
                                    </mat-error>
                                </div>

                                <div class="col-12 col-sm-12 right no-pad">
                                    <button mat-raised-button color="primary" type="submit"
                                        [disabled]="formScope.pristine || formScope.invalid || formScope.pending"
                                        (click)="onFormScopeSubmit()">{{version?.objectives?.[0]?.uuid ? 'Update':
                                        'Save'}}</button>
                                    <button mat-raised-button type="button"
                                        [mat-dialog-close]="false">Cancel</button><br />
                                </div><br /><br />
                            </div>
                        </form>
                    </div>
                </mat-tab>
                <mat-tab label="Risk Analysis">
                    <div class="content">
                        <h2>
                            <span class="text-primary">Risk Analysis</span>
                        </h2>
                        <form [formGroup]="formRiskAnalysis" class="form" novalidate>
                            <div class="row custom-row">
                                <div class="col-12 col-sm-3">
                                    <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Name*</mat-label>
                                    <mat-form-field appearance="outline" class="w-100" color="primary">
                                        <input matInput formControlName="name" required />
                                        <mat-error *ngIf="formRiskAnalysis.get('name')?.hasError('required')"
                                            class="no-pad">
                                            Name required
                                        </mat-error>
                                    </mat-form-field>
                                </div>
                                <div class="col-12 col-sm-6">
                                    <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">URL
                                        Documentation*</mat-label>
                                    <mat-form-field appearance="outline" class="w-100" color="primary">
                                        <input matInput formControlName="docs" required />
                                        <mat-error *ngIf="formRiskAnalysis.get('docs')?.hasError('required')"
                                            class="no-pad">
                                            URL documentation required
                                        </mat-error>
                                    </mat-form-field>
                                </div>
                                <div class="col-12 col-sm-3 mt-sm">
                                    <button mat-raised-button color="primary" type="submit"
                                        [disabled]="formRiskAnalysis.pristine || formRiskAnalysis.invalid || formRiskAnalysis.pending"
                                        (click)="onFormRiskAnalysisSubmit()">{{version?.riskanalysis?.[0]?.uuid ?
                                        'Update':
                                        'Save'}}</button>
                                    <button mat-raised-button type="button"
                                        [mat-dialog-close]="false">Cancel</button>&nbsp;&nbsp;<br />
                                </div><br /><br />
                            </div>
                        </form>

                        <div class="bottom-section">
                            <h2>
                                <span class="text-primary">Risks:</span>
                                <button *ngIf="version?.riskanalysis?.[0]?.uuid" mat-raised-button color="primary"
                                    class="new-bottom-button" (click)="openAddRiskForm()">
                                    New Risk
                                </button>
                            </h2>

                            <div class="bottom-cards">
                                <ng-container *ngIf="!version?.riskanalysis?.[0]?.risks?.length">
                                    <mat-card class="bottom-card cardWithShadow">
                                        <mat-card-content>
                                            <div class="no-top-section">
                                                <h3>No Risks found</h3>
                                            </div>
                                        </mat-card-content>
                                    </mat-card>
                                </ng-container>
                                <ng-container *ngIf="version?.riskanalysis?.[0]?.risks?.length ?? 0 > 0">
                                    <mat-card *ngFor="let risk of version?.riskanalysis?.[0]?.risks"
                                        class="bottom-card cardWithShadow">
                                        <div class="title-buttons">
                                            <h4>{{risk.name}}</h4>
                                            <div>
                                                <button mat-icon-button (click)="openEditRiskForm(risk)">
                                                    <mat-icon color="primary">edit</mat-icon>
                                                </button>
                                                <button mat-icon-button (click)="deleteRisk(risk)">
                                                    <mat-icon color="warn">delete</mat-icon>
                                                </button>
                                            </div>
                                        </div>
                                        <mat-card-content>
                                            <div class="content-all">
                                                <div class="content-left">
                                                    <div *ngIf="risk.uuid"
                                                        [innerHTML]="safeRiskDescriptions[risk.uuid]">
                                                    </div><br />
                                                    <div class="row custom-row">
                                                        <div class="col-12 col-sm-4">
                                                            <b>Value:</b> {{risk.value}}
                                                        </div>
                                                        <div class="col-12 col-sm-4">
                                                            <b>Status:</b> {{risk.status}}
                                                        </div>
                                                        <div class="col-12 col-sm-4">
                                                            <b>Level:</b> {{risk.level}}
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </mat-card-content>
                                    </mat-card>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                </mat-tab>
                <mat-tab label="Requirements">
                    <div class="content">
                        <div class="bottom-section">
                            <h2>
                                <span class="text-primary">Requirements</span>
                                <button *ngIf="version?.uuid" mat-raised-button color="primary"
                                    class="new-bottom-button" (click)="openAddRequirementForm()">
                                    New Requirement
                                </button>
                            </h2>

                            <div class="bottom-cards">
                                <ng-container *ngIf="!version?.requirements?.length">
                                    <mat-card class="bottom-card cardWithShadow">
                                        <mat-card-content>
                                            <div class="no-top-section">
                                                <h3>No Requirements found</h3>
                                            </div>
                                        </mat-card-content>
                                    </mat-card>
                                </ng-container>
                                <ng-container *ngIf="version?.requirements?.length ?? 0 > 0">
                                    <mat-card *ngFor="let requirement of version?.requirements"
                                        class="bottom-card cardWithShadow">
                                        <div class="title-buttons">
                                            <h4>{{requirement.name}}</h4>
                                            <div>
                                                <button mat-icon-button (click)="openEditRequirementForm(requirement)">
                                                    <mat-icon color="primary">edit</mat-icon>
                                                </button>
                                                <button mat-icon-button (click)="deleteRequirement(requirement)">
                                                    <mat-icon color="warn">delete</mat-icon>
                                                </button>
                                            </div>
                                        </div>
                                        <mat-card-content>
                                            <div class="content-all">
                                                <div class="content-left">
                                                    <div *ngIf="requirement.uuid"
                                                        [innerHTML]="safeRequirementDescriptions[requirement.uuid]">
                                                    </div>
                                                    <br />
                                                    <div class="row custom-row">
                                                        <div class="col-12 col-sm-6">
                                                            <b>Priority:</b> {{requirement.priority}}
                                                        </div>
                                                        <div class="col-12 col-sm-6">
                                                            <b>Status:</b> {{requirement.status}}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </mat-card-content>
                                    </mat-card>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                </mat-tab>
            </mat-tab-group>
        </div>
    </mat-tab>
    <mat-tab label="Information Gathering">
        <div class="special">
            <div class="bottom-section">
                <div class="diagram-header">
                    <h2>
                        <span class="text-primary">Diagram</span>
                    </h2>
                    <div class="search-container">
                        <mat-form-field appearance="outline">
                            <input matInput [formControl]="parameterSearchControl" (click)="onSearchInputClick()"
                                placeholder="search parameter" />
                            <mat-icon matSuffix>search</mat-icon>
                        </mat-form-field>
                    </div>
                </div>
                <div class="diagram-container">
                    <app-diagram-builder [diagramInput]="diagramInput" [version]="version"
                        (actionProcess)="actionProcess($event)"></app-diagram-builder>
                    <app-parameter-list [version]="version"></app-parameter-list>
                </div>
            </div>
        </div>
    </mat-tab>
    <mat-tab label="Reconnaissance" [disabled]="!this.version?.objectives?.[0]?.objectives">
        <div>
            <mat-tab-group animationDuration="0ms" dynamicHeight>
                <mat-tab label="Flows">
                    <div class="content">
                        <app-flow [dataInput]="dataInput" (actionProcess)="actionProcess($event)"></app-flow>
                    </div>
                </mat-tab>
                <mat-tab label="Flow Executions">
                    <div class="content">
                        <app-flow-execution [flowexecutions]="version?.flowexecutions"
                            (actionProcess)="actionProcess($event)"></app-flow-execution>
                    </div>
                </mat-tab>
            </mat-tab-group>
        </div>
    </mat-tab>
    <mat-tab label="Vulnerability Assessment" [disabled]="true">

    </mat-tab>
    <mat-tab label="Tests">
        <div>
            <app-test-menu [dataInput]="dataInput" (actionProcess)="actionProcess($event)"></app-test-menu>
        </div>
    </mat-tab>
    <mat-tab label="Recommendations & Reporting">
        <div>
            <mat-tab-group animationDuration="0ms" dynamicHeight [selectedIndex]="1">
                <mat-tab label="Recommendations" [disabled]="true">
                </mat-tab>
                <mat-tab label="Reporting">
                    <div class="content">
                        <app-report [version]="version"></app-report>
                    </div>
                </mat-tab>
            </mat-tab-group>
        </div>

    </mat-tab>
</mat-tab-group>


<button mat-fab aria-label="Edit notes" class="fixed-notes" (click)="openEditVersionForm(version)" color="primary">
    <mat-icon>edit</mat-icon>
</button>