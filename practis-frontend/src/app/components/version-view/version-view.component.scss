.diagram-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h2 {
      margin: 0;
    }
    
    .search-container {
      width: 300px;
      
      mat-form-field {
        width: 100%;
        margin-bottom: -1.25em;
      }
    }
  }
  
  .diagram-container {
    position: relative;
    height: calc(100vh - 250px);
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 4px;
    overflow: hidden;
  }
  
  /* Assurer que le composant de diagramme occupe tout l'espace */
  app-diagram-builder {
    width: 100%;
    height: 100%;
    display: block;
  }
  app-parameter-list {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }