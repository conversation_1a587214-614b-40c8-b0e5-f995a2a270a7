import { MaterialModule } from 'app/material.module';
import {
  AfterViewInit,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Sut, SutWithVersions } from 'app/models/sut';
import { Version, VersionWithAllData } from 'app/models/version';
import { SutService } from 'app/services/sut.service';
import { VersionService } from 'app/services/version.service';
import { UtilsService } from 'app/utils/utils';
import { SutAddEditComponent } from '../sut-add-edit/sut-add-edit.component';
import { VersionAddEditComponent } from '../version-add-edit/version-add-edit.component';
import { SafeHtml } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { MenuService } from 'app/services/menu.service';
import { ObjectivesAndScope } from 'app/models/objective-scope';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ObjectiveType } from 'app/models/enum-types';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { OjectiveScopeService } from 'app/services/ojective-scope.service';
import { RiskAnalysisService } from 'app/services/risk-analysis.service';
import { RiskService } from 'app/services/risk.service';
import { RequirementService } from 'app/services/requirement.service';
import { RiskAddEditComponent } from '../risk-add-edit/risk-add-edit.component';
import { Risk } from 'app/models/risk';
import { RequirementAddEditComponent } from '../requirement-add-edit/requirement-add-edit.component';
import { Requirement } from 'app/models/requirement';
import { FlowComponent } from '../flow/flow.component';
import { FlowExecutionComponent } from '../flow-execution/flow-execution.component';
import { DiagramBuilderComponent } from '../diagram-builder/diagram-builder.component';
import { ReportComponent } from '../report/report.component';
import { ParameterListComponent } from '../parameter-list/parameter-list.component';
import { TestMenuComponent } from '../test-menu/test-menu.component';

import { FormControl } from '@angular/forms';
@Component({
  selector: 'app-version-view',
  standalone: true,
  imports: [
    MaterialModule,
    FlowComponent,
    FlowExecutionComponent,
    DiagramBuilderComponent,
    ReportComponent,
    ParameterListComponent,
    TestMenuComponent
  ],
  templateUrl: './version-view.component.html',
  styleUrl: './version-view.component.scss',
})
export class VersionViewComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('tabGroupAll') tabGroupAll!: MatTabGroup;
  @ViewChild('tabGroup') tabGroup!: MatTabGroup;
  version?: VersionWithAllData;
  sutId?: string;
  versionId?: string;
  formObjective: FormGroup;
  formScope: FormGroup;
  formRiskAnalysis: FormGroup;
  selectedTabAllIndex = 1;
  selectedTabIndex = 1;
  objectiveTypes = Object.values(ObjectiveType);
  safeRiskDescriptions: { [key: string]: SafeHtml } = {};
  safeRequirementDescriptions: { [key: string]: SafeHtml } = {};
  dataInput?: any;
  diagramInput?: any;
  parameterSearchControl = new FormControl('');
  @ViewChild(ParameterListComponent) parameterListComponent?: ParameterListComponent;


  constructor(
    private _dialog: MatDialog,
    private _versionService: VersionService,
    public _snackBar: MatSnackBar,
    private _route: ActivatedRoute,
    private _router: Router,
    private _utilsService: UtilsService,
    private _fb: FormBuilder,
    private _objectiveScopeService: OjectiveScopeService,
    private _riskAnalysisService: RiskAnalysisService,
    private _riskService: RiskService,
    private _requirementService: RequirementService
  ) {
    this.formObjective = this._fb.group({
      description: ['', Validators.required],
      objectives: [ObjectiveType.EvaluatingSecurity, Validators.required],
    });
    this.formScope = this._fb.group({
      scope: ['', Validators.required],
    });
    this.formRiskAnalysis = this._fb.group({
      name: ['', Validators.required],
      docs: ['', Validators.required],
    });
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.selectedTabAllIndex = 0;
      this.selectedTabIndex = 1;
      
      // S'assurer que le composant parameter-list est accessible
      setTimeout(() => {
        if (this.parameterListComponent) {
          console.log('Parameter list component found');
          
          // Synchroniser les changements de valeur
          this.parameterSearchControl.valueChanges.subscribe(value => {
            console.log('Search value changed:', value);
            this.parameterListComponent?.searchControl.setValue(value);
            
            // Déclencher automatiquement la recherche pour chaque changement
            if (value !== null && value !== undefined) {
              if (value.trim() !== '') {
                this.parameterListComponent?.performSearch(value);
              } else {
                // Si la valeur est vidée, cacher les résultats
                this.parameterListComponent?.resetSearch();
              }
            }
          });
          
          // Si déjà une valeur dans le champ au chargement de la page
          if (this.parameterSearchControl.value) {
            this.parameterListComponent.performSearch(this.parameterSearchControl.value);
          }
        } else {
          console.warn('Parameter list component not found');
        }
      }, 500); // Attendre que tous les composants soient initialisés
    });
  }

  onTabChange(event: MatTabChangeEvent) {
    if (event.index === 0) {
      // 0 is the index of the "SUT" tab
      this._router.navigate(['/sut', this.sutId]);
    }
  }

  ngOnInit(): void {
    this._route.params.subscribe((params) => {
      this.sutId = params['sutId'];
      this.versionId = params['versionId'];
      this.diagramInput = {
        sutId: params['sutId'],
        versionId: params['versionId'],
        componentId: null,
        subcomponentId: null,
        portId: null,
      };
      //console.log(this.sutId);

      this.refresh();
    });
  }

  actionProcess(dataProcess: any) {
    if (dataProcess.action == 'refresh') {
      this.refresh();
    }
  }

  //get version
  refresh() {
    this._versionService.getVersionById(this.versionId as string).subscribe({
      next: (res) => {
        //console.log(res);
        this.version = res;
        this.dataInput = {
          sut: {
            uuid: this.version.sut?.uuid,
            name: this.version.sut?.name,
          },
          version: {
            uuid: this.version.uuid,
            name: this.version?.name,
          },
        };
        this.version?.riskanalysis?.[0]?.risks?.forEach((risk) => {
          if (risk.uuid) {
            this.safeRiskDescriptions[risk.uuid] =
              this._utilsService.getSafeDescription(risk.description);
          }
        });
        this.version?.requirements?.forEach((requirement) => {
          if (requirement.uuid) {
            this.safeRequirementDescriptions[requirement.uuid] =
              this._utilsService.getSafeDescription(requirement.description);
          }
        });
        this.initializeForm();
      },
      error: console.log,
    });
  }

  // Populate form fields for editing an existing asset
  private initializeForm(): void {
    this.formObjective.patchValue({
      description: this.version?.objectives?.[0]?.description ?? '',
      objectives:
        this.version?.objectives?.[0]?.objectives ??
        ObjectiveType.EvaluatingSecurity,
    });

    this.formScope.patchValue({
      scope: this.version?.objectives?.[0]?.scope ?? '',
    });

    this.formRiskAnalysis.patchValue({
      name: this.version?.riskanalysis?.[0]?.name ?? '',
      docs: this.version?.riskanalysis?.[0]?.docs ?? '',
    });

    // Manually update form validity after patching values to ensure button reflects the correct state
    this.markFormGroupTouchedAndDirty(this.formObjective);
    this.markFormGroupTouchedAndDirty(this.formScope);
    this.markFormGroupTouchedAndDirty(this.formRiskAnalysis);
  }

  // Function to mark all controls as touched and dirty
  private markFormGroupTouchedAndDirty(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      if (control instanceof FormArray) {
        // Recursively apply to each FormGroup in the FormArray
        (control as FormArray).controls.forEach((arrayControl) => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouchedAndDirty(arrayControl);
          }
        });
      } else if (control instanceof FormGroup) {
        this.markFormGroupTouchedAndDirty(control);
      } else {
        control.markAsTouched();
        control.markAsDirty();
      }
    });
  }

  openEditVersionForm(version: any): void {
    const dialogRef = this._dialog.open(VersionAddEditComponent, {
      data: { version: version, sut: this.sutId, onlyNotes: true },
    });
    dialogRef.afterClosed().subscribe({
      next: (val) => {
        if (val) {
          this.refresh();
          //this._menuService.getMenuList();
        }
      },
    });
  }

  onFormObjectiveSubmit() {
    if (this.formObjective.valid) {
      // Create FormData to handle image uploads
      const formData = new FormData();
      formData.append('sut_version', this.version?.uuid || '');
      formData.append('onlyScope', '0');

      // Append form values, handling objects properly
      Object.keys(this.formObjective.value).forEach((key) => {
        const value = this.formObjective.value[key];

        // Check if value is an object (not null) and stringify only if necessary
        if (typeof value === 'object' && value !== null) {
          formData.append(key, JSON.stringify(value));
        } else {
          formData.append(key, value ?? ''); // Append empty string if value is null/undefined
        }
      });
      //console.log(formData);

      //Uncomment later when backend work correctly
      if (
        this.version &&
        this.version.objectives &&
        this.version.objectives.length > 0 &&
        this.version.objectives[0].uuid
      ) {
        this._objectiveScopeService
          .updateOjectiveScope(this.version.objectives[0].uuid, formData)
          .subscribe({
            next: (val: any) => {
              //console.log(val);
              this._snackBar.open('Objective saved!', 'Done', {
                duration: 3000,
              });
              this.refresh();
            },
            error: (err: any) => {
              console.error(err);
            },
          });
      } else {
        this._objectiveScopeService.addOjectiveScope(formData).subscribe({
          next: (val: any) => {
            this._snackBar.open('Objective saved!', 'Done', {
              duration: 3000,
            });
            this.refresh();
          },
          error: (err: any) => {
            console.error(err);
          },
        });
      }
    }
  }

  onFormScopeSubmit() {
    if (this.formScope.valid) {
      // Create FormData to handle image uploads
      const formData = new FormData();
      formData.append('sut_version', this.version?.uuid || '');
      formData.append('onlyScope', '1');

      Object.keys(this.formScope.value).forEach((key) => {
        const value = this.formScope.value[key];

        // Check if value is an object (not null) and stringify only if necessary
        if (typeof value === 'object' && value !== null) {
          formData.append(key, JSON.stringify(value));
        } else {
          formData.append(key, value ?? ''); // Append empty string if value is null/undefined
        }
      });
      //console.log(formData);

      //Uncomment later when backend work correctly
      if (
        this.version &&
        this.version.objectives &&
        this.version.objectives.length > 0 &&
        this.version.objectives[0].uuid
      ) {
        this._objectiveScopeService
          .updateOjectiveScope(this.version.objectives[0].uuid, formData)
          .subscribe({
            next: (val: any) => {
              //console.log(val);
              this._snackBar.open('Scope saved!', 'Done', {
                duration: 3000,
              });
              this.refresh();
            },
            error: (err: any) => {
              console.error(err);
            },
          });
      } else {
        this._objectiveScopeService.addOjectiveScope(formData).subscribe({
          next: (val: any) => {
            this._snackBar.open('Scope saved!', 'Done', {
              duration: 3000,
            });
            this.refresh();
          },
          error: (err: any) => {
            console.error(err);
          },
        });
      }
    }
  }

  onFormRiskAnalysisSubmit() {
    if (this.formRiskAnalysis.valid) {
      // Create FormData to handle image uploads
      const formData = new FormData();
      formData.append('sut_version', this.version?.uuid || '');
      Object.keys(this.formRiskAnalysis.value).forEach((key) => {
        const value = this.formRiskAnalysis.value[key];

        // Check if value is an object (not null) and stringify only if necessary
        if (typeof value === 'object' && value !== null) {
          formData.append(key, JSON.stringify(value));
        } else {
          formData.append(key, value ?? ''); // Append empty string if value is null/undefined
        }
      });
      //console.log(formData);

      //Uncomment later when backend work correctly
      if (
        this.version &&
        this.version.riskanalysis &&
        this.version.riskanalysis.length > 0 &&
        this.version.riskanalysis[0].uuid
      ) {
        this._riskAnalysisService
          .updateRiskAnalysis(this.version.riskanalysis[0].uuid, formData)
          .subscribe({
            next: (val: any) => {
              console.log(val);
              this._snackBar.open('Risk analysis updated!', 'Done', {
                duration: 3000,
              });
              this.refresh();
            },
            error: (err: any) => {
              console.error(err);
            },
          });
      } else {
        this._riskAnalysisService.addRiskAnalysis(formData).subscribe({
          next: (val: any) => {
            this._snackBar.open('Risk analysis saved successfully', 'Done', {
              duration: 3000,
            });
            this.refresh();
          },
          error: (err: any) => {
            console.error(err);
          },
        });
      }
    }
  }

  openAddRiskForm(): void {
    const dialogRef = this._dialog.open(RiskAddEditComponent, {
      data: { risk_analysis: this.version?.riskanalysis?.[0]?.uuid },
    });
    dialogRef.afterClosed().subscribe({
      next: (val) => {
        if (val) {
          this.refresh();
        }
      },
    });
  }
  openEditRiskForm(risk: Risk): void {
    const dialogRef = this._dialog.open(RiskAddEditComponent, {
      data: {
        risk: risk,
        risk_analysis: this.version?.riskanalysis?.[0]?.uuid,
      },
    });
    dialogRef.afterClosed().subscribe({
      next: (val) => {
        if (val) {
          this.refresh();
        }
      },
    });
  }

  //delete risk
  deleteRisk(risk: Risk) {
    const backup = this.version?.riskanalysis?.[0].risks
      ? [...this.version?.riskanalysis?.[0].risks]
      : []; // Create a shallow copy of the data for backup
    this.version!.riskanalysis![0].risks =
      this.version!.riskanalysis![0].risks!.filter((m) => m.uuid !== risk.uuid); // Remove risk from data source

    // Open a snackbar with an undo option
    const snackBarRef = this._snackBar.open(
      `Risk '${risk.name}' will be deleted`,
      'Undo',
      { duration: 3000 }
    );

    // Handle snackbar dismissal
    snackBarRef.afterDismissed().subscribe((res) => {
      if (!res.dismissedByAction) {
        // If user did not click 'Undo', proceed with deletion
        this._riskService.deleteRisk(risk.uuid as string).subscribe({
          next: (response: any) => {
            this.refresh();
            this._snackBar.open('Risk deleted!', 'Done', { duration: 3000 });
          },
          error: (error) => {
            //console.error('Error deleting risk:', error);
            //Restore data on error
            //204 No content should not return message (Backend needs to correct that)
            this.version!.riskanalysis![0].risks = backup;
            this._snackBar.open(
              'Failed to delete risk. Please try again.',
              'Close',
              { duration: 5000 }
            );
          },
        });
      } else {
        // If 'Undo' was clicked, restore the original data
        this.version!.riskanalysis![0].risks = backup;
      }
    });
  }

  openAddRequirementForm(): void {
    const dialogRef = this._dialog.open(RequirementAddEditComponent, {
      data: {
        sut_version: this.version?.uuid,
      },
    });
    dialogRef.afterClosed().subscribe({
      next: (val) => {
        if (val) {
          this.refresh();
        }
      },
    });
  }
  openEditRequirementForm(requirement: Requirement): void {
    const dialogRef = this._dialog.open(RequirementAddEditComponent, {
      data: {
        requirement: requirement,
        sut_version: this.version?.uuid,
      },
    });
    dialogRef.afterClosed().subscribe({
      next: (val) => {
        if (val) {
          this.refresh();
        }
      },
    });
  }

  //delete requirement
  deleteRequirement(requirement: Requirement) {
    const backup = this.version?.requirements
      ? [...this.version?.requirements]
      : []; // Create a shallow copy of the data for backup
    this.version!.requirements = this.version!.requirements!.filter(
      (m) => m.uuid !== requirement.uuid
    ); // Remove requirement from data source

    // Open a snackbar with an undo option
    const snackBarRef = this._snackBar.open(
      `Requirement '${requirement.name}' will be deleted`,
      'Undo',
      { duration: 3000 }
    );

    // Handle snackbar dismissal
    snackBarRef.afterDismissed().subscribe((res) => {
      if (!res.dismissedByAction) {
        // If user did not click 'Undo', proceed with deletion
        this._requirementService
          .deleteRequirement(requirement.uuid as string)
          .subscribe({
            next: (response: any) => {
              this.refresh();
              this._snackBar.open('Requirement deleted!', 'Done', {
                duration: 3000,
              });
            },
            error: (error) => {
              //console.error('Error deleting requirement:', error);
              //Restore data on error
              //204 No content should not return message (Backend needs to correct that)
              this.version!.requirements = backup;
              this._snackBar.open(
                'Failed to delete requirement. Please try again.',
                'Close',
                { duration: 5000 }
              );
            },
          });
      } else {
        // If 'Undo' was clicked, restore the original data
        this.version!.requirements = backup;
      }
    });
  }
  
  // Ajouter cette méthode
  onSearchInputClick(): void {
    // Déclencher la recherche immédiatement au clic
    if (this.parameterListComponent && this.version?.uuid) {
      console.log('Search input clicked, current value:', this.parameterSearchControl.value);
      
      if (this.parameterSearchControl.value && this.parameterSearchControl.value.trim() !== '') {
        // Si le champ contient déjà du texte, filtrer avec ce texte
        this.parameterListComponent.performSearch(this.parameterSearchControl.value);
      } else {
        // Si le champ est vide, afficher tous les paramètres de cette version
        console.log('Showing all parameters for version', this.version.uuid);
        this.parameterListComponent.showAllParametersForVersion(this.version.uuid);
      }
    }
  }

  ngOnDestroy(): void {
    this._snackBar.dismiss();
  }
}
