import { Component, OnInit, ViewChild, TemplateRef, AfterViewInit, Input, Output, EventEmitter, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Chart, registerables } from 'chart.js';

// Interface for TestExecution with UI properties
interface TestExecutionWithUIData extends TestExecution {
  expanded?: boolean;
}

// Angular Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule } from '@angular/material/dialog';

// Models
import { TestRun } from '../../models/test-run';
import { TestCase } from '../../models/test-case';
import { TestExecution } from '../../models/test-execution';

// Services
import { TestRunService } from '../../services/test-run.service';
import { TestCaseService } from '../../services/test-case.service';
import { TestExecutionService } from '../../services/test-execution.service';

// Register Chart.js components
Chart.register(...registerables);

interface Attachment {
  name: string;
  url: string;
}

@Component({
  selector: 'app-test-execution',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatExpansionModule,
    MatIconModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatOptionModule,
    MatTableModule,
    MatChipsModule,
    MatSlideToggleModule,
    MatTooltipModule,
    MatDialogModule
  ],
  templateUrl: './test-execution.component.html',
  styleUrl: './test-execution.component.scss'
})
export class TestExecutionComponent implements OnInit, AfterViewInit {
  // Input and Output
  @Input() dataInput: any;
  @Output() actionProcess = new EventEmitter<any>();

  // Dialog templates and chart canvases
  @ViewChild('expandedChartDialog') expandedChartDialog!: TemplateRef<any>;
  @ViewChild('compactExecutionResultsChart') compactChartCanvas!: ElementRef<HTMLCanvasElement>;
  @ViewChild('expandedExecutionResultsChart') expandedChartCanvas!: ElementRef<HTMLCanvasElement>;

  // Test runs and executions
  testRuns: TestRun[] = [];
  testExecutions: TestExecutionWithUIData[] = [];
  selectedTestRunId: string | null = null;
  selectedTestRun: TestRun | null = null;
  selectedExecution: TestExecutionWithUIData | null = null;

  // Table configuration
  displayedColumns: string[] = ['name', 'date', 'exploited'];

  // Report and automation
  executionReport: string = '';
  automationEnabled: boolean = false;
  executionPath: string = '';
  sourceUrl: string = '';

  // Charts
  executionResultsChart: Chart | null = null;
  compactChart: Chart | null = null;
  expandedChart: Chart | null = null;

  // Status counts for the chart
  statusCounts = {
    PASSED: 0,
    FAILED: 0,
    ERROR: 0,
    OTHER: 0
  };

  constructor(
    private testRunService: TestRunService,
    private testCaseService: TestCaseService,
    private testExecutionService: TestExecutionService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // Load all test runs
    this.loadTestRuns();

    // If there's data input with a selected version, use it
    if (this.dataInput && this.dataInput.version) {
      console.log('Data input with version:', this.dataInput.version);
    }
  }

  ngAfterViewInit(): void {
    // Initialize chart if we have data
    setTimeout(() => {
      if (this.selectedTestRun && this.testExecutions.length > 0) {
        this.initializeChart();
      }
    }, 100);
  }

  /**
   * Loads all test runs
   */
  loadTestRuns(): void {
    this.testRunService.getTestRunList().subscribe({
      next: (runs) => {
        this.testRuns = runs;
      },
      error: (err) => {
        console.error('Error loading test runs:', err);
        this.snackBar.open('Error loading test runs', 'Close', {
          duration: 3000
        });
      }
    });
  }

  /**
   * Handles test run selection change
   */
  onTestRunChange(): void {
    // Reset state
    this.testExecutions = [];
    this.selectedExecution = null;

    if (!this.selectedTestRunId) {
      this.selectedTestRun = null;
      return;
    }

    // Find the selected test run
    this.testRunService.getTestRunById(this.selectedTestRunId).subscribe({
      next: (testRun) => {
        this.selectedTestRun = testRun;
        console.log(`Selected test run: ${testRun.name} (${testRun.uuid})`);

        // Load test executions for the selected test run
        this.loadTestExecutions();
      },
      error: (err) => {
        console.error('Error loading test run:', err);
        this.snackBar.open('Error loading test run', 'Close', {
          duration: 3000
        });
      }
    });
  }

  /**
   * Loads test executions for the selected test run
   */
  loadTestExecutions(): void {
    if (!this.selectedTestRun || !this.selectedTestRun.uuid) {
      return;
    }

    // Reset current test executions and selected execution
    this.testExecutions = [];
    this.selectedExecution = null;

    this.testExecutionService.getTestExecutionsForRun(this.selectedTestRun.uuid).subscribe({
      next: (executions) => {
        console.log(`Loaded ${executions.length} test executions for test run ${this.selectedTestRun?.name} (${this.selectedTestRun?.uuid})`);

        // Filter executions to ensure they belong to the selected test run
        const filteredExecutions = executions.filter(execution => {
          const executionRunId = typeof execution.test_run === 'string'
            ? execution.test_run
            : execution.test_run?.uuid;

          return executionRunId === this.selectedTestRun?.uuid;
        });

        console.log(`After filtering: ${filteredExecutions.length} test executions`);

        // Initialize UI properties for each execution
        this.testExecutions = filteredExecutions.map(execution => ({
          ...execution,
          expanded: false
        }));

        // Load test case details for each execution
        this.testExecutions.forEach(execution => {
          if (typeof execution.test_case === 'string') {
            this.testCaseService.getTestCaseById(execution.test_case).subscribe({
              next: (testCase) => {
                execution.test_case = testCase;
              },
              error: (err) => {
                console.error(`Error loading test case ${execution.test_case}:`, err);
              }
            });
          }
        });

        // Initialize chart with the new data after a short delay to ensure DOM is ready
        setTimeout(() => {
          this.initializeChart();
        }, 300);
      },
      error: (err) => {
        console.error('Error loading test executions:', err);
        this.snackBar.open('Error loading test executions', 'Close', {
          duration: 3000
        });
      }
    });
  }

  /**
   * Checks if an execution is expanded
   * @param execution The execution to check
   * @returns True if the execution is expanded
   */
  isExpanded(execution: TestExecutionWithUIData): boolean {
    return execution.expanded === true;
  }

  /**
   * Handles expansion of a panel
   * @param execution The execution being expanded
   */
  handleExpand(execution: TestExecutionWithUIData): void {
    // Use setTimeout to avoid ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      execution.expanded = true;
      this.selectExecution(execution);
    });
  }

  /**
   * Handles collapse of a panel
   * @param execution The execution being collapsed
   */
  handleCollapse(execution: TestExecutionWithUIData): void {
    // Use setTimeout to avoid ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      execution.expanded = false;
      if (this.selectedExecution === execution) {
        this.selectedExecution = null;
      }
    });
  }

  /**
   * Selects a test execution
   * @param execution The test execution to select
   */
  selectExecution(execution: TestExecutionWithUIData): void {
    // Use setTimeout to avoid ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      this.selectedExecution = execution;
      this.executionReport = execution.report || '';
      this.automationEnabled = execution.automated || false;
      this.executionPath = execution.execution_path || '';
      this.sourceUrl = execution.source || '';
    });
  }

  /**
   * Checks if a test execution is selected
   * @param execution The test execution to check
   * @returns True if the execution is selected
   */
  isExecutionSelected(execution: TestExecutionWithUIData): boolean {
    return this.selectedExecution?.uuid === execution.uuid;
  }

  /**
   * Updates the status of the selected test execution
   * @param status The new status
   */
  updateExecutionStatus(status: string): void {
    if (!this.selectedExecution || !this.selectedExecution.uuid) {
      return;
    }

    // Store a reference to the test case to preserve it
    const originalTestCase = this.selectedExecution.test_case;

    // Prepare the test case ID
    let testCaseId: string | undefined;
    if (this.selectedExecution.test_case) {
      if (typeof this.selectedExecution.test_case === 'string') {
        testCaseId = this.selectedExecution.test_case;
      } else if (this.selectedExecution.test_case.uuid) {
        testCaseId = this.selectedExecution.test_case.uuid;
      }
    }

    // Prepare the test run ID
    let testRunId: string | undefined;
    if (this.selectedExecution.test_run) {
      if (typeof this.selectedExecution.test_run === 'string') {
        testRunId = this.selectedExecution.test_run;
      } else if (this.selectedExecution.test_run.uuid) {
        testRunId = this.selectedExecution.test_run.uuid;
      }
    }

    // Create the update payload with proper IDs
    const updatedExecution = {
      uuid: this.selectedExecution.uuid,
      status: status,
      report: this.executionReport,
      test_case: testCaseId,
      test_run: testRunId,
      automated: this.automationEnabled,
      execution_path: this.executionPath,
      source: this.sourceUrl
      // Date will be set by the backend
    };

    console.log('Updating test execution with data:', updatedExecution);

    // Use NgZone to properly handle the async operation
    this.testExecutionService.updateTestExecution(this.selectedExecution.uuid, updatedExecution).subscribe({
      next: (response) => {
        console.log('Test execution updated successfully:', response);

        // Use setTimeout to ensure updates happen in the next change detection cycle
        setTimeout(() => {
          // Update the execution in the list
          const index = this.testExecutions.findIndex(e => e.uuid === this.selectedExecution?.uuid);
          if (index !== -1) {
            // Store the expanded state
            const wasExpanded = this.testExecutions[index].expanded;

            // Create a new object to avoid reference issues
            this.testExecutions[index] = {
              ...this.testExecutions[index],
              status: status,
              report: this.executionReport,
              date: new Date(), // Update the date to current time
              test_case: originalTestCase, // Preserve the original test case object
              automated: this.automationEnabled,
              execution_path: this.executionPath,
              source: this.sourceUrl,
              expanded: wasExpanded // Preserve the expanded state
            };
          }

          // Update the selected execution
          if (this.selectedExecution) {
            // Update properties individually to avoid reference issues
            this.selectedExecution.status = status;
            this.selectedExecution.report = this.executionReport;
            this.selectedExecution.date = new Date(); // Update the date to current time
            this.selectedExecution.automated = this.automationEnabled;
            this.selectedExecution.execution_path = this.executionPath;
            this.selectedExecution.source = this.sourceUrl;
            // Keep the original test case object
            this.selectedExecution.test_case = originalTestCase;
          }

          // Update the chart
          this.initializeChart();

          this.snackBar.open(`Test execution status updated to ${status}`, 'Close', {
            duration: 3000
          });
        }, 0);
      },
      error: (err) => {
        console.error('Error updating test execution:', err);
        this.snackBar.open('Error updating test execution', 'Close', {
          duration: 3000
        });
      }
    });
  }

  /**
   * Handles automation toggle changes
   */
  onAutomationToggle(): void {
    // Don't clear the execution path when automation is disabled
    // Users should be able to keep and edit the path regardless of automation status
    this.updateAutomationFields();
  }

  /**
   * Updates automation fields in the selected execution
   */
  updateAutomationFields(): void {
    if (!this.selectedExecution) {
      return;
    }

    // Always update the selected execution fields
    // Validation will be handled in saveAutomationChanges
    this.selectedExecution.automated = this.automationEnabled;
    this.selectedExecution.execution_path = this.executionPath;
    this.selectedExecution.source = this.sourceUrl;

    // Auto-save the changes
    this.saveAutomationChanges();
  }

  /**
   * Handles content changes in the Quill editor for the execution report
   */
  onReportContentChanged(): void {
    // Auto-save the report when content changes
    this.saveExecutionReport();
  }

  /**
   * Saves the execution report to the backend
   */
  saveExecutionReport(): void {
    if (!this.selectedExecution || !this.selectedExecution.uuid) {
      return;
    }

    // Update the selected execution report
    this.selectedExecution.report = this.executionReport;

    // Prepare the test case ID
    let testCaseId: string | undefined;
    if (this.selectedExecution.test_case) {
      if (typeof this.selectedExecution.test_case === 'string') {
        testCaseId = this.selectedExecution.test_case;
      } else if (this.selectedExecution.test_case.uuid) {
        testCaseId = this.selectedExecution.test_case.uuid;
      }
    }

    // Prepare the test run ID
    let testRunId: string | undefined;
    if (this.selectedExecution.test_run) {
      if (typeof this.selectedExecution.test_run === 'string') {
        testRunId = this.selectedExecution.test_run;
      } else if (this.selectedExecution.test_run.uuid) {
        testRunId = this.selectedExecution.test_run.uuid;
      }
    }

    // Create the update payload
    const updatedExecution = {
      uuid: this.selectedExecution.uuid,
      status: this.selectedExecution.status,
      report: this.executionReport,
      test_case: testCaseId,
      test_run: testRunId,
      automated: this.selectedExecution.automated,
      execution_path: this.selectedExecution.execution_path,
      source: this.selectedExecution.source
    };

    // Save to backend
    this.testExecutionService.updateTestExecution(this.selectedExecution.uuid, updatedExecution).subscribe({
      next: (response) => {
        // Update the execution in the list
        const index = this.testExecutions.findIndex(e => e.uuid === this.selectedExecution?.uuid);
        if (index !== -1) {
          this.testExecutions[index] = {
            ...this.testExecutions[index],
            report: this.executionReport
          };
        }
        console.log('Execution report saved successfully');
      },
      error: (err) => {
        console.error('Error saving execution report:', err);
        this.snackBar.open('Error saving execution report', 'Close', {
          duration: 3000
        });
      }
    });
  }

  /**
   * Saves automation changes to the backend
   */
  saveAutomationChanges(): void {
    if (!this.selectedExecution || !this.selectedExecution.uuid) {
      return;
    }

    // Validate before saving
    if (this.automationEnabled && !this.executionPath.trim()) {
      this.snackBar.open('Execution path is required when automation is enabled', 'Close', {
        duration: 3000
      });
      return;
    }

    // Prepare the test case ID
    let testCaseId: string | undefined;
    if (this.selectedExecution.test_case) {
      if (typeof this.selectedExecution.test_case === 'string') {
        testCaseId = this.selectedExecution.test_case;
      } else if (this.selectedExecution.test_case.uuid) {
        testCaseId = this.selectedExecution.test_case.uuid;
      }
    }

    // Prepare the test run ID
    let testRunId: string | undefined;
    if (this.selectedExecution.test_run) {
      if (typeof this.selectedExecution.test_run === 'string') {
        testRunId = this.selectedExecution.test_run;
      } else if (this.selectedExecution.test_run.uuid) {
        testRunId = this.selectedExecution.test_run.uuid;
      }
    }

    // Create the update payload
    const updatedExecution = {
      uuid: this.selectedExecution.uuid,
      status: this.selectedExecution.status,
      report: this.selectedExecution.report,
      test_case: testCaseId,
      test_run: testRunId,
      automated: this.automationEnabled,
      execution_path: this.executionPath,
      source: this.sourceUrl
    };

    // Save to backend
    this.testExecutionService.updateTestExecution(this.selectedExecution.uuid, updatedExecution).subscribe({
      next: (response) => {
        // Update the execution in the list
        const index = this.testExecutions.findIndex(e => e.uuid === this.selectedExecution?.uuid);
        if (index !== -1) {
          this.testExecutions[index] = {
            ...this.testExecutions[index],
            automated: this.automationEnabled,
            execution_path: this.executionPath,
            source: this.sourceUrl
          };
        }
      },
      error: (err) => {
        console.error('Error updating automation settings:', err);
        this.snackBar.open('Error saving automation settings', 'Close', {
          duration: 3000
        });
      }
    });
  }

  /**
   * Uploads an attachment for the selected test execution
   */
  uploadAttachment(): void {
    // Simulate file upload
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.onchange = (event) => {
      const target = event.target as HTMLInputElement;
      const files = target.files;

      if (files && files.length > 0) {
        const file = files[0];

        // Add the attachment to the selected execution
        if (this.selectedExecution) {
          if (!this.selectedExecution.attachments) {
            this.selectedExecution.attachments = [];
          }

          this.selectedExecution.attachments.push({
            name: file.name,
            url: URL.createObjectURL(file)
          });

          this.snackBar.open(`Attachment ${file.name} added`, 'Close', {
            duration: 3000
          });
        }
      }
    };

    fileInput.click();
  }

  /**
   * Downloads an attachment
   * @param attachment The attachment to download
   */
  downloadAttachment(attachment: Attachment): void {
    window.open(attachment.url, '_blank');
  }

  /**
   * Removes an attachment from the selected test execution
   * @param attachment The attachment to remove
   */
  removeAttachment(attachment: Attachment): void {
    if (this.selectedExecution && this.selectedExecution.attachments) {
      this.selectedExecution.attachments = this.selectedExecution.attachments.filter(a => a.url !== attachment.url);

      this.snackBar.open(`Attachment ${attachment.name} removed`, 'Close', {
        duration: 3000
      });
    }
  }

  /**
   * Formats a date for display
   * @param date The date to format
   * @returns The formatted date string
   */
  formatDate(date?: Date | string): string {
    if (!date) {
      return 'N/A';
    }

    const d = new Date(date);
    return `${d.getDate().toString().padStart(2, '0')}-${(d.getMonth() + 1).toString().padStart(2, '0')}-${d.getFullYear()}`;
  }

  /**
   * Gets the description from a test case
   * @param testCase The test case object or string
   * @returns The test case description or a default message
   */
  getTestCaseDescription(testCase?: TestCase | string): string {
    try {
      if (!testCase) {
        return 'No description available';
      }

      if (typeof testCase === 'string') {
        return 'No description available';
      }

      return testCase.description || 'No description available';
    } catch (error) {
      console.error('Error getting test case description:', error);
      return 'No description available';
    }
  }

  /**
   * Gets the source from a test case
   * @param testCase The test case object or string
   * @returns The test case source or a default message
   */
  getTestCaseSource(testCase?: TestCase | string): string {
    try {
      if (!testCase) {
        return 'No source code available';
      }

      if (typeof testCase === 'string') {
        return 'No source code available';
      }

      return testCase.source || 'No source code available';
    } catch (error) {
      console.error('Error getting test case source:', error);
      return 'No source code available';
    }
  }

  /**
   * Gets the name from a test case
   * @param testCase The test case object or string
   * @returns The test case name or a default message
   */
  getTestCaseName(testCase?: TestCase | string): string {
    try {
      if (!testCase) {
        return 'Unnamed Test Case';
      }

      if (typeof testCase === 'string') {
        return 'Unnamed Test Case';
      }

      return testCase.name || 'Unnamed Test Case';
    } catch (error) {
      console.error('Error getting test case name:', error);
      return 'Unnamed Test Case';
    }
  }

  /**
   * Initializes the charts with test execution data
   */
  initializeChart(): void {
    console.log('Initializing charts with', this.testExecutions.length, 'test executions');

    if (!this.testExecutions.length) {
      console.log('No test executions to display in chart');
      return;
    }

    // Count executions by status
    this.statusCounts = {
      PASSED: 0,
      FAILED: 0,
      ERROR: 0,
      OTHER: 0
    };

    this.testExecutions.forEach(execution => {
      if (execution.status === 'PASSED') {
        this.statusCounts.PASSED++;
      } else if (execution.status === 'FAILED') {
        this.statusCounts.FAILED++;
      } else if (execution.status === 'ERROR') {
        this.statusCounts.ERROR++;
      } else {
        this.statusCounts.OTHER++;
      }
    });

    console.log('Status counts:', this.statusCounts);

    // Use setTimeout to ensure the DOM is fully rendered
    setTimeout(() => {
      this.initializeCompactChart();
    }, 200); // Wait 200ms for the DOM to be ready
  }

  /**
   * Initializes the compact chart in the header
   */
  private initializeCompactChart(): void {
    // Destroy existing chart if it exists
    if (this.compactChart) {
      this.compactChart.destroy();
    }

    // Get the canvas element
    const ctx = this.compactChartCanvas?.nativeElement;
    if (!ctx) {
      console.error('Compact chart canvas element not found!');
      return;
    }

    // Create the compact chart
    this.compactChart = new Chart(ctx, {
      type: 'pie',
      data: {
        labels: ['Success', 'Failed', 'Error', 'Other'],
        datasets: [{
          data: [
            this.statusCounts.PASSED,
            this.statusCounts.FAILED,
            this.statusCounts.ERROR,
            this.statusCounts.OTHER
          ],
          backgroundColor: [
            'rgba(76, 175, 80, 0.7)',  // Green for Success
            'rgba(244, 67, 54, 0.7)',  // Red for Failed
            'rgba(255, 152, 0, 0.7)',  // Orange for Error
            'rgba(33, 150, 243, 0.7)'  // Blue for Other
          ],
          borderColor: [
            'rgba(76, 175, 80, 1)',
            'rgba(244, 67, 54, 1)',
            'rgba(255, 152, 0, 1)',
            'rgba(33, 150, 243, 1)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false // Hide legend in compact view
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                const label = context.label || '';
                const value = context.raw as number;
                const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0) as number;
                const percentage = Math.round((value / total) * 100);
                return `${label}: ${value} (${percentage}%)`;
              }
            }
          }
        }
      }
    });
  }

  /**
   * Initializes the expanded chart in the dialog
   */
  private initializeExpandedChart(): void {
    // Destroy existing chart if it exists
    if (this.expandedChart) {
      this.expandedChart.destroy();
    }

    // Get the canvas element
    const ctx = this.expandedChartCanvas?.nativeElement;
    if (!ctx) {
      console.error('Expanded chart canvas element not found!');
      return;
    }

    // Create the expanded chart
    this.expandedChart = new Chart(ctx, {
      type: 'pie',
      data: {
        labels: ['Success', 'Failed', 'Error', 'Other'],
        datasets: [{
          data: [
            this.statusCounts.PASSED,
            this.statusCounts.FAILED,
            this.statusCounts.ERROR,
            this.statusCounts.OTHER
          ],
          backgroundColor: [
            'rgba(76, 175, 80, 0.7)',  // Green for Success
            'rgba(244, 67, 54, 0.7)',  // Red for Failed
            'rgba(255, 152, 0, 0.7)',  // Orange for Error
            'rgba(33, 150, 243, 0.7)'  // Blue for Other
          ],
          borderColor: [
            'rgba(76, 175, 80, 1)',
            'rgba(244, 67, 54, 1)',
            'rgba(255, 152, 0, 1)',
            'rgba(33, 150, 243, 1)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'right',
            labels: {
              padding: 20,
              font: {
                size: 14
              }
            }
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                const label = context.label || '';
                const value = context.raw as number;
                const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0) as number;
                const percentage = Math.round((value / total) * 100);
                return `${label}: ${value} (${percentage}%)`;
              }
            }
          }
        }
      }
    });
  }

  /**
   * Opens the expanded chart view dialog
   */
  openExpandedChartView(): void {
    // Open the dialog
    const dialogRef = this.dialog.open(this.expandedChartDialog, {
      width: '800px',
      maxWidth: '90vw',
      maxHeight: '90vh'
    });

    // Initialize the expanded chart after the dialog is open
    dialogRef.afterOpened().subscribe(() => {
      setTimeout(() => {
        this.initializeExpandedChart();
      }, 100);
    });
  }
}
