import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { of, throwError } from 'rxjs';

import { TestExecutionComponent } from './test-execution.component';
import { TestExecutionService } from '../../services/test-execution.service';
import { TestRunService } from '../../services/test-run.service';
import { TestCaseService } from '../../services/test-case.service';
import { TestExecution } from '../../models/test-execution';
import { TestCase } from '../../models/test-case';
import { TestRun } from '../../models/test-run';

describe('TestExecutionComponent', () => {
  let component: TestExecutionComponent;
  let fixture: ComponentFixture<TestExecutionComponent>;
  let mockTestExecutionService: jasmine.SpyObj<TestExecutionService>;
  let mockTestRunService: jasmine.SpyObj<TestRunService>;
  let mockTestCaseService: jasmine.SpyObj<TestCaseService>;
  let mockSnackBar: jasmine.SpyObj<MatSnackBar>;
  let mockDialog: jasmine.SpyObj<MatDialog>;

  beforeEach(async () => {
    const testExecutionServiceSpy = jasmine.createSpyObj('TestExecutionService', [
      'updateTestExecution', 'getTestExecutionsForRun'
    ]);
    const testRunServiceSpy = jasmine.createSpyObj('TestRunService', ['getTestRunList']);
    const testCaseServiceSpy = jasmine.createSpyObj('TestCaseService', ['getTestCaseById']);
    const snackBarSpy = jasmine.createSpyObj('MatSnackBar', ['open']);
    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);

    await TestBed.configureTestingModule({
      imports: [TestExecutionComponent],
      providers: [
        { provide: TestExecutionService, useValue: testExecutionServiceSpy },
        { provide: TestRunService, useValue: testRunServiceSpy },
        { provide: TestCaseService, useValue: testCaseServiceSpy },
        { provide: MatSnackBar, useValue: snackBarSpy },
        { provide: MatDialog, useValue: dialogSpy }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(TestExecutionComponent);
    component = fixture.componentInstance;

    mockTestExecutionService = TestBed.inject(TestExecutionService) as jasmine.SpyObj<TestExecutionService>;
    mockTestRunService = TestBed.inject(TestRunService) as jasmine.SpyObj<TestRunService>;
    mockTestCaseService = TestBed.inject(TestCaseService) as jasmine.SpyObj<TestCaseService>;
    mockSnackBar = TestBed.inject(MatSnackBar) as jasmine.SpyObj<MatSnackBar>;
    mockDialog = TestBed.inject(MatDialog) as jasmine.SpyObj<MatDialog>;

    // Setup default mocks
    mockTestRunService.getTestRunList.and.returnValue(of([]));
    mockTestExecutionService.getTestExecutionsForRun.and.returnValue(of([]));

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Automation and Execution Path Validation', () => {
    let mockTestExecution: TestExecution;

    beforeEach(() => {
      mockTestExecution = new TestExecution({
        uuid: 'test-execution-1',
        status: 'IDLE',
        report: 'Test report',
        test_case: 'test-case-1',
        test_run: 'test-run-1',
        automated: false,
        execution_path: '',
        source: ''
      });

      component.selectedExecution = mockTestExecution;
      component.automationEnabled = false;
      component.executionPath = '';
      component.sourceUrl = '';
    });

    it('should allow setting execution path when automation is disabled', () => {
      // Arrange
      component.automationEnabled = false;
      component.executionPath = '/path/to/test';
      mockTestExecutionService.updateTestExecution.and.returnValue(of({}));

      // Act
      component.updateAutomationFields();

      // Assert
      expect(component.selectedExecution?.execution_path).toBe('/path/to/test');
      expect(component.selectedExecution?.automated).toBe(false);
      expect(mockTestExecutionService.updateTestExecution).toHaveBeenCalled();
    });

    it('should preserve execution path when toggling automation off', () => {
      // Arrange
      component.automationEnabled = true;
      component.executionPath = '/existing/path';
      mockTestExecutionService.updateTestExecution.and.returnValue(of({}));

      // Act - toggle automation off
      component.automationEnabled = false;
      component.onAutomationToggle();

      // Assert
      expect(component.executionPath).toBe('/existing/path');
      expect(component.selectedExecution?.execution_path).toBe('/existing/path');
      expect(component.selectedExecution?.automated).toBe(false);
    });

    it('should allow enabling automation when execution path is provided', () => {
      // Arrange
      component.automationEnabled = true;
      component.executionPath = '/valid/path';
      mockTestExecutionService.updateTestExecution.and.returnValue(of({}));

      // Act
      component.updateAutomationFields();

      // Assert
      expect(component.selectedExecution?.automated).toBe(true);
      expect(component.selectedExecution?.execution_path).toBe('/valid/path');
      expect(mockTestExecutionService.updateTestExecution).toHaveBeenCalled();
      expect(mockSnackBar.open).not.toHaveBeenCalledWith(
        jasmine.stringMatching(/required/),
        jasmine.any(String),
        jasmine.any(Object)
      );
    });

    it('should show error when enabling automation without execution path', () => {
      // Arrange
      component.automationEnabled = true;
      component.executionPath = '';
      mockTestExecutionService.updateTestExecution.and.returnValue(of({}));

      // Act
      spyOn(component, 'saveAutomationChanges').and.callThrough();
      component.updateAutomationFields();

      // Assert
      expect(mockSnackBar.open).toHaveBeenCalledWith(
        'Execution path is required when automation is enabled',
        'Close',
        { duration: 3000 }
      );
    });

    it('should show error when enabling automation with whitespace-only execution path', () => {
      // Arrange
      component.automationEnabled = true;
      component.executionPath = '   ';
      mockTestExecutionService.updateTestExecution.and.returnValue(of({}));

      // Act
      spyOn(component, 'saveAutomationChanges').and.callThrough();
      component.updateAutomationFields();

      // Assert
      expect(mockSnackBar.open).toHaveBeenCalledWith(
        'Execution path is required when automation is enabled',
        'Close',
        { duration: 3000 }
      );
    });

    it('should update source URL independently of automation status', () => {
      // Arrange
      component.automationEnabled = false;
      component.sourceUrl = 'https://example.com/source';
      mockTestExecutionService.updateTestExecution.and.returnValue(of({}));

      // Act
      component.updateAutomationFields();

      // Assert
      expect(component.selectedExecution?.source).toBe('https://example.com/source');
      expect(mockTestExecutionService.updateTestExecution).toHaveBeenCalled();
    });

    it('should handle backend error when saving automation changes', () => {
      // Arrange
      component.automationEnabled = false;
      component.executionPath = '/test/path';
      mockTestExecutionService.updateTestExecution.and.returnValue(
        throwError(() => new Error('Backend error'))
      );

      // Act
      component.updateAutomationFields();

      // Assert
      expect(mockSnackBar.open).toHaveBeenCalledWith(
        'Error saving automation settings',
        'Close',
        { duration: 3000 }
      );
    });
  });

  describe('Execution Report Auto-Save', () => {
    let mockTestExecution: TestExecution;

    beforeEach(() => {
      mockTestExecution = new TestExecution({
        uuid: 'test-execution-1',
        status: 'IDLE',
        report: 'Initial report',
        test_case: 'test-case-1',
        test_run: 'test-run-1',
        automated: false,
        execution_path: '',
        source: ''
      });

      component.selectedExecution = mockTestExecution;
      component.executionReport = 'Initial report';
    });

    it('should save execution report when saveExecutionReport is called', () => {
      // Arrange
      component.executionReport = 'Updated report content';
      mockTestExecutionService.updateTestExecution.and.returnValue(of({}));

      // Act
      component.saveExecutionReport();

      // Assert
      expect(component.selectedExecution?.report).toBe('Updated report content');
      expect(mockTestExecutionService.updateTestExecution).toHaveBeenCalledWith(
        'test-execution-1',
        jasmine.objectContaining({
          report: 'Updated report content'
        })
      );
    });

    it('should update execution in list when report is saved successfully', () => {
      // Arrange
      component.executionReport = 'New report';
      component.testExecutions = [mockTestExecution];
      mockTestExecutionService.updateTestExecution.and.returnValue(of({}));

      // Act
      component.saveExecutionReport();

      // Assert
      expect(component.testExecutions[0].report).toBe('New report');
    });

    it('should handle error when saving execution report fails', () => {
      // Arrange
      component.executionReport = 'Failed report';
      mockTestExecutionService.updateTestExecution.and.returnValue(
        throwError(() => new Error('Save failed'))
      );

      // Act
      component.saveExecutionReport();

      // Assert
      expect(mockSnackBar.open).toHaveBeenCalledWith(
        'Error saving execution report',
        'Close',
        { duration: 3000 }
      );
    });

    it('should not save when no execution is selected', () => {
      // Arrange
      component.selectedExecution = null;
      component.executionReport = 'Some report';

      // Act
      component.saveExecutionReport();

      // Assert
      expect(mockTestExecutionService.updateTestExecution).not.toHaveBeenCalled();
    });

    it('should not save when execution has no UUID', () => {
      // Arrange
      component.selectedExecution = new TestExecution({
        status: 'IDLE',
        report: 'Test report'
        // No UUID
      });
      component.executionReport = 'Some report';

      // Act
      component.saveExecutionReport();

      // Assert
      expect(mockTestExecutionService.updateTestExecution).not.toHaveBeenCalled();
    });
  });
});
