<div class="container">
  <!-- Title and test run selector -->
  <div class="header-section d-flex justify-content-between align-items-center mb-4">
    <h2>
      <span class="text-primary">Test Execution - Collapsible Version</span>
    </h2>
    <div class="d-flex align-items-center">
      <mat-form-field appearance="outline">
        <mat-label>Select Test Run</mat-label>
        <mat-select [(ngModel)]="selectedTestRunId" (selectionChange)="onTestRunChange()">
          <mat-option *ngFor="let run of testRuns" [value]="run.uuid">
            {{ run.name }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </div>

  <!-- Test Run Details Card -->
  <div class="row mb-5" *ngIf="selectedTestRun">
    <div class="col-md-8">
      <mat-card class="test-run-card h-100">
        <mat-card-header>
          <mat-card-title>{{ selectedTestRun.name }}</mat-card-title>
          <mat-card-subtitle *ngIf="selectedTestRun.start_date">Started: {{ formatDate(selectedTestRun.start_date) }}</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="test-run-info">
            <div class="test-run-details">
              <p *ngIf="selectedTestRun.description" class="description">{{ selectedTestRun.description }}</p>
              <p *ngIf="selectedTestRun.end_date"><strong>End Date:</strong> {{ formatDate(selectedTestRun.end_date) }}</p>
              <p *ngIf="testExecutions.length"><strong>Test Cases:</strong> {{ testExecutions.length }}</p>
            </div>
            <div class="test-run-status">
              <mat-icon class="status-icon" color="primary">check_circle</mat-icon>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Results Chart (Compact View) -->
    <div class="col-md-4" *ngIf="testExecutions.length > 0">
      <mat-card class="chart-card h-100">
        <mat-card-header>
          <mat-card-title class="d-flex justify-content-between align-items-center">
            <span>Test Results</span>
            <button mat-icon-button color="primary" (click)="openExpandedChartView()" matTooltip="Expand Chart">
              <mat-icon>open_in_full</mat-icon>
            </button>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="compact-chart-container">
            <canvas #compactExecutionResultsChart id="compactExecutionResultsChart"></canvas>
          </div>
          <div class="chart-summary">
            <span class="summary-item success">{{ statusCounts.PASSED }}</span>
            <span class="summary-item failed">{{ statusCounts.FAILED }}</span>
            <span class="summary-item error">{{ statusCounts.ERROR }}</span>
            <span class="summary-item other">{{ statusCounts.OTHER }}</span>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <!-- Test Executions List -->
  <div class="test-executions-container" *ngIf="selectedTestRun">
    <div class="execution-list">
      <mat-accordion>
        <mat-expansion-panel *ngFor="let execution of testExecutions; let i = index"
                            [expanded]="isExpanded(execution)"
                            (afterExpand)="handleExpand(execution)"
                            (afterCollapse)="handleCollapse(execution)"
                            class="execution-panel mb-3">
          <mat-expansion-panel-header>
            <mat-panel-title class="d-flex align-items-center">
              {{ getTestCaseName(execution.test_case) }}
              <mat-icon *ngIf="execution.automated" class="ms-2 text-primary" matTooltip="Automated Test">smart_toy</mat-icon>
            </mat-panel-title>
            <mat-panel-description class="d-flex justify-content-between align-items-center">
              <span>{{ formatDate(execution.date) }}</span>
              <span class="status-indicator">
                <mat-icon *ngIf="execution.status === 'PASSED'" class="status-icon success">check_circle</mat-icon>
                <mat-icon *ngIf="execution.status === 'FAILED'" class="status-icon failed">cancel</mat-icon>
                <mat-icon *ngIf="execution.status === 'ERROR'" class="status-icon error">error</mat-icon>
                <mat-icon *ngIf="execution.status === 'IDLE' || execution.status === 'RUNNING'" class="status-icon pending">pending</mat-icon>
              </span>
            </mat-panel-description>
          </mat-expansion-panel-header>

          <!-- Expanded Content -->
          <div class="execution-details p-3">
            <div class="row">
              <div class="col-md-4">
                <div class="description-panel p-3 border rounded h-100">
                  <h4>Description</h4>
                  <p>{{ getTestCaseDescription(execution.test_case) }}</p>
                </div>
              </div>
              <div class="col-md-8">
                <div class="report-panel p-3 border rounded" *ngIf="selectedExecution">
                  <h4 class="mb-3">Execution Report</h4>
                  <!-- Debug info -->
                  <small class="text-muted mb-2 d-block">Debug: executionReport = "{{ executionReport }}"</small>
                  <quill-editor
                    [(ngModel)]="executionReport"
                    (onContentChanged)="onReportContentChanged()"
                    placeholder="Enter execution report...">
                  </quill-editor>
                  <div class="status-buttons d-flex justify-content-center mt-2">
                    <button mat-mini-fab color="primary" class="mx-2" matTooltip="Mark as Passed" (click)="updateExecutionStatus('PASSED')">
                      <mat-icon>check</mat-icon>
                    </button>
                    <button mat-mini-fab class="mx-2 status-pending" matTooltip="Mark as Running" (click)="updateExecutionStatus('RUNNING')">
                      <mat-icon>play_arrow</mat-icon>
                    </button>
                    <button mat-mini-fab color="warn" class="mx-2" matTooltip="Mark as Failed" (click)="updateExecutionStatus('FAILED')">
                      <mat-icon>close</mat-icon>
                    </button>
                    <button mat-mini-fab class="mx-2 status-error" matTooltip="Mark as Error" (click)="updateExecutionStatus('ERROR')">
                      <mat-icon>error</mat-icon>
                    </button>
                  </div>
                </div>
                <!-- Automation Configuration Panel -->
                <div class="automation-config-panel p-3 border rounded mt-3" *ngIf="selectedExecution">
                  <h5 class="d-flex align-items-center mb-3">
                    <mat-icon class="me-2">smart_toy</mat-icon>
                    Automation Configuration
                  </h5>

                  <div class="row">
                    <div class="col-md-12 mb-3">
                      <mat-slide-toggle
                        [(ngModel)]="automationEnabled"
                        (change)="onAutomationToggle()"
                        class="automation-toggle">
                        Enable Automation
                      </mat-slide-toggle>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <mat-form-field appearance="outline" class="w-100">
                        <mat-label>Execution Path</mat-label>
                        <input
                          matInput
                          [(ngModel)]="executionPath"
                          placeholder="Enter execution path"
                          (blur)="updateAutomationFields()">
                        <mat-hint *ngIf="automationEnabled">Required when automation is enabled</mat-hint>
                        <mat-hint *ngIf="!automationEnabled">Optional - can be set for future automation</mat-hint>
                        <mat-error *ngIf="automationEnabled && !executionPath.trim()">
                          Execution path is required when automation is enabled
                        </mat-error>
                      </mat-form-field>
                    </div>

                    <div class="col-md-6 mb-3">
                      <mat-form-field appearance="outline" class="w-100">
                        <mat-label>Source URL</mat-label>
                        <input
                          matInput
                          type="url"
                          [(ngModel)]="sourceUrl"
                          placeholder="Enter source URL (optional)"
                          (blur)="updateAutomationFields()">
                        <mat-hint>Optional URL to source code or documentation</mat-hint>
                      </mat-form-field>
                    </div>
                  </div>

                  <!-- Display current values -->
                  <div class="current-values mt-2" *ngIf="selectedExecution">
                    <div class="row">
                      <div class="col-md-4">
                        <small class="text-muted">
                          <strong>Status:</strong>
                          <span class="ms-1">
                            <mat-icon *ngIf="automationEnabled" class="small-icon text-success">check_circle</mat-icon>
                            <mat-icon *ngIf="!automationEnabled" class="small-icon text-muted">cancel</mat-icon>
                            {{ automationEnabled ? 'Automated' : 'Manual' }}
                          </span>
                        </small>
                      </div>
                      <div class="col-md-4" *ngIf="executionPath">
                        <small class="text-muted">
                          <strong>Path:</strong>
                          <code class="ms-1">{{ executionPath }}</code>
                        </small>
                      </div>
                      <div class="col-md-4" *ngIf="sourceUrl">
                        <small class="text-muted">
                          <strong>Source:</strong>
                          <a [href]="sourceUrl" target="_blank" class="ms-1">
                            <mat-icon class="tiny-icon">open_in_new</mat-icon>
                            View
                          </a>
                        </small>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="attachment-panel p-3 border rounded mt-3">
                  <h4>Attachment</h4>
                  <div class="d-flex align-items-center">
                    <button mat-stroked-button color="primary" (click)="uploadAttachment()">
                      <mat-icon class="me-2">attach_file</mat-icon> Upload Attachment
                    </button>
                    <span *ngIf="selectedExecution && selectedExecution.attachments?.length" class="ms-3">
                      {{ selectedExecution.attachments?.length || 0 }} attachment(s)
                    </span>
                  </div>
                  <div *ngIf="selectedExecution && selectedExecution.attachments?.length" class="attachment-list mt-3">
                    <mat-chip-set>
                      <mat-chip *ngFor="let attachment of selectedExecution?.attachments || []" (click)="downloadAttachment(attachment)">
                        {{ attachment.name }}
                        <button matChipRemove (click)="removeAttachment(attachment)">
                          <mat-icon>cancel</mat-icon>
                        </button>
                      </mat-chip>
                    </mat-chip-set>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </mat-expansion-panel>
      </mat-accordion>
    </div>
  </div>



  <!-- No Test Run Selected Message -->
  <div *ngIf="!selectedTestRun" class="no-test-run-message text-center p-5">
    <mat-icon class="large-icon">assignment</mat-icon>
    <h3>No Test Run Selected</h3>
    <p>Please select a test run from the dropdown above to view and manage test executions.</p>
  </div>
</div>



<!-- Expanded Chart Dialog -->
<ng-template #expandedChartDialog>
  <h2 mat-dialog-title>Test Execution Results</h2>
  <mat-dialog-content>
    <div class="expanded-chart-container">
      <canvas #expandedExecutionResultsChart id="expandedExecutionResultsChart"></canvas>
    </div>
    <div class="chart-legend mt-4">
      <div class="legend-item">
        <div class="legend-color success"></div>
        <span>Success: Tests that passed successfully</span>
      </div>
      <div class="legend-item">
        <div class="legend-color failed"></div>
        <span>Failed: Tests that failed during execution</span>
      </div>
      <div class="legend-item">
        <div class="legend-color error"></div>
        <span>Error: Tests that encountered an error</span>
      </div>
      <div class="legend-item">
        <div class="legend-color other"></div>
        <span>Other: Tests in other states (idle, running, etc.)</span>
      </div>
    </div>
    <div class="chart-stats mt-4">
      <h3>Summary</h3>
      <div class="row">
        <div class="col-md-3 text-center">
          <div class="stat-value success-text">{{statusCounts.PASSED}}</div>
          <div class="stat-label">Passed</div>
        </div>
        <div class="col-md-3 text-center">
          <div class="stat-value failed-text">{{statusCounts.FAILED}}</div>
          <div class="stat-label">Failed</div>
        </div>
        <div class="col-md-3 text-center">
          <div class="stat-value error-text">{{statusCounts.ERROR}}</div>
          <div class="stat-label">Error</div>
        </div>
        <div class="col-md-3 text-center">
          <div class="stat-value other-text">{{statusCounts.OTHER}}</div>
          <div class="stat-label">Other</div>
        </div>
      </div>
    </div>
  </mat-dialog-content>
  <mat-dialog-actions align="end">
    <button mat-button mat-dialog-close>Close</button>
  </mat-dialog-actions>
</ng-template>
