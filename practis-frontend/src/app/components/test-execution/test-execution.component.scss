// General styles
.container {
  padding: 20px;
}

// Header styles
.header-section {
  margin-bottom: 30px;
}

// Test run card
.test-run-card {
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .mat-card-header {
    margin-bottom: 10px;

    .mat-card-title {
      font-size: 18px;
      margin-bottom: 5px;
    }

    .mat-card-subtitle {
      margin-bottom: 0;
    }
  }
}

.test-run-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  height: 100%;

  .test-run-details {
    flex: 1;

    p {
      margin-bottom: 8px;
      color: #666;

      &.description {
        font-style: italic;
        margin-bottom: 15px;
        white-space: pre-line;
      }

      strong {
        color: #333;
        font-weight: 500;
      }
    }
  }
}

.status-icon {
  font-size: 24px;
  margin-left: 15px;

  &.success {
    color: #4caf50;
  }

  &.failed {
    color: #f44336;
  }

  &.error {
    color: #ff9800;
  }

  &.pending {
    color: #2196f3;
  }
}

// Execution panel styles
.test-executions-container {
  margin-bottom: 30px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.execution-list {
  margin-bottom: 20px;
}

.execution-panel {
  margin-bottom: 10px;

  .mat-expansion-panel-header {
    padding: 0 16px;

    &.mat-expanded {
      background-color: #e3f2fd;
    }
  }

  .mat-expansion-panel-header-title {
    font-weight: 500;
    flex: 1;
  }

  .mat-expansion-panel-header-description {
    justify-content: space-between;
    align-items: center;
    flex: 2;
  }

  &:hover {
    .mat-expansion-panel-header:not(.mat-expanded) {
      background-color: #f5f5f5;
    }
  }
}

// Execution details
.execution-details {
  background-color: #fff;
  border-radius: 4px;
  margin-top: 20px;
}

.description-panel, .report-panel, .attachment-panel, .automation-config-panel {
  background-color: #fff;

  h4, h5 {
    margin-top: 0;
    margin-bottom: 15px;
    font-weight: 500;
  }
}

// Automation configuration panel
.automation-config-panel {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;

  h5 {
    color: #1976d2;
    margin-bottom: 20px;
    font-size: 16px;
  }

  .automation-toggle {
    margin-bottom: 10px;
  }

  .mat-form-field {
    margin-bottom: 0;
  }

  .current-values {
    border-top: 1px solid #e0e0e0;
    padding-top: 15px;
    margin-top: 15px;

    small {
      display: block;
      margin-bottom: 5px;
    }

    code {
      background-color: #f5f5f5;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 12px;
      color: #333;
    }
  }

  .small-icon {
    font-size: 16px;
    vertical-align: middle;
  }

  .tiny-icon {
    font-size: 14px;
    vertical-align: middle;
  }

  a {
    color: #1976d2;
    text-decoration: none;
    display: inline-flex;
    align-items: center;

    &:hover {
      text-decoration: underline;
    }
  }

  .text-success {
    color: #4caf50 !important;
  }

  .text-muted {
    color: #9e9e9e !important;
  }
}

// Editor toolbar
.editor-toolbar {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 5px;
  margin-bottom: 10px;
}

// Status buttons
.status-buttons {
  margin-top: 15px;

  .status-pending {
    background-color: #2196f3;
    color: white;
  }

  .status-error {
    background-color: #ff9800;
    color: white;
  }
}

// Charts
.chart-card {
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .mat-card-header {
    padding-top: 8px;

    .mat-card-title {
      font-size: 16px;
      margin-bottom: 0;
    }
  }
}

.compact-chart-container {
  height: 130px;
  position: relative;
  margin: 0 auto;

  canvas {
    width: 100% !important;
    height: 100% !important;
    display: block;
  }
}

.chart-summary {
  display: flex;
  justify-content: center;
  margin-top: 10px;

  .summary-item {
    display: inline-block;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin: 0 5px;
    line-height: 24px;
    text-align: center;
    font-size: 12px;
    font-weight: bold;
    color: white;

    &.success {
      background-color: rgba(76, 175, 80, 0.7);
    }

    &.failed {
      background-color: rgba(244, 67, 54, 0.7);
    }

    &.error {
      background-color: rgba(255, 152, 0, 0.7);
    }

    &.other {
      background-color: rgba(33, 150, 243, 0.7);
    }
  }
}

.expanded-chart-container {
  height: 350px;
  position: relative;
  margin: 0 auto;
  max-width: 700px;

  canvas {
    width: 100% !important;
    height: 100% !important;
    display: block;
  }
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 20px;

  .legend-item {
    display: flex;
    align-items: center;
    margin: 0 15px 10px;

    .legend-color {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      margin-right: 8px;

      &.success {
        background-color: rgba(76, 175, 80, 0.7);
      }

      &.failed {
        background-color: rgba(244, 67, 54, 0.7);
      }

      &.error {
        background-color: rgba(255, 152, 0, 0.7);
      }

      &.other {
        background-color: rgba(33, 150, 243, 0.7);
      }
    }

    span {
      font-size: 14px;
      color: #666;
    }
  }
}

.chart-stats {
  text-align: center;

  h3 {
    margin-bottom: 20px;
    font-weight: 500;
  }

  .stat-value {
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 5px;

    &.success-text {
      color: rgba(76, 175, 80, 1);
    }

    &.failed-text {
      color: rgba(244, 67, 54, 1);
    }

    &.error-text {
      color: rgba(255, 152, 0, 1);
    }

    &.other-text {
      color: rgba(33, 150, 243, 1);
    }
  }

  .stat-label {
    font-size: 14px;
    color: #666;
  }
}

// No test run message
.no-test-run-message {
  text-align: center;
  padding: 50px 0;
  color: #666;

  .large-icon {
    font-size: 64px;
    height: 64px;
    width: 64px;
    color: #ccc;
    margin-bottom: 20px;
  }

  h3 {
    margin-bottom: 10px;
    font-weight: 500;
  }
}

// Source code
.source-code {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  max-height: 500px;
  overflow: auto;
  font-family: monospace;
  white-space: pre-wrap;
}

// Dark theme overrides
:host-context(.dark-theme) {
  .bg-light-blue {
    background-color: #1e3a5f;
  }

  .mat-row {
    &:hover {
      background-color: #2c2c2c;
    }

    &.selected-row {
      background-color: #1e3a5f;
    }

    &.expanded-row {
      background-color: #2c2c2c;
    }
  }

  .execution-details, .description-panel, .report-panel, .attachment-panel, .automation-config-panel {
    background-color: #333;
  }

  .automation-config-panel {
    background-color: #2c2c2c;
    border-color: #555;

    h5 {
      color: #64b5f6;
    }

    .current-values {
      border-color: #555;

      code {
        background-color: #1e1e1e;
        color: #e0e0e0;
      }
    }

    a {
      color: #64b5f6;
    }
  }

  .editor-toolbar {
    background-color: #2c2c2c;
  }

  .source-code {
    background-color: #2c2c2c;
    color: #e0e0e0;
  }

  .no-test-run-message {
    color: #aaa;

    .large-icon {
      color: #555;
    }
  }
}