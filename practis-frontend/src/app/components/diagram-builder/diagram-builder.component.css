.diagram-container {
  flex: 1;
  border: 1px solid #dee2e6;
  width: 100%;
  height: auto;
  position: relative;
  /*margin-top: 10px;*/
  background: white;
  border-radius: 0px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: auto;
  background-color: white;
  position: relative;
  overflow: hidden;
}

.diagram-header {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.diagram-header button {
  padding: 5px 10px;
}

.element-form {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: #fff;
  border: 1px solid #ccc;
  padding: 15px;
  z-index: 1000;
  box-shadow: 2px 2px 5px rgba(0,0,0,0.3);
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.element-form div {
  margin-bottom: 10px;
}

.element-form label {
  display: block;
  font-weight: bold;
}

.form-buttons {
  display: flex;
  gap: 8px;
}

.form-buttons {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.diagram-layout {
  display: grid;
  grid-template-columns: 250px 1fr;
  height: 100vh;
  background-color: #f8f9fa;
  padding: 1rem;
}

.legend {
  width: 200px;
  padding: 20px;
  background: #f5f5f5;
}

.draggable-component {
  cursor: grab;
  margin-bottom: 10px;
  padding: 0.5rem;
  border-radius: 4px;
  transition: transform 0.2s;
}

.draggable-component:hover {
  transform: translateY(-2px);
}

.preview-box.component {
  background: #3498db;
  color: white;
  padding: 10px;
  border-radius: 4px;
  text-align: center;
}

.preview-box.subcomponent {
  background: #9b59b6;
  color: white;
  padding: 10px;
  border-radius: 4px;
  text-align: center;
}

.preview-box.port {
  background: #f1c40f;
  color: black;
  padding: 10px;
  border-radius: 50%;
  text-align: center;
  width: 20px;
  height: 20px;
  margin: 0 auto;
}

.form-overlay {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  z-index: 1000;
  min-width: 300px;
}

.diagram-builder {
  display: grid;
  grid-template-columns: 200px 1fr;
  height: 100vh;
  background-color: #ecf0f1;
}

.toolbox {
  padding: 1rem;
  background: white;
  border-right: 1px solid #dee2e6;
  box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

.toolbox h3 {
  margin-bottom: 1rem;
  color: #2c3e50;
}

.element-palette {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.palette-item {
  cursor: grab;
  padding: 0.5rem;
  border-radius: 4px;
  background: white;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.diagram-area {
  flex: 1;
  position: relative;
  overflow: auto;
  /*padding: 2rem;*/
}

.preview {
  padding: 1rem;
  text-align: center;
  border-radius: 4px;
}

.preview.component {
  background: #3498db;
  color: white;
}

.preview.subcomponent {
  background: #9b59b6;
  color: white;
}

.preview.port {
  background: #f1c40f;
  width: 20px;
  height: 20px;
  margin: 0 auto;
}

.preview-element {
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  border-radius: 4px;
  cursor: grab;
  user-select: none;
  transition: transform 0.2s;
}

.preview-element:hover {
  transform: translateY(-2px);
}

.preview-component {
  background: #3498db;
  color: white;
}

.preview-subcomponent {
  background: #9b59b6;
  color: white;
}

.preview-port {
  background: #f1c40f;
  width: 20px;
  height: 20px;
  margin: 0 auto;
  border-radius: 3px;
}

.form-panel {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-group {
  margin-bottom: 1rem;
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #2c3e50;
  margin-bottom: 5px;
  color: #333;
  font-size: 0.95rem;
}

.form-group input[type="text"],
.form-group textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.form-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.form-buttons button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.form-buttons button[type="submit"] {
  background: #3498db;
  color: white;
}

.form-buttons button[type="button"] {
  background: #e74c3c;
  color: white;
}

.cancel-btn {
  background-color: #dc3545; /* Rouge */
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
}

.cancel-btn:hover {
  background-color: #c82333;
}

.submit-btn {
  background-color: #007bff; /* Bleu - ou votre couleur actuelle */
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
}

.submit-btn:hover {
  background-color: #0069d9;
}

.submit-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.interface-types {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 10px;
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.interface-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  margin-bottom: 0.5rem;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  background: white;
}

.interface-btn.active {
  background: #e8f4fd;
  border-color: #3498db;
}

.arrow {
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
}

.arrow.red {
  border-left: 10px solid #e74c3c;
}

.arrow.green {
  border-left: 10px solid #2ecc71;
}

.interface-btn:hover {
  background: #f8f9fa;
}

.interface-btn.active:hover {
  background: #d1e9fc;
}

.container-full {
  display: flex;
  width: 100%;
  height: 100vh;
}

.sidebar {
  width: 250px;
  padding: 20px;
  background-color: #f5f5f5;
  border-right: 1px solid #ddd;
}

.toolbar {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.tool-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: move;
}

.tool-preview {
  width: 40px;
  height: 40px;
  border-radius: 4px;
}

.tool-preview.component {
  background-color: #3498db;
}

.tool-preview.subcomponent {
  background-color: #9b59b6;
}

.tool-preview.port {
 
  background-color: #f1c40f;
}

.interface-types button {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem !important;
}

/* État normal */
.interface-types button:not(.active) {
  background-color: white;
  color: #333;
  border-color: #ddd;
}

/* État actif pour Internal */
.interface-types button.active[type="internal"] {
  background-color: #e74c3c;
  color: white;
  border-color: #c0392b;
}

/* État actif pour External */
.interface-types button.active[type="external"] {
  background-color: #2ecc71;
  color: white;
  border-color: #27ae60;
}

/* Effet hover pour les boutons non actifs */
.interface-types button:not(.active):hover {
  background-color: #f5f5f5;
}

/* Bouton External actif */
.interface-types button.active[type="external"],
.interface-types button.active[type="external"]:hover {
  background-color: #2ecc71;
  color: white;
  border-color: #27ae60;
}

/* Bouton Internal actif */
.interface-types button.active[type="internal"],
.interface-types button.active[type="internal"]:hover {
  background-color: #e74c3c;
  color: white;
  border-color: #c0392b;
}

/* Effet hover pour les boutons non actifs */
.interface-types button:hover {
  background-color: #f5f5f5;
}

h2 {
  margin: 20px 0 10px;
  font-size: 1.1rem;
  color: #333;
}

:host ::ng-deep .joint-paper {
  background-color: transparent;
}

.form-control {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.95rem;
}

.form-control:focus {
  border-color: #3498db;
  outline: none;
  box-shadow: 0 0 0 2px rgba(52,152,219,0.2);
}

.floating-form {
  position: fixed;
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.2);
  z-index: 1000;
  min-width: 200px;
}

.floating-form input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 10px;
  font-size: 0.95rem;
}

.form-buttons {
  display: flex;
  gap: 8px;
}

.form-buttons button {
  flex: 1;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.95rem;
}

.form-buttons button[type="submit"] {
  background-color: #2ecc71;
  color: white;
}

.form-buttons button[type="button"] {
  background-color: #e74c3c;
  color: white;
}

/* Add these styles to your existing CSS */

.form-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.form-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  width: 400px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.checkbox-group {
  display: flex;
  gap: 20px;
}

.checkbox-group div {
  display: flex;
  align-items: center;
  gap: 5px;
}

.checkbox-group input {
  margin-right: 5px;
}

.component-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.component-form-container {
  padding: 20px 20px 10px 20px !important;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  width: 600px;
  max-width: 90%;
}

.component-form-container .mat-mdc-dialog-title h4{
  color: var(--mdc-dialog-subhead-color, var(--mat-app-on-surface, rgba(0, 0, 0, 0.87)));
  font-family: var(--mdc-dialog-subhead-font, var(--mat-app-headline-small-font, inherit));
  line-height: var(--mdc-dialog-subhead-line-height, var(--mat-app-headline-small-line-height, 1.5rem));
  font-size: var(--mdc-dialog-subhead-size, var(--mat-app-headline-small-size, 1rem));
  font-weight: var(--mdc-dialog-subhead-weight, var(--mat-app-headline-small-weight, 400));
  letter-spacing: var(--mdc-dialog-subhead-tracking, var(--mat-app-headline-small-tracking, 0.03125em));
}

.mat-mdc-dialog-title {
  display: block;
  position: relative;
  flex-shrink: 0;
  box-sizing: border-box;
  margin: 0 0 1px;
  padding: 10px 0px 10px 8px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.checkbox-group {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.checkbox-group div {
  display: flex;
  align-items: center;
}

.checkbox-group input {
  width: auto;
  margin-right: 5px;
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.form-buttons button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.form-buttons button[type="submit"] {
  background-color: #3498db;
  color: white;
}

.form-buttons button[type="button"] {
  background-color: #e0e0e0;
  color: white;
}

/* Ajoutez à votre fichier CSS */

.parameter-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.inline-group {
  margin-right: 10px;
  flex: 1;
}

.checkbox-inline {
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.checkbox-inline input {
  margin-right: 5px;
}

.remove-btn {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  font-size: 1.1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-btn {
  background: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  margin-top: 10px;
  cursor: pointer;
}

/* Styles pour les paramètres */
.parameter-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.parameter-inputs {
  display: flex;
  flex: 1;
  gap: 8px;
}

.parameter-inputs input[type="text"] {
  flex: 1;
  padding: 5px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.checkbox-wrapper input {
  margin-right: 5px;
}

.remove-param-btn {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  font-size: 1.1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.add-param-btn {
  background: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  margin-top: 8px;
  cursor: pointer;
}

.image-previews {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.image-preview-item {
  position: relative;
  width: 100px;
  height: 100px;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.image-preview-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-image-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(255, 255, 255, 0.7);
  border: none;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  font-size: 16px;
  line-height: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-upload {
  margin-top: 10px;
}

.upload-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 16px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.upload-btn:hover {
  background-color: #e0e0e0;
}
/* Styles généraux pour les formulaires Material */
.full-width {
  width: 100%;
  margin-bottom: 15px;
}

.mat-form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.parameter-row {
  display: grid;
  grid-template-columns: 2fr 2fr 1fr 2fr auto;
  gap: 10px;
  margin-bottom: 10px;
  align-items: center;
}

.component-form-container {
  background: white;
  padding: 24px;
  border-radius: 8px;
  max-width: 800px;
  width: 90%;
  max-height: 85vh;
  overflow-y: auto;
}

.component-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.parameters-section {
  padding: 0px;
  border-radius: 4px;
  margin-bottom: 0px;
}

.parameters-section table{
  margin-left: -6px !important;
  margin-top: -7px !important;
}

.parameters-section h4 {
  margin-top: 0;
  margin-bottom: 10px;
}

.add-btn {
  margin-top: 10px;
}

/* Styles pour la sidebar */
.sidebar {
  background: #f5f5f5;
  padding: 15px;
  border-right: 1px solid #e0e0e0;
}

.interface-types button {
  margin-right: 10px;
}

.interface-types button.active {
  background-color: #3f51b5;
  color: white;
}

/* Styles pour les éléments d'outil */
.tool-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.tool-item:hover {
  background-color: #f0f0f0;
}

.tool-preview {
  width: 30px;
  height: 30px;
  margin-right: 10px;
}

.tool-preview.component {
  background-color: #3498db;
  border-radius: 4px;
}

.tool-preview.subcomponent {
  background-color: #cc2ec4;
  border-radius: 4px;
}

.tool-preview.port {
  background-color: #f1c40f;
  border-radius: 4px;
}
/* Make sure delete buttons are red */
.form-buttons .mat-warn {
  background-color: #f44336 !important;
  color: white !important;
}

.btn-default {
  font-size: 12px;
  min-width: auto;
  padding: 0 8px;
}
/* Add these CSS classes to match version-add-edit styling */
.button-container {
  display: flex;
  justify-content: center;  /* Centre les boutons horizontalement */
  gap: 16px;  /* Espacement entre les boutons */
  width: 100%;  /* Largeur complète du conteneur */
  margin: 0 auto;  /* Centrage automatique */
}

.content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0 15px;
}


.custom-row {
  margin: 0 -15px;
}

.col-12 {
  width: 100%;
  padding: 0 15px;
}

.component-form-overlay,
.subcomponent-form-overlay,
.port-form-overlay,
.interface-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.component-form-container,
.subcomponent-form-container,
.port-form-overlay,
.interface-form-overlay {
  width: 1000px;
  max-width: 95vw;
  background: white;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  overflow: hidden;
}

/* Amélioration du style des tableaux de paramètres */
.responsive-table {
  margin-bottom: 16px;
}

.responsive-table table {
  min-width: 100%;
}

.responsive-table th {
  font-weight: 600;
  color: rgba(0, 0, 0, 0.7);
  padding: 12px 8px;
}

.responsive-table td {
  padding: 4px 8px;
}

/* Style pour les checkbox dans les tableaux */
.checkbox-cell {
  text-align: center;
  width: 60px;
}

/* Amélioration des boutons d'ajout de paramètres */
.add-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 6px 16px;
}

/* Style pour les titres des sections */
.parameters-section .mat-subtitle-2 {
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.8);
}

/* Uniformisation des boutons d'action */
.button-container button {
  min-width: 120px;
  padding: 8px 20px;
}
.zoom-controls {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 24px;
  padding: 4px 8px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
  z-index: 2000; /* Increased z-index to ensure it stays above other elements */
  user-select: none; /* Prevent text selection */
  transition: opacity 0.3s ease;
}

/* Add hover effect to make it more visible when needed */
.zoom-controls:hover {
  opacity: 1;
}

/* Style for the zoom percentage text */
.zoom-level {
  margin: 0 8px;
  font-size: 14px;
  font-weight: 500;
  min-width: 45px;
  text-align: center;
}

/* Styles for the buttons in the zoom controls */
.zoom-controls .mat-icon-button {
  width: 30px;
  height: 30px;
  line-height: 30px;
}