import {
  Component,
  OnInit,
  ViewChild,
  OnDestroy,
  Output,
  EventEmitter,
  Input,
} from '@angular/core';
import { FlowExecutionService } from 'app/services/flow-execution.service';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FlowExecution } from 'app/models/flow-execution';
import { MaterialModule } from 'app/material.module';
import { environment } from 'environments/environment';

@Component({
  selector: 'app-flow-execution',
  standalone: true,
  imports: [MaterialModule],
  templateUrl: './flow-execution.component.html',
  styleUrl: './flow-execution.component.scss',
})
export class FlowExecutionComponent implements OnInit, OnDestroy {
  displayedColumns: string[] = ['flow', 'date', 'result', 'report', 'action'];

  dataSource!: MatTableDataSource<FlowExecution>;
  private _flowexecutions?: FlowExecution[];
  apiUrl?: string = environment.apiUrl;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  @Output() public actionProcess: EventEmitter<any> = new EventEmitter<any>();

  get flowexecutions(): FlowExecution[] | undefined {
    return this._flowexecutions;
  }
  @Input() set flowexecutions(value: FlowExecution[] | undefined) {
    this._flowexecutions = value;
    //console.log(value);
    if (value) {
      this.getFlowExecutionList();
    }
  }
  previewFile: string | null = null;
  previewContent: string | null = null;
  unsupportedPreview: boolean = false;

  constructor(
    private _flowExecutionService: FlowExecutionService,
    public _snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    //this.getFlowExecutionList();
  }

  setPreview(row: any) {
    const fileUrl = `${environment.imagePrefixUrl}${row.report}`;

    if (this.isImage(fileUrl)) {
      this.previewFile = fileUrl; // For images
      this.previewContent = null;
      this.unsupportedPreview = false;
    } else if (this.isTextFile(fileUrl)) {
      fetch(fileUrl)
        .then((response) => response.text()) // Get text content
        .then((text) => {
          this.previewFile = null;
          this.previewContent = text;
          this.unsupportedPreview = false;
        })
        .catch((error) => console.error('Error loading preview:', error));
    } else {
      // Unsupported file type
      this.previewFile = null;
      this.previewContent = null;
      this.unsupportedPreview = true;
    }
  }

  isImage(url: string): boolean {
    return /\.(jpg|jpeg|png|gif|webp)$/i.test(url);
  }
  isTextFile(url: string): boolean {
    return /\.(txt|json|log|csv|md)$/i.test(url);
  }

  //get list of flow-executions
  getFlowExecutionList() {
    this.dataSource = new MatTableDataSource(this.flowexecutions);
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
    this.dataSource.filterPredicate = (data: FlowExecution, filter: string) => {
      const str =
        data.date +
        ' ' +
        data.result +
        ' ' +
        data.report +
        ' ' +
        data.result +
        ' ' +
        data.flow?.name;
      return str.toLowerCase().includes(filter);
    };
  }

  //filter table
  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  //delate flow-execution
  deleteFlowExecution(flow_execution: FlowExecution) {
    const backup = [...this.dataSource.data]; // Create a shallow copy of the data for backup
    this.dataSource.data = this.dataSource.data.filter(
      (m) => m.uuid !== flow_execution.uuid
    ); // Remove flow-execution from data source

    // Open a snackbar with an undo option
    const snackBarRef = this._snackBar.open(
      `FlowExecution '${flow_execution.date}' will be deleted`,
      'Undo',
      { duration: 3000 }
    );

    // Handle snackbar dismissal
    snackBarRef.afterDismissed().subscribe((res) => {
      if (!res.dismissedByAction) {
        // If user did not click 'Undo', proceed with deletion
        this._flowExecutionService
          .deleteFlowExecution(flow_execution.uuid as string)
          .subscribe({
            next: (response: any) => {
              this.actionProcess.emit({ action: 'refreshVersion' });
              this._snackBar.open('FlowExecution deleted!', 'Done', {
                duration: 3000,
              });
            },
            error: (error) => {
              //console.error('Error deleting flow-execution:', error);
              //Restore data on error
              //204 No content should not return message (Backend needs to correct that)
              this.dataSource.data = backup;
              this._snackBar.open(
                'Failed to delete flow-execution. Please try again.',
                'Close',
                { duration: 5000 }
              );
            },
          });
      } else {
        // If 'Undo' was clicked, restore the original data
        this.dataSource.data = backup;
      }
    });
  }

  ngOnDestroy(): void {
    this._snackBar.dismiss();
  }
}
