<div class="content">
    <div class="bottom-section">
        <h2>
            <span class="text-primary">Flow Executions</span>
        </h2>

        <div class="bottom-cards">
            <!-- Flexbox container for filter and new sut button -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <!-- Filter input on the left -->
                <div class="flex-grow-1">
                    <mat-form-field appearance="outline">
                        <input matInput (keyup)="applyFilter($event)" placeholder="Filter" appSetFocus #input>
                    </mat-form-field>
                </div>
            </div>

            <div class="row custom-row mt-3">
                <div class="col-12 col-sm-10">
                    <div class="mat-elevation-z1 responsive-table">
                        <table mat-table [dataSource]="dataSource" matSort matSortDisableClear>
                            <ng-container matColumnDef="flow">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header> Flow </th>
                                <td mat-cell *matCellDef="let row">{{row.flow.name}}</td>
                            </ng-container>
                            <ng-container matColumnDef="date">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header> Date </th>
                                <td mat-cell *matCellDef="let row"> {{row.date}} </td>
                            </ng-container>
                            <ng-container matColumnDef="result">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header> Result </th>
                                <td mat-cell *matCellDef="let row"> {{row.result}} </td>
                            </ng-container>
                            <ng-container matColumnDef="report">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header> Report
                                </th>
                                <td mat-cell *matCellDef="let row"> {{row.report}}
                                </td>
                            </ng-container>
                            <ng-container matColumnDef="action">
                                <th mat-header-cell *matHeaderCellDef style="width: 200px"> Actions </th>
                                <td mat-cell *matCellDef="let row">
                                    <button mat-icon-button color="accent" (click)="setPreview(row)"
                                        matTooltip="Preview" aria-label="Preview" matTooltipPosition="above">
                                        <mat-icon>visibility</mat-icon>
                                    </button>
                                    <a mat-icon-button color="primary"
                                        [href]="apiUrl + '/flowexecution/' + row.uuid + '/report/'"
                                        matTooltip="Download" aria-label="Download" matTooltipPosition="above">
                                        <mat-icon>download</mat-icon>
                                    </a>
                                    <button mat-icon-button color="warn" (click)="deleteFlowExecution(row)"
                                        matTooltip="Delete" aria-label="Delete" matTooltipPosition="above">
                                        <mat-icon>delete</mat-icon>
                                    </button>
                                </td>
                            </ng-container>
                            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                            <tr mat-row *matRowDef="let row; columns: displayedColumns;" (click)="setPreview(row)"></tr>
                            <!-- Row shown when there is no matching data. -->
                            <tr class="mat-row" *matNoDataRow>
                                <td class="mat-cell text-center" *ngIf="input.value" colspan="5">No data matching the
                                    filter
                                    "{{input.value}}"
                                </td>
                                <td class="mat-cell text-center" *ngIf="!input.value" colspan="5">No data
                                </td>
                            </tr>
                        </table>
                        <mat-paginator [pageSize]="25" [pageSizeOptions]="[3, 5, 10, 25, 100]"></mat-paginator>
                    </div>
                </div>
                <div class="col-12 col-sm-2">
                    <div *ngIf="previewFile || previewContent" class="preview-container">
                        <h3>File Preview</h3>
                        <ng-container *ngIf="previewFile && isImage(previewFile); else textPreview">
                            <img [src]="previewFile" alt="Preview" class="image-preview" />
                        </ng-container>
                        <ng-template #textPreview>
                            <pre class="preview-text"
                                style="overflow: auto; max-height:300px;">{{ previewContent }}</pre>
                        </ng-template>
                    </div>
                    <p *ngIf="!previewFile && !previewContent && unsupportedPreview" class="error-message">
                        This file type is not supported for preview.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>