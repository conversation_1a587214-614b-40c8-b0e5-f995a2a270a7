<div class="parameter-list-container">
    <!-- Conteneur des résultats qui s'affiche comme un panneau flottant uniquement lors de la recherche -->
    <div *ngIf="showResults" class="results-panel mat-elevation-z8">
        <div class="results-header">
            <h3>Parameter Search Results</h3>
            <button mat-icon-button (click)="closeResults()">
                <mat-icon>close</mat-icon>
            </button>
        </div>

        <!-- Afficher le spinner de chargement pendant la recherche -->
        <div *ngIf="loading" class="loading-overlay">
            <mat-spinner diameter="40"></mat-spinner>
        </div>

        <!-- Message si aucun résultat après recherche -->
        <div *ngIf="hasSearched && dataSource.data.length === 0 && !loading" class="no-results-message">
            <mat-icon>search_off</mat-icon>
            <p>No parameters found matching "{{searchControl.value}}"</p>
        </div>

        <!-- Message si aucune recherche n'a été effectuée -->
        <div *ngIf="!hasSearched && !loading" class="search-prompt">
            <mat-icon>search</mat-icon>
            <p>Start typing to search for parameters</p>
        </div>

        <!-- Afficher les résultats après une recherche avec résultats -->
        <div *ngIf="hasSearched && dataSource.data.length > 0" class="table-container">
            <table mat-table [dataSource]="dataSource" matSort matSortActive="name" matSortDirection="asc">
                <!-- Name Column -->
                <ng-container matColumnDef="name">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> Name </th>
                    <td mat-cell *matCellDef="let row"> {{row.name}} </td>
                </ng-container>

                <!-- Value Column -->
                <ng-container matColumnDef="value">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> Value </th>
                    <td mat-cell *matCellDef="let row">
                        <span *ngIf="!row.secret">{{row.value || 'N/A'}}</span>
                        <span *ngIf="row.secret" class="secret-value">*****</span>
                    </td>
                </ng-container>

                <!-- Type Column -->
                <ng-container matColumnDef="type">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> Type </th>
                    <td mat-cell *matCellDef="let row"> {{getParameterType(row)}} </td>
                </ng-container>

                <!-- Element Type Column -->
                <ng-container matColumnDef="elementType">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> Element Type </th>
                    <td mat-cell *matCellDef="let row"> {{getElementType(row)}} </td>
                </ng-container>

                <!-- Element Name Column -->
                <ng-container matColumnDef="elementName">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> Element Name </th>
                    <td mat-cell *matCellDef="let row"> {{getElementName(row)}} </td>
                </ng-container>

                <!-- Parent Info Column -->
                <ng-container matColumnDef="parentInfo">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> Parent/Connection </th>
                    <td mat-cell *matCellDef="let row" [matTooltip]="row.parent_info || 'N/A'">
                        <div class="parent-info">{{row.parent_info || 'N/A'}}</div>
                    </td>
                </ng-container>

                <!-- Secret Column -->
                <ng-container matColumnDef="secret">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> Secret </th>
                    <td mat-cell *matCellDef="let row">
                        <div class="chip-container">
                            <span [ngClass]="row.secret ? 'warn-chip' : 'primary-chip'">
                                {{row.secret ? 'Yes' : 'No'}}
                            </span>
                        </div>
                    </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>

            <mat-paginator [pageSizeOptions]="pageSizeOptions" [pageSize]="pageSize" showFirstLastButtons
                aria-label="Select page of parameters">
            </mat-paginator>
        </div>
    </div>
</div>