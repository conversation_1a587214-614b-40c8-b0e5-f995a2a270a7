
.parameter-list-container {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 10;
    pointer-events: none;
  }
  
  .results-panel {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 90%;
    max-width: 900px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 10;
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 300px);
    overflow: hidden;
    pointer-events: auto;
  }
  
  .results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    background-color: #f5f5f5;
    
    h3 {
      margin: 0;
    }
  }
  
  .table-container {
    overflow: auto;
    max-height: calc(100vh - 380px);
    padding: 0 16px 16px 16px;
  }
  
  table {
    width: 100%;
  }
  
  .mat-column-name {
    min-width: 120px;
  }
  
  .mat-column-value {
    min-width: 120px;
  }
  
  .mat-column-type {
    min-width: 100px;
  }
  
  .mat-column-elementType {
    min-width: 110px;
  }
  
  .mat-column-elementName {
    min-width: 150px;
  }
  
  .mat-column-parentInfo {
    min-width: 180px;
    max-width: 250px;
  }
  
  .parent-info {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 230px;
  }
  
  .mat-column-secret {
    width: 80px;
    text-align: center;
  }
  
  .secret-value {
    font-style: italic;
    color: rgba(0, 0, 0, 0.54);
  }
  
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
  }
  
  .no-results-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px;
    color: rgba(0, 0, 0, 0.54);
    height: 200px;
    text-align: center;
    
    mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      margin-bottom: 16px;
    }
    
    p {
      margin: 0;
    }
  }
  
  .chip-container {
    display: flex;
    justify-content: center;
    
    span {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
    }
  }
  
  .warn-chip {
    background-color:  #3f51b5;
    color: white;
  }
  
  .primary-chip {
    background-color: #f44336;
    color: white;
  }
  // Ajouter le style pour search-prompt
.search-prompt {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px;
    color: rgba(0, 0, 0, 0.54);
    height: 200px;
    text-align: center;
    
    mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      margin-bottom: 16px;
    }
    
    p {
      margin: 0;
    }
  }