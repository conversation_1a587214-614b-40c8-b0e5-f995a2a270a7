import { Component, Input, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { DiagramService } from '../../services/diagram.service';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-parameter-list',
  templateUrl: './parameter-list.component.html',
  styleUrls: ['./parameter-list.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatButtonModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatTooltipModule
  ]
})
export class ParameterListComponent implements OnInit, AfterViewInit {
  @Input() version: any;

  dataSource = new MatTableDataSource<any>([]);
  displayedColumns: string[] = ['name', 'value', 'type', 'elementType', 'elementName', 'parentInfo', 'secret'];
  searchControl = new FormControl('');
  loading = false;
  hasSearched = false;
  showResults = false;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageSize = 10;

  constructor(private diagramService: DiagramService) {}

  ngOnInit(): void {
    // Custom sort pour ignorer la casse
    this.dataSource.sortingDataAccessor = (item, property) => {
      switch(property) {
        case 'name':
        case 'value': 
          return item[property]?.toLowerCase() || '';
        case 'type': 
          return item.parameter_type_detail?.name?.toLowerCase() || '';
        case 'elementType': 
          return item.element_type?.toLowerCase() || '';
        case 'elementName': 
          return item.element_detail?.name?.toLowerCase() || '';
        case 'parentInfo': 
          return (item.parent_info || 'N/A').toLowerCase();
        default: 
          return item[property];
      }
    };
  }

  // Modifier la méthode ngAfterViewInit
  ngAfterViewInit() {
    // On ne fait rien ici car les références doivent être appliquées après chaque mise à jour des données
  }

  // Méthode pour afficher le panneau de résultats sans faire de recherche
  showResultsPanel(): void {
    this.showResults = true;
    this.hasSearched = false;
    this.dataSource.data = [];
  }

  // Méthode pour réinitialiser la recherche
  resetSearch(): void {
    this.showResults = false;
    this.hasSearched = false;
    this.dataSource.data = [];
  }

  // Méthode pour effectuer une recherche
  performSearch(searchTerm: string): void {
    if (searchTerm && searchTerm.trim() !== '') {
      this.loading = true;
      this.hasSearched = true;
      this.showResults = true;
      this.searchParameters(searchTerm);
    } else {
      // Si le terme de recherche est vide, juste montrer le panneau
      this.showResultsPanel();
    }
  }

  // Modifier la méthode searchParameters
  searchParameters(searchTerm: string): void {
    console.log('Searching for parameters:', searchTerm);
    
    if (!this.version || !this.version.uuid) {
      console.error('No version specified for parameter search');
      this.loading = false;
      return;
    }
    
    // Utiliser la nouvelle méthode pour filtrer par version
    this.diagramService.getCompleteParametersByVersion(this.version.uuid).subscribe({
      next: (parameters) => {
        console.log('Complete parameters received for version:', parameters);
        
        if (!parameters || parameters.length === 0) {
          console.log('No parameters found in response');
          this.loading = false;
          this.dataSource.data = [];
          return;
        }
        
        const searchTermLower = searchTerm.toLowerCase();
        
        // Filtrer en utilisant les nouvelles propriétés
        const filteredData = parameters.filter(param => 
          (param.name && param.name.toLowerCase().includes(searchTermLower)) ||
          (param.value && param.value.toLowerCase().includes(searchTermLower)) ||
          (param.parameter_type_detail?.name && param.parameter_type_detail.name.toLowerCase().includes(searchTermLower)) ||
          (param.element_type && param.element_type.toLowerCase().includes(searchTermLower)) ||
          (param.element_detail?.name && param.element_detail.name.toLowerCase().includes(searchTermLower)) ||
          (param.parent_info && param.parent_info.toLowerCase().includes(searchTermLower))
        );
        
        console.log('Filtered parameters:', filteredData);
        this.dataSource.data = filteredData;
        
        // Configuration du tri personnalisé
        this.dataSource.sortingDataAccessor = (item: any, property) => {
          switch(property) {
            case 'type': return item.parameter_type_detail?.name || '';
            case 'elementType': return item.element_type || '';
            case 'elementName': return item.element_detail?.name || '';
            case 'parentInfo': return this.getParentInfo(item);
            default: return item[property];
          }
        };
        
        // Appliquer la pagination et le tri après avoir mis à jour les données
        this.applyPaginatorAndSort();
        
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading parameters:', error);
        this.loading = false;
      }
    });
  }

  // Modifier la méthode showAllParametersForVersion
  showAllParametersForVersion(versionId: string): void {
    this.loading = true;
    this.hasSearched = true;
    this.showResults = true;
    
    this.diagramService.getCompleteParametersByVersion(versionId).subscribe({
      next: (parameters) => {
        console.log('All parameters for version:', parameters);
        
        if (!parameters || parameters.length === 0) {
          console.log('No parameters found for this version');
          this.dataSource.data = [];
        } else {
          this.dataSource.data = parameters;
          
          // Configuration du tri personnalisé
          this.dataSource.sortingDataAccessor = (item: any, property) => {
            switch(property) {
              case 'type': return item.parameter_type_detail?.name || '';
              case 'elementType': return item.element_type || '';
              case 'elementName': return item.element_detail?.name || '';
              case 'parentInfo': return this.getParentInfo(item);
              default: return item[property];
            }
          };
          
          // Appliquer la pagination et le tri après avoir mis à jour les données
          this.applyPaginatorAndSort();
        }
        
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading parameters:', error);
        this.loading = false;
      }
    });
  }

  // Ajouter cette méthode pour appliquer le paginator et le sort
  private applyPaginatorAndSort(): void {
    if (this.paginator) {
      // Réinitialiser à la première page à chaque nouvelle recherche
      this.paginator.firstPage();
      this.dataSource.paginator = this.paginator;
    }
    
    if (this.sort) {
      this.dataSource.sort = this.sort;
    }
  }

  getElementType(parameter: any): string {
    return parameter.element_type ? parameter.element_type.charAt(0).toUpperCase() + parameter.element_type.slice(1) : 'N/A';
  }

  getElementName(parameter: any): string {
    return parameter.element_detail?.name || 'N/A';
  }

  getParameterType(parameter: any): string {
    return parameter.parameter_type_detail?.name || 'N/A';
  }
  
  getParentInfo(parameter: any): string {
    return parameter.parent_info || 'N/A';
  }
  
  getConnectionInfo(parameter: any): string {
    if (parameter.connection_details) {
      let info = '';
      if (parameter.connection_details.port_from) {
        info += `From: ${parameter.connection_details.port_from.name}`;
      }
      if (parameter.connection_details.port_to) {
        info += info ? ', To: ' : 'To: ';
        info += parameter.connection_details.port_to.name;
      }
      if (parameter.connection_details.subcomponent_to) {
        info += info ? ', To SubComp: ' : 'To SubComp: ';
        info += parameter.connection_details.subcomponent_to.name;
      }
      return info;
    }
    return '';
  }
  
  closeResults(): void {
    this.showResults = false;
  }
}