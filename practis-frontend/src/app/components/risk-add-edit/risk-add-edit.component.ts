import { Component, Inject, OnInit } from '@angular/core';
import { MaterialModule } from 'app/material.module';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Risk } from 'app/models/risk';
import { RiskService } from 'app/services/risk.service';
import { RiskStatus } from 'app/models/enum-types';
import { RiskValue } from 'app/models/enum-types';

@Component({
  selector: 'app-risk-add-edit',
  standalone: true,
  imports: [MaterialModule],
  templateUrl: './risk-add-edit.component.html',
  styleUrl: './risk-add-edit.component.scss',
})
export class RiskAddEditComponent implements OnInit {
  form: FormGroup;
  data?: Risk;
  risk_analysis?: string;
  riskStatuses = Object.values(RiskStatus);
  riskValues = Object.values(RiskValue);

  constructor(
    private _fb: FormBuilder,
    private _dialogRef: MatDialogRef<RiskAddEditComponent>,
    private _riskService: RiskService,
    @Inject(MAT_DIALOG_DATA) public all_data: any,
    public _snackBar: MatSnackBar
  ) {
    this.form = this._fb.group({
      name: ['', Validators.required],
      description: [''],
      value: [RiskValue.Low, Validators.required],
      status: [RiskStatus.New, Validators.required],
      level: ['', Validators.required],
    });
  }

  ngOnInit(): void {
    this.data = this.all_data.risk;
    this.risk_analysis = this.all_data.risk_analysis;
    //console.log(this.all_data);

    if (this.data) {
      // If editing, initialize the form with asset data
      this.initializeForm(this.data);
    }
  }

  // Populate form fields for editing an existing asset
  private initializeForm(data: Risk): void {
    this.form.patchValue({
      name: data.name,
      description: data.description?.replace(/\\n/g, '\n'),
      status: data.status || RiskStatus.New,
      value: data.value || RiskValue.Low,
      level: data.level,
    });

    // Manually update form validity after patching values to ensure button reflects the correct state
    this.markFormGroupTouchedAndDirty(this.form);
  }

  // Function to mark all controls as touched and dirty
  private markFormGroupTouchedAndDirty(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      if (control instanceof FormArray) {
        // Recursively apply to each FormGroup in the FormArray
        (control as FormArray).controls.forEach((arrayControl) => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouchedAndDirty(arrayControl);
          }
        });
      } else if (control instanceof FormGroup) {
        this.markFormGroupTouchedAndDirty(control);
      } else {
        control.markAsTouched();
        control.markAsDirty();
      }
    });
  }

  onFormSubmit() {
    if (this.form.valid) {
      // Create FormData to handle image uploads
      const formData = new FormData();
      if (this.risk_analysis) {
        formData.append('risk_analysis', this.risk_analysis);
      }

      Object.keys(this.form.value).forEach((key) => {
        const value = this.form.value[key];

        // Check if value is an object (not null) and stringify only if necessary
        if (typeof value === 'object' && value !== null) {
          formData.append(key, JSON.stringify(value));
        } else {
          formData.append(key, value ?? ''); // Append empty string if value is null/undefined
        }
      });

      //console.log(formData);

      //Uncomment later when backend work correctly
      if (this.data && this.data.uuid) {
        this._riskService.updateRisk(this.data.uuid, formData).subscribe({
          next: (val: any) => {
            //console.log(val);
            this._snackBar.open('Risk detail updated!', 'Done', {
              duration: 3000,
            });
            this._dialogRef.close(val);
          },
          error: (err: any) => {
            console.error(err);
          },
        });
      } else {
        this._riskService.addRisk(formData).subscribe({
          next: (val: any) => {
            this._snackBar.open('Risk added successfully', 'Done', {
              duration: 3000,
            });
            this._dialogRef.close(val);
          },
          error: (err: any) => {
            console.error(err);
          },
        });
      }
    }
  }
}
