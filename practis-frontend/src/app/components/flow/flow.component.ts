import {
  Component,
  OnInit,
  ViewChild,
  OnDestroy,
  Version,
  Output,
  Input,
  EventEmitter,
} from '@angular/core';
import { FlowService } from 'app/services/flow.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { Flow } from 'app/models/flow';
import { MaterialModule } from 'app/material.module';
import { FlowAddEditComponent } from '../flow-add-edit/flow-add-edit.component';
import { ParameterTypeService } from 'app/services/parameter-type.service';
import { ParameterType } from 'app/models/parameter-type';
import { environment } from 'environments/environment';
import { Sut } from 'app/models/sut';
import { ComponentModel } from 'app/models/component';
import { SubcomponentModel } from 'app/models/subcomponent';
import { PortModel } from 'app/models/port';
import { MatSelectChange } from '@angular/material/select';
import { UtilsService } from 'app/utils/utils';
import { SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'app-flow',
  standalone: true,
  imports: [MaterialModule],
  templateUrl: './flow.component.html',
  styleUrl: './flow.component.scss',
})
export class FlowComponent implements OnInit, OnDestroy {
  flows?: Flow[];
  initialFlows?: Flow[];
  parameterTypes?: ParameterType[];
  selectedParameterTypes?: string[];
  noderedUrl: string = environment.noderedUrl;
  genericParameterType?: string;
  sut?: Sut;
  version?: Version;
  component?: ComponentModel;
  subcomponent?: SubcomponentModel;
  port?: PortModel;
  private _dataInput?: any;
  safeFlowDescriptions: { [key: string]: SafeHtml } = {};

  @Output() public actionProcess: EventEmitter<any> = new EventEmitter<any>();

  get dataInput(): any | undefined {
    return this._dataInput;
  }
  @Input() set dataInput(value: any | undefined) {
    this._dataInput = value;
    if (value) {
      this.sut = value.sut ? value.sut : null;
      this.version = value.version ? value.version : null;
      this.component = value.component ? value.component : null;
      this.subcomponent = value.subcomponent ? value.subcomponent : null;
      this.port = value.port ? value.port : null;
      this.refresh();
    }
  }

  constructor(
    private _dialog: MatDialog,
    private _flowService: FlowService,
    private _parameterTypeService: ParameterTypeService,
    private _snackBar: MatSnackBar,
    private _utilsService: UtilsService
  ) {}

  ngOnInit(): void {}

  refresh() {
    this._parameterTypeService.getParameterTypeList().subscribe({
      next: (res) => {
        this.parameterTypes = res;
        
        // Utiliser une approche plus sûre pour obtenir genericParameterType
        const genericParams = this.parameterTypes.filter(param => param.generic);
        this.genericParameterType = genericParams.length > 0 ? genericParams[0].id : undefined;
        
        this.getFlowList();
      },
      error: console.log,
    });
  }

  //get list of flows
  getFlowList() {
    this._flowService.getFlowList().subscribe({
      next: (res) => {
        this.flows = res.map((flow) => new Flow(flow));
        this.initialFlows = [...this.flows];
        if (this.dataInput?.selectedParameterTypes) {
          this.selectedParameterTypes = this.dataInput.selectedParameterTypes
            ? this.dataInput.selectedParameterTypes
            : [];
        } else {
          this.selectedParameterTypes = this.genericParameterType
            ? [this.genericParameterType]
            : [];
        }
        if (this.genericParameterType) {
          this.filterFlows(this.selectedParameterTypes || []);
        }
        this.flows.forEach((flow) => {
          if (flow.uuid) {
            this.safeFlowDescriptions[flow.uuid] =
              this._utilsService.getSafeDescription(flow.description);
          }
        });
      },
      error: console.log,
    });
  }

  //filter table
  applyFilter(event: MatSelectChange) {
    const filterValue = event.value;
    this.filterFlows(filterValue ? filterValue : []);
  }

  filterFlows(values: string[]) {
    if (values?.length) {
      this.flows = this.initialFlows
        ?.map((flow) => {
          const intersections = this.getArrayIntersection(
            flow.parameter_type_ids,
            values
          );
          return new Flow({ ...flow, nb_conditions: intersections.length });
        })
        .filter((flow) => (flow.nb_conditions ?? 0) > 0)
        .sort((a, b) => (b.nb_conditions ?? 0) - (a.nb_conditions ?? 0));
    } else {
      this.flows = [...(this.initialFlows || [])];
    }
  }

  getArrayIntersection(arr1: string[], arr2: string[]): string[] {
    return arr1.filter((value) => arr2.includes(value));
  }

  //delate flow
  deleteFlow(flow: Flow) {
    const backup = this.flows ? [...this.flows] : []; // Create a shallow copy of the data for backup
    if (this.flows) {
      this.flows = this.flows.filter((m) => m.uuid !== flow.uuid); // Remove flow from data source
    }

    // Open a snackbar with an undo option
    const snackBarRef = this._snackBar.open(
      `Flow '${flow.name}' will be deleted`,
      'Undo',
      { duration: 3000 }
    );

    // Handle snackbar dismissal
    snackBarRef.afterDismissed().subscribe((res) => {
      if (!res.dismissedByAction) {
        // If user did not click 'Undo', proceed with deletion
        this._flowService.deleteFlow(flow.uuid as string).subscribe({
          next: (response: any) => {
            this.actionProcess.emit({ action: 'refresh' });
            this._snackBar.open('Flow deleted!', 'Done', { duration: 3000 });
          },
          error: (error) => {
            //console.error('Error deleting flow:', error);
            //Restore data on error
            //204 No content should not return message (Backend needs to correct that)
            this.flows = backup;
            this._snackBar.open(
              'Failed to delete flow. Please try again.',
              'Close',
              { duration: 5000 }
            );
          },
        });
      } else {
        // If 'Undo' was clicked, restore the original data
        this.flows = backup;
      }
    });
  }

  //add form
  openAddFlowForm() {
    const dialogRef = this._dialog.open(FlowAddEditComponent);
    dialogRef.afterClosed().subscribe({
      next: (val) => {
        if (val) {
          this.actionProcess.emit({ action: 'refresh' });
        }
      },
    });
  }

  //edit form
  openEditFlowForm(flow: Flow) {
    const dialogRef = this._dialog.open(FlowAddEditComponent, {
      data: flow,
    });
    dialogRef.afterClosed().subscribe({
      next: (val) => {
        if (val) {
          this.actionProcess.emit({ action: 'refresh' });
        }
      },
    });
  }

  launchFlow(flow: Flow) {
    const data = {
      flow: flow,
      sut: this.sut,
      version: this.version,
      component: this.component,
      subcomponent: this.subcomponent,
      port: this.port,
    };

    if (flow.api_url) {
      //console.log(data);
      this._flowService.launchFlow(data, flow.api_url).subscribe({
        next: (val: any) => {
          this._snackBar.open('Data submitted to flow successfully', 'Done', {
            duration: 3000,
          });
        },
        error: (err: any) => {
          //console.error(err);
        },
      });
    } else {
      this._snackBar.open('Flow API URL is missing', 'Close', {
        duration: 3000,
      });
    }
  }

  ngOnDestroy(): void {
    this._snackBar.dismiss();
  }
}
