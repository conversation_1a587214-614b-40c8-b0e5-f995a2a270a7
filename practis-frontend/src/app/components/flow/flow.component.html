<div class="content">
    <div class="bottom-section">
        <h2>
            <span class="text-primary">Flows</span>
            <div>
                <a mat-raised-button color="accent" [href]="noderedUrl" target="_blank">Open
                    Nodered</a>&nbsp;&nbsp;&nbsp;
                <button mat-raised-button color="primary" class="new-bottom-button" (click)="openAddFlowForm()">
                    New Flow
                </button>
            </div>
        </h2>

        <div class="bottom-cards">
            <div class="row custom-row">
                <div class="col-12 col-sm-12">
                    <mat-form-field appearance="outline" color="primary">
                        <mat-select [(ngModel)]="selectedParameterTypes" (selectionChange)="applyFilter($event)"
                            multiple placeholder="Search by parameter...">
                            @for (parameterType of parameterTypes; track parameterType) {
                            <mat-option [value]="parameterType.id">{{ parameterType.name }}</mat-option>
                            }
                        </mat-select>
                    </mat-form-field>
                </div>
            </div>
            <ng-container *ngIf="!flows?.length">
                <mat-card class="bottom-card cardWithShadow">
                    <mat-card-content>
                        <div class="no-top-section">
                            <h3>No Flows found</h3>
                        </div>
                    </mat-card-content>
                </mat-card>
            </ng-container>
            <ng-container *ngIf="flows?.length ?? 0 > 0">
                <mat-card *ngFor="let flow of flows" class="bottom-card cardWithShadow">
                    <div class="title-buttons">
                        <h4>{{flow.name}}</h4>
                        <div>
                            <button mat-icon-button (click)="openEditFlowForm(flow)">
                                <mat-icon color="primary">edit</mat-icon>
                            </button>
                            <button mat-icon-button (click)="deleteFlow(flow)">
                                <mat-icon color="warn">delete</mat-icon>
                            </button>
                        </div>
                    </div>
                    <mat-card-content>
                        <div class="content-all">
                            <div class="content-left">
                                <div *ngIf="flow.uuid" [innerHTML]="safeFlowDescriptions[flow.uuid]">
                                </div>
                                <br />
                                <div class="row custom-row">
                                    <div class="col-12 col-sm-4">
                                        <b>Parameters: </b>{{flow.parameter_type_names}}
                                        <!-- <mat-chip-set aria-label="Parameters"
                                            *ngFor="let param of flow.parameter_type_names" color="primary">
                                            <mat-chip color="primary" class="primary">{{param}}</mat-chip>
                                        </mat-chip-set> -->
                                    </div>
                                    <div class="col-12 col-sm-4">
                                        <b>Address:</b> {{flow.address}}
                                    </div>
                                    <div class="col-12 col-sm-4">
                                        <b>Api Url:</b> {{flow.api_url}}
                                    </div>
                                </div>
                            </div>
                            <div class="content-right right">
                                <button mat-raised-button color="primary" class="new-bottom-button"
                                    (click)="launchFlow(flow)">
                                    Launch Flow
                                </button>
                            </div>
                        </div>
                    </mat-card-content>
                </mat-card>
            </ng-container>
        </div>
    </div>
</div>