<div mat-dialog-title>
  <h4>
    Update {{
    data.elementType === "component" ? "Component" :
    data.elementType === "subcomponent" ? "Subcomponent" :
    data.elementType === "port" ? "Port" :
    ""
    }} Notes
  </h4>
</div>

<!-- <div *ngIf="isLoading" class="loading-spinner">
  <mat-spinner diameter="40"></mat-spinner>
</div> -->

<form [formGroup]="form" class="form" novalidate>
  <div mat-dialog-content class="content">
    <div class="row custom-row">
      <div class="col-12 col-sm-12">
        <!-- Notes Field -->
        <div class="notes-header">
          <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Notes</mat-label>
        </div>

        <!-- Si vous avez Quill Editor installé, utilisez ceci: -->
        <!-- <quill-editor formControlName="notes" [styles]="{height: '200px'}"></quill-editor> -->

        <!-- Sinon, utilisez le textarea standard: -->
        <mat-form-field appearance="outline" class="w-100" color="primary">
          <textarea matInput formControlName="notes" rows="8"></textarea>
        </mat-form-field>
      </div>
    </div>
  </div>

  <div mat-dialog-actions class="action">
    <div class="button-container">
      <button mat-raised-button color="primary" type="submit" [disabled]="form.pristine || form.invalid || isLoading"
        (click)="onFormSubmit()">
        Update
      </button>
      <button mat-raised-button type="button" [mat-dialog-close]="false">
        Cancel
      </button><br />
    </div><br /><br />
  </div>
</form>