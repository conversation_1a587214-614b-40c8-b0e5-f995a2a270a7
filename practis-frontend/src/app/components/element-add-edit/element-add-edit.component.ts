
import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DiagramService } from '../../services/diagram.service';
import { CommonModule } from '@angular/common';
import { MaterialModule } from '../../material.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-element-add-edit',
  standalone: true,
  imports: [CommonModule, MaterialModule, FormsModule, ReactiveFormsModule],
  templateUrl: './element-add-edit.component.html',
  styleUrls: ['./element-add-edit.component.scss']
})
export class ElementAddEditComponent implements OnInit {
  form: FormGroup;
  isLoading = false;

  constructor(
    private _fb: FormBuilder,
    private _diagramService: DiagramService,
    private _dialogRef: MatDialogRef<ElementAddEditComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private _snackBar: MatSnackBar
  ) {
    this.form = this._fb.group({
      notes: ['']
    });
  }

  ngOnInit(): void {
    // Set the form value from the input data
    if (this.data && this.data.element) {
      console.log('Element being edited:', this.data.element);
      this.form.patchValue({
        notes: this.data.element.notes || '',
        name: this.data.element.name || ''  // Include the name field for the form
      });
    }
  }
  
  onFormSubmit() {
    if (this.form.valid) {
      this.isLoading = true;
      
      // Create FormData instead of JSON
      const formData = new FormData();
      
      // Always include notes (the field we're updating)
      formData.append('notes', this.form.value.notes || '');
      formData.append('onlyNotes', '1'); // Indicate that we're only updating notes
      
      // Handle component relationship for subcomponents and ports
      const elementType = this.data.elementType || 'component';
      if (elementType === 'port' || elementType === 'subcomponent') {
        let componentId = null;
        
        if (this.data.element.component) {
          if (typeof this.data.element.component === 'object' && this.data.element.component.id) {
            componentId = this.data.element.component.id;
          } else if (typeof this.data.element.component === 'string') {
            componentId = this.data.element.component;
          }
        } else if (this.data.element.component_id) {
          componentId = this.data.element.component_id;
        }
        
        if (componentId) {
          formData.append('component', componentId);
        }
      }
      
      
   
      const elementId = this.data.element.id || this.data.element.id || this.data.elementId;
      
      if (!elementId) {
        console.error('Missing element ID:', this.data.element);
        this._snackBar.open('Cannot update notes: Missing element ID', 'Close', { duration: 3000 });
        this.isLoading = false;
        return;
      }
           
      // Select the appropriate update method based on element type
      switch(elementType) {
        case 'component':
          this._diagramService.updateComponent(elementId, formData).subscribe({
            next: (_: any) => {
              this._snackBar.open('Notes updated successfully', 'Close', { duration: 3000 });
              this._dialogRef.close(true);
              this.isLoading = false;
            },
            error: (err: any) => {
              console.error('Error updating notes:', err);
              this._snackBar.open('Error updating notes: ' + (err.error?.message || err.message || 'Unknown error'), 'Close', { duration: 3000 });
              this.isLoading = false;
            }
          });
          break;
          
        case 'subcomponent':
          this._diagramService.updateSubComponent(elementId, formData).subscribe({
            next: (_: any) => {
              this._snackBar.open('Notes updated successfully', 'Close', { duration: 3000 });
              this._dialogRef.close(true);
              this.isLoading = false;
            },
            error: (err: any) => {
              console.error('Error updating notes:', err);
              this._snackBar.open('Error updating notes: ' + (err.error?.message || err.message || 'Unknown error'), 'Close', { duration: 3000 });
              this.isLoading = false;
            }
          });
          break;
          
        case 'port':
          this._diagramService.updatePort(elementId, formData).subscribe({
            next: (_: any) => {
              this._snackBar.open('Notes updated successfully', 'Close', { duration: 3000 });
              this._dialogRef.close(true);
              this.isLoading = false;
            },
            error: (err: any) => {
              console.error('Error updating notes:', err);
              this._snackBar.open('Error updating notes: ' + (err.error?.message || err.message || 'Unknown error'), 'Close', { duration: 3000 });
              this.isLoading = false;
            }
          });
          break;
          
        default:
          this._snackBar.open('Unknown element type: ' + elementType, 'Close', { duration: 3000 });
          this.isLoading = false;
      }
    }
  }
}
