import { Component, Inject, OnInit } from '@angular/core';
import { MaterialModule } from 'app/material.module';
import { <PERSON><PERSON><PERSON><PERSON>, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FlowService } from 'app/services/flow.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Flow } from 'app/models/flow';
import { ParameterType } from 'app/models/parameter-type';
import { ParameterTypeService } from 'app/services/parameter-type.service';
import { environment } from 'environments/environment';

@Component({
  selector: 'app-flow-add-edit',
  standalone: true,
  imports: [MaterialModule],
  templateUrl: './flow-add-edit.component.html',
  styleUrl: './flow-add-edit.component.scss',
})
export class FlowAddEditComponent implements OnInit {
  form: FormGroup;
  parameterTypes?: ParameterType[];

  constructor(
    private _fb: FormBuilder,
    private _flowService: FlowService,
    private _parameterTypeService: ParameterTypeService,
    private _dialogRef: MatDialogRef<FlowAddEditComponent>,
    @Inject(MAT_DIALOG_DATA) public data: Flow,
    public _snackBar: MatSnackBar
  ) {
    this.form = this._fb.group({
      name: ['', Validators.required],
      description: [''],
      address: ['', Validators.required],
      api_url: ['', Validators.required],
      parameter_type: [null],
    });
  }

  ngOnInit(): void {
    this._parameterTypeService.getParameterTypeList().subscribe({
      next: (res) => {
        this.parameterTypes = res;
      },
      error: console.log,
    });
    if (this.data) {
      // If editing, initialize the form with flow data
      this.initializeForm(this.data);
    }
  }

  // Populate form fields for editing an existing flow
  private initializeForm(data: Flow): void {
    data = new Flow(data);
    this.form.patchValue({
      name: data.name,
      description: data.description,
      address: data.address,
      api_url: data.api_url,
      parameter_type: data.parameter_type_ids,
    });

    // Manually update form validity after patching values to ensure button reflects the correct state
    this.markFormGroupTouchedAndDirty(this.form);
  }

  // Function to mark all controls as touched and dirty
  private markFormGroupTouchedAndDirty(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      if (control instanceof FormArray) {
        // Recursively apply to each FormGroup in the FormArray
        (control as FormArray).controls.forEach((arrayControl) => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouchedAndDirty(arrayControl);
          }
        });
      } else if (control instanceof FormGroup) {
        this.markFormGroupTouchedAndDirty(control);
      } else {
        control.markAsTouched();
        control.markAsDirty();
      }
    });
  }

  onFormSubmit() {
    if (this.form.valid) {
      const formData = this.form.value;

      //console.log('Form submitted:', formData);
      if (this.data) {
        this._flowService
          .updateFlow(this.data.uuid as string, formData)
          .subscribe({
            next: (val: any) => {
              this._snackBar.open('Flow detail updated!', 'Done', {
                duration: 3000,
              });
              this._dialogRef.close(true);
            },
            error: (err: any) => {
              console.error(err);
            },
          });
      } else {
        this._flowService.addFlow(formData).subscribe({
          next: (val: any) => {
            this._snackBar.open('Flow added successfully', 'Done', {
              duration: 3000,
            });
            this._dialogRef.close(true);
          },
          error: (err: any) => {
            console.error(err);
          },
        });
      }
    } else {
      console.error('Form is invalid');
    }
  }
}
