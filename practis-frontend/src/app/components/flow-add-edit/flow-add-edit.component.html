<div mat-dialog-title>
    <h4>{{data ? 'Update': 'Add'}} Flow</h4>
</div>
<form [formGroup]="form" class="form" novalidate>
    <div mat-dialog-content class="content">
        <div class="row custom-row">
            <div class="col-12 col-sm-12">
                <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Name *</mat-label>
                <mat-form-field appearance="outline" class="w-100" color="primary">
                    <input matInput formControlName="name" required />
                    <mat-error *ngIf="form.get('name')?.hasError('required')" class="no-pad">
                        Name is required
                    </mat-error>
                </mat-form-field>

                <!-- Description Field -->
                <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Description</mat-label>
                <mat-form-field appearance="outline" class="w-100" color="primary">
                    <textarea matInput formControlName="description"></textarea>
                </mat-form-field>

                <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Address *</mat-label>
                <mat-form-field appearance="outline" class="w-100" color="primary">
                    <input matInput formControlName="address" required />
                    <mat-error *ngIf="form.get('address')?.hasError('required')" class="no-pad">
                        Address is required
                    </mat-error>
                </mat-form-field>

                <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">API URL *</mat-label>
                <mat-form-field appearance="outline" class="w-100" color="primary">
                    <input matInput formControlName="api_url" required />
                    <mat-error *ngIf="form.get('api_url')?.hasError('required')" class="no-pad">
                        API URL is required
                    </mat-error>
                </mat-form-field>

                <mat-label class="mat-subtitle-2 f-s-14 f-w-600 m-b-12 d-block">Parameters</mat-label>
                <mat-form-field appearance="outline" class="w-100" color="primary">
                    <mat-select formControlName="parameter_type" multiple>
                        @for (parameterType of parameterTypes; track parameterType) {
                        <mat-option [value]="parameterType.id">{{ parameterType.name }}</mat-option>
                        }
                    </mat-select>
                    <mat-error *ngIf="form.get('parameter_type')?.hasError('required')" class="no-pad">
                        Parameters required
                    </mat-error>
                </mat-form-field>

            </div>
        </div>
    </div>
    <div mat-dialog-actions class="action">
        <div>
            <button mat-raised-button color="primary" type="submit"
                [disabled]="form.pristine || form.invalid || form.pending" (click)="onFormSubmit()">{{data ? 'Update':
                'Save'}}</button>
            <button mat-raised-button type="button" [mat-dialog-close]="false">Cancel</button><br />
        </div><br /><br />
    </div>
</form>