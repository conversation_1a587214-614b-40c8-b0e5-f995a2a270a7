import {
  Component,
  EventEmitter,
  Inject,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { MaterialModule } from 'app/material.module';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Version } from 'app/models/version';
import { ImageFile } from 'app/models/image-file';
import { VersionStatus } from 'app/models/enum-types';
import { ReportService } from 'app/services/report.service';

@Component({
  selector: 'app-report',
  standalone: true,
  imports: [MaterialModule],
  templateUrl: './report.component.html',
  styleUrl: './report.component.scss',
})
export class ReportComponent implements OnInit {
  formRules: FormGroup;
  isLoading = false;
  disableRules = true;

  private _version?: any;

  get version(): any | undefined {
    return this._version;
  }
  @Input() set version(value: any | undefined) {
    this._version = value;
    this.initializeFormRules();
  }

  constructor(
    private _fb: FormBuilder,
    private _reportService: ReportService,
    public _snackBar: MatSnackBar
  ) {
    this.formRules = this._fb.group({
      sut_description: [{ value: false, disabled: true }],
      objectives: [{ value: false, disabled: true }],
      scope: [{ value: false, disabled: true }],
    });
  }

  private initializeFormRules(): void {
    this.formRules.patchValue({
      sut_description: this.version?.sut?.description ? true : false,
      objectives: this.version?.objectives?.[0]?.objectives ? true : false,
      scope: this.version?.objectives?.[0]?.scope ? true : false,
    });
    this.disableRules = true;
    if (
      this.version?.sut?.description &&
      this.version?.objectives?.[0]?.objectives &&
      this.version?.objectives?.[0]?.scope
    ) {
      this.disableRules = false;
    }
  }

  ngOnInit(): void {}

  stripHtmlTags(input: string): string {
    return input
      .replace(/<\/?[^>]+(>|$)/g, '')
      .replace(/&nbsp;/g, ' ')
      .trim();
  }

  onFormRulesSubmit() {
    // Create FormData to handle image uploads
    const formData = new FormData();
    // Définition de l'objet JSON
    const templateData: {
      template: string;
      mapping: {
        [key: string]: string | { start: string; end: string; state: string }[];
      };
    } = {
      template: 'sut-template.docx',
      mapping: {
        '[sut.version]': this.version.name,
        '[sut.name]': this.stripHtmlTags(this.version.sut.name),
        '[sut.description]': this.stripHtmlTags(this.version.sut.description),
        '[Date]': '@DATE',
        '[Full Date]': '@DATE_LONG',
        '[sut.objectives]': this.stripHtmlTags(
          this.version.objectives?.[0]?.objectives || ''
        ),
        '[sut.scope]': this.stripHtmlTags(
          this.version.objectives?.[0]?.scope || ''
        ),
        __sections__: [
          {
            start: '[objectives-section-start]',
            end: '[objectives-section-end]',
            state: this.formRules.value['objectives'] ? 'visible' : 'hidden',
          },
          {
            start: '[scope-section-start]',
            end: '[scope-section-end]',
            state: this.formRules.value['scope'] ? 'visible' : 'hidden',
          },
        ],
      },
    };
    // // Ajouter le template
    // formData.append('template', templateData.template);

    // // Ajouter chaque clé-valeur du mapping individuellement
    // for (const key in templateData.mapping) {
    //   if (key !== '__sections__') {
    //     const value = templateData.mapping[key];
    //     if (typeof value === 'string') {
    //       formData.append(`mapping[${key}]`, value);
    //     }
    //   }
    // }

    // // Ajouter les sections individuellement
    // (
    //   templateData.mapping['__sections__'] as {
    //     start: string;
    //     end: string;
    //     state: string;
    //   }[]
    // ).forEach((section, index) => {
    //   formData.append(`sections[${index}][start]`, section.start);
    //   formData.append(`sections[${index}][end]`, section.end);
    //   formData.append(`sections[${index}][state]`, section.state);
    // });

    console.log(templateData);

    this._reportService.rulesEngagement(templateData);
    this._snackBar.open('Report generation', 'Done', {
      duration: 3000,
    });
  }
}
