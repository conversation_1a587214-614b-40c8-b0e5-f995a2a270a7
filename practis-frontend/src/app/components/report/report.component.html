<div class="bottom-section">
    <h2>
        <span class="text-primary">Rules of Engagement</span>
    </h2>
    <form [formGroup]="formRules" class="form" novalidate>
        <div>
            <div class="row custom-row">
                <div class="col-12 col-sm-6 bottom-cards">
                    <mat-card appearance="outlined">
                        <mat-card-content>
                            <div class="checkbox-group">
                                <mat-checkbox formControlName="sut_description" color="primary">SUT
                                    description</mat-checkbox><br />
                                <mat-checkbox formControlName="objectives"
                                    color="primary">Objectives</mat-checkbox><br />
                                <mat-checkbox formControlName="scope" color="primary">Scope</mat-checkbox>
                            </div>
                        </mat-card-content>
                    </mat-card>

                </div>
                <div class="col-12 col-sm-6">
                    <div><br class="hidden-sm" /><br class="hidden-sm" />
                        <button mat-raised-button color="primary" type="submit" [disabled]="disableRules"
                            (click)="onFormRulesSubmit()">Generate</button>
                    </div><br /><br />
                </div>
            </div>
        </div>

    </form>
</div>