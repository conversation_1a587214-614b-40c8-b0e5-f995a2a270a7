import { Component, Inject, OnInit } from '@angular/core';
import { MaterialModule } from 'app/material.module';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Sut } from 'app/models/sut';
import { ImageFile } from 'app/models/image-file';
import { SutService } from 'app/services/sut.service';
import { UtilsService } from 'app/utils/utils';

@Component({
  selector: 'app-sut-add-edit',
  standalone: true,
  imports: [MaterialModule],
  templateUrl: './sut-add-edit.component.html',
  styleUrl: './sut-add-edit.component.scss',
})
export class SutAddEditComponent implements OnInit {
  form: FormGroup;
  imageFiles: ImageFile[] = [];
  isLoading = false;
  data?: Sut;
  sut_provider?: string;

  constructor(
    private _fb: FormBuilder,
    private _dialogRef: MatDialogRef<SutAddEditComponent>,
    private _sutService: SutService,
    @Inject(MAT_DIALOG_DATA) public all_data: any,
    public _snackBar: MatSnackBar,
    public utilsService: UtilsService
  ) {
    this.form = this._fb.group({
      name: ['', Validators.required],
      description: [''],
    });
  }

  ngOnInit(): void {
    this.data = this.all_data.sut;
    this.sut_provider = this.all_data.sut_provider;
    //console.log(this.all_data);

    if (this.data) {
      // If editing, initialize the form with asset data
      this.initializeForm(this.data);
    }
  }

  // Populate form fields for editing an existing asset
  private initializeForm(data: Sut): void {
    this.isLoading = true;
    this.form.patchValue({
      name: data.name,
      description: data.description?.replace(/\\n/g, '\n'),
    });

    if (this.data?.images && this.data?.images.length > 0) {
      this.imageFiles = this.data.images.map((image) => new ImageFile(image));
      if (!this.imageFiles.some((image) => image.default)) {
        this.imageFiles[0].default = true;
      }
    }

    // Manually update form validity after patching values to ensure button reflects the correct state
    this.markFormGroupTouchedAndDirty(this.form);
    this.isLoading = false;
  }

  // Function to mark all controls as touched and dirty
  private markFormGroupTouchedAndDirty(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      if (control instanceof FormArray) {
        // Recursively apply to each FormGroup in the FormArray
        (control as FormArray).controls.forEach((arrayControl) => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouchedAndDirty(arrayControl);
          }
        });
      } else if (control instanceof FormGroup) {
        this.markFormGroupTouchedAndDirty(control);
      } else {
        control.markAsTouched();
        control.markAsDirty();
      }
    });
  }

  onImageSelect(event: any) {
    const images = event.target.files;
    if (images) {
      const newImages = Array.from(images).map((file, index) => {
        const reader = new FileReader();
        const imageFile = new ImageFile({
          uploadFile: file as File,
          preview: '',
          default: this.imageFiles.length === 0 && index === 0,
        });

        reader.onload = (e: any) => {
          imageFile.preview = e.target.result;
        };
        reader.readAsDataURL(file as Blob);

        return imageFile;
      });

      this.imageFiles.push(...newImages);
    }
  }

  removeImage(index: number) {
    const removedImage = this.imageFiles[index];
    this.imageFiles.splice(index, 1);

    if (removedImage.default && this.imageFiles.length > 0) {
      this.imageFiles[0].default = true;
    }
  }

  setImageAsDefault(index: number) {
    this.imageFiles.forEach((image) => (image.default = false));
    this.imageFiles[index].default = true;
  }

  onFormSubmit() {
    if (this.form.valid) {
      // Create FormData to handle image uploads
      const formData = new FormData();
      if (this.sut_provider) {
        formData.append('sut_provider', JSON.stringify(this.sut_provider));
      }

      Object.keys(this.form.value).forEach((key) => {
        formData.append(key, JSON.stringify(this.form.value[key]));
      });

      let images: { uuid: string; default: number }[] = [];
      this.imageFiles.forEach((image, index) => {
        let img = {
          uuid: image.uuid || '',
          default: image.default ? 1 : 0,
        };
        images.push(img);

        if (image.uploadFile) {
          formData.append('files', image.uploadFile);
        }
      });
      formData.append('images', JSON.stringify(images));
      //console.log(formData);

      //Uncomment later when backend work correctly
      if (this.data && this.data.uuid) {
        this._sutService.updateSut(this.data.uuid, formData).subscribe({
          next: (val: any) => {
            //console.log(val);
            this._snackBar.open('Sut detail updated!', 'Done', {
              duration: 3000,
            });
            this._dialogRef.close(val);
          },
          error: (err: any) => {
            console.error(err);
          },
        });
      } else {
        this._sutService.addSut(formData).subscribe({
          next: (val: any) => {
            this._snackBar.open('Sut added successfully', 'Done', {
              duration: 3000,
            });
            this._dialogRef.close(val);
          },
          error: (err: any) => {
            console.error(err);
          },
        });
      }
    }
  }
}
