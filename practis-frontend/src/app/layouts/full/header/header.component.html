<mat-toolbar class="topbar">

  <!-- Mobile Menu -->
  <button mat-icon-button (click)="toggleMobileNav.emit()" class="d-flex d-md-none justify-content-center"
    style="margin:-15px;">
    <mat-icon class="icon-20 d-flex">menu</mat-icon>
  </button>
  <span class="flex-1-auto"></span>
  <button mat-icon-button (click)="toggleTheme()">
    <mat-icon>{{ isDarkMode ? 'brightness_4' : 'brightness_7' }}</mat-icon>
  </button>
  <button *ngIf="isLoggedIn" mat-raised-button (click)="logout()" color="primary">Logout</button>
  <button *ngIf="!isLoggedIn" mat-raised-button (click)="login()" color="primary">Login</button>
</mat-toolbar>