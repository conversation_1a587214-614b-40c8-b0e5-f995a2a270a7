import {
  Component,
  Output,
  EventEmitter,
  Input,
  ViewEncapsulation,
} from '@angular/core';
import { MaterialModule } from 'src/app/material.module';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { NgScrollbarModule } from 'ngx-scrollbar';
import { AuthenticationService } from 'app/services/authentication.service';
import { environment } from 'environments/environment';
import { Router } from '@angular/router';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [RouterModule, CommonModule, NgScrollbarModule, MaterialModule],
  templateUrl: './header.component.html',
  encapsulation: ViewEncapsulation.None,
})
export class HeaderComponent {
  @Input() showToggle = true;
  @Input() toggleChecked = false;
  @Output() toggleMobileNav = new EventEmitter<void>();
  @Output() toggleMobileFilterNav = new EventEmitter<void>();
  @Output() toggleCollapsed = new EventEmitter<void>();
  isDarkMode = false;
  isLoggedIn = false;
  noderedUrl: string = environment.noderedUrl;
  gvmUrl: string = environment.gvmUrl;
  kiwiUrl: string = environment.kiwiUrl;

  constructor(
    private _authenticationService: AuthenticationService,
    private _router: Router
  ) {
    this.isLoggedIn = this._authenticationService.isLoggedIn();
  }

  ngOnInit() {
    let savedTheme = localStorage.getItem('theme') || 'dark';
    if (!savedTheme || savedTheme === null || savedTheme === undefined) {
      savedTheme = 'dark';
    }

    this.isDarkMode = savedTheme === 'dark';
    this.refreshTheme();
  }

  toggleTheme() {
    this.isDarkMode = !this.isDarkMode;
    localStorage.setItem('theme', this.isDarkMode ? 'dark' : 'light');
    this.refreshTheme();
  }

  refreshTheme() {
    if (this.isDarkMode) {
      document.body.classList.add('dark-theme');
      document.body.classList.remove('light-theme');
    } else {
      document.body.classList.add('light-theme');
      document.body.classList.remove('dark-theme');
    }
  }

  logout() {
    this._authenticationService.logout();
  }

  login() {
    this._authenticationService.login({
      redirectUri: window.location.origin + this._router.url,
    });
  }
}
