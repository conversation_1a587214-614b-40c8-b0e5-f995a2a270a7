import { BreakpointObserver, MediaMatcher } from '@angular/cdk/layout';
import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { Subscription } from 'rxjs';
import { <PERSON><PERSON><PERSON>av, MatSidenavContent } from '@angular/material/sidenav';
import { NavigationEnd, Router } from '@angular/router';
import { NavService } from '../../services/nav.service';
import { RouterModule } from '@angular/router';
import { MaterialModule } from 'src/app/material.module';
import { CommonModule } from '@angular/common';
import { SidebarComponent } from './sidebar/sidebar.component';
import { NgScrollbarModule } from 'ngx-scrollbar';
import { HeaderComponent } from './header/header.component';
import { NavItem } from './sidebar/nav-item/nav-item';
import { MenuService } from 'app/services/menu.service';

const MOBILE_VIEW = 'screen and (max-width: 768px)';
const TABLET_VIEW = 'screen and (min-width: 769px) and (max-width: 1024px)';
const MONITOR_VIEW = 'screen and (min-width: 1024px)';
const BELOWMONITOR = 'screen and (max-width: 1023px)';

@Component({
  selector: 'app-full',
  standalone: true,
  imports: [
    RouterModule,
    MaterialModule,
    CommonModule,
    SidebarComponent,
    NgScrollbarModule,
    HeaderComponent,
  ],
  templateUrl: './full.component.html',
  styleUrls: [],
  encapsulation: ViewEncapsulation.None,
})
export class FullComponent implements OnInit {
  menuItems: NavItem[] = [];

  @ViewChild('leftsidenav')
  public sidenav: MatSidenav | any;

  //get options from service
  private layoutChangesSubscription = Subscription.EMPTY;
  private isMobileScreen = false;
  private isContentWidthFixed = true;
  private isCollapsedWidthFixed = false;
  private htmlElement!: HTMLHtmlElement;

  get isOver(): boolean {
    return this.isMobileScreen;
  }

  constructor(
    private breakpointObserver: BreakpointObserver,
    private navService: NavService,
    public router: Router,
    public _menuService: MenuService
  ) {
    //this.htmlElement = document.querySelector('html')!;
    //this.htmlElement.classList.add('light-theme');
    this.layoutChangesSubscription = this.breakpointObserver
      .observe([MOBILE_VIEW, TABLET_VIEW, MONITOR_VIEW])
      .subscribe((state) => {
        // SidenavOpened must be reset true when layout changes

        this.isMobileScreen = state.breakpoints[MOBILE_VIEW];

        this.isContentWidthFixed = state.breakpoints[MONITOR_VIEW];
      });
  }

  ngOnInit(): void {
    this.fetchmenus();
  }

  fetchmenus() {
    this._menuService.menuItems$.subscribe((items) => {
      this.menuItems = items;
      this.router.events.subscribe((event) => {
        if (event instanceof NavigationEnd) {
          this.menuItems = this.updateMenuItems(
            [...this.menuItems],
            this.router.url
          );
        }
      });

      // Initialize on first load
      this.menuItems = this.updateMenuItems(
        [...this.menuItems],
        this.router.url
      );
    });

    this._menuService.getMenuList();
  }

  updateMenuItems(menuItems: any[], currentRoute: string): any[] {
    return menuItems.map((menuItem) => {
      menuItem.expanded = this.isRouteMatch(menuItem, currentRoute);

      if (menuItem.children && menuItem.children.length > 0) {
        menuItem.children = this.updateMenuItems(
          menuItem.children,
          currentRoute
        );
      }

      return menuItem;
    });
  }

  isRouteMatch(menuItem: any, currentRoute: string): boolean {
    if (menuItem.route === currentRoute) {
      return true;
    }

    return (
      menuItem.children?.some((child: NavItem) =>
        this.isRouteMatch(child, currentRoute)
      ) || false
    );
  }

  ngOnDestroy() {
    this.layoutChangesSubscription.unsubscribe();
  }

  toggleCollapsed() {
    this.isContentWidthFixed = false;
  }

  onSidenavClosedStart() {
    this.isContentWidthFixed = false;
  }

  onSidenavOpenedChange(isOpened: boolean) {
    this.isCollapsedWidthFixed = !this.isOver;
  }
  // Navigation method
  navigateToRoute(route: string | undefined): void {
    if (route) {
      this.router.navigate([route]);
    }
  }

  // Toggle expand/collapse
  toggleExpand(item: NavItem): void {
    item.expanded = !item.expanded;
  }

  // Improved route active checking
  isRouteActive(route: string | undefined): boolean {
    if (!route) return false;

    const currentUrl = this.router.url;

    // Exact match
    if (currentUrl === route) return true;

    // Check if current route is a child of this route
    if (route !== '/' && currentUrl.startsWith(route + '/')) {
      return true;
    }

    return false;
  }
}
