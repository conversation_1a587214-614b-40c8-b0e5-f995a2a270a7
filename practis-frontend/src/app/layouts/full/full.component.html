<mat-sidenav-container class="mainWrapper blue_theme light-theme" autosize autoFocus dir="ltr">
  <!-- ============================================================== -->
  <!-- Vertical Sidebar -->
  <!-- ============================================================== -->

  <mat-sidenav #leftsidenav [mode]="isOver ? 'over' : 'side'" [opened]="!isOver"
    (openedChange)="onSidenavOpenedChange($event)" (closedStart)="onSidenavClosedStart()" class="sidebarNav">
    <div class="flex-layout">
      <app-sidebar (toggleMobileNav)="sidenav.toggle()" [showToggle]="isOver"></app-sidebar>
      <ng-scrollbar class="position-relative" style="height: 100%">
        <mat-nav-list class="sidebar-list">
          <div class="nav-item-container">
            <a mat-list-item routerLink="/" routerLinkActive="active-link" class="nav-item"
              [routerLinkActiveOptions]="{ exact: true }">
              <div class="nav-item-content">
                <mat-icon class="nav-icon">home</mat-icon>
                <span class="nav-label">Home</span>
              </div>
            </a>
          </div>

          <ng-container *ngFor="let sut of menuItems">
            <div class="nav-item-container">
              <div class="nav-item" [class.active-link]="isRouteActive(sut.route)">
                <div class="nav-item-content">
                  <div class="nav-item-left" (click)="navigateToRoute(sut.route)">
                    <mat-icon class="nav-icon">security</mat-icon>
                    <span class="nav-label">{{sut.name}}</span>
                  </div>
                  <mat-icon class="expand-icon" (click)="toggleExpand(sut)">
                    {{sut.expanded ? 'remove' : 'add'}}
                  </mat-icon>
                </div>
              </div>

              <div class="nav-children" *ngIf="sut.expanded">
                <ng-container *ngFor="let version of sut.children">
                  <div class="nav-item-container indent-level-1">
                    <div class="nav-item" [class.active-link]="isRouteActive(version.route)">
                      <div class="nav-item-content">
                        <div class="nav-item-left" (click)="navigateToRoute(version.route)">
                          <mat-icon class="nav-icon">layers</mat-icon>
                          <span class="nav-label">{{version.name}}</span>
                        </div>
                        <mat-icon class="expand-icon" (click)="toggleExpand(version)" *ngIf="version.children?.length">
                          {{version.expanded ? 'remove' : 'add'}}
                        </mat-icon>
                      </div>
                    </div>

                    <div class="nav-children" *ngIf="version.expanded">
                      <ng-container *ngFor="let component of version.children">
                        <div class="nav-item-container indent-level-2">
                          <div class="nav-item" [class.active-link]="isRouteActive(component.route)">
                            <div class="nav-item-content">
                              <div class="nav-item-left" (click)="navigateToRoute(component.route)">
                                <mat-icon class="nav-icon">developer_board</mat-icon>
                                <span class="nav-label">{{component.name}}</span>
                              </div>
                              <mat-icon class="expand-icon" (click)="toggleExpand(component)"
                                *ngIf="component.children?.length">
                                {{component.expanded ? 'remove' : 'add'}}
                              </mat-icon>
                            </div>
                          </div>

                          <div class="nav-children" *ngIf="component.expanded">
                            <ng-container *ngFor="let item of component.children">
                              <div class="nav-item-container indent-level-3">
                                <div class="nav-item" [class.active-link]="isRouteActive(item.route)"
                                  (click)="navigateToRoute(item.route)">
                                  <div class="nav-item-content">
                                    <div class="nav-item-left">
                                      <mat-icon class="nav-icon">
                                        {{item.type === 'port' ? 'settings_ethernet' : 'memory'}}
                                      </mat-icon>
                                      <span class="nav-label">{{item.name}}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </ng-container>
                          </div>
                        </div>
                      </ng-container>
                    </div>
                  </div>
                </ng-container>
              </div>
            </div>
          </ng-container>

          <!-- <div class="nav-item-container">
            <a mat-list-item routerLink="/reports" routerLinkActive="active-link" class="nav-item">
              <div class="nav-item-content">
                <mat-icon class="nav-icon">assessment</mat-icon>
                <span class="nav-label">Reports</span>
              </div>
            </a>
          </div> -->
          <!-- <div class="nav-item-container">
            <a mat-list-item routerLink="/diagram" routerLinkActive="active-link" class="nav-item">
              <div class="nav-item-content">
                <mat-icon class="nav-icon">account_tree</mat-icon>
                <span class="nav-label">Diagram</span>
              </div>
            </a>
          </div> -->

          <!-- <div class="nav-item-container">
            <a mat-list-item routerLink="/users" routerLinkActive="active-link" class="nav-item">
              <div class="nav-item-content">
                <mat-icon class="nav-icon">people</mat-icon>
                <span class="nav-label">Users</span>
              </div>
            </a>
          </div> -->
        </mat-nav-list>
        <br /><br />
      </ng-scrollbar>
    </div>
  </mat-sidenav>





  <!-- ============================================================== -->
  <!-- Main Content -->
  <!-- ============================================================== -->
  <mat-sidenav-content class="contentWrapper" #content>
    <!-- ============================================================== -->
    <!-- VerticalHeader -->
    <!-- ============================================================== -->

    <app-header [showToggle]="!isOver" (toggleCollapsed)="toggleCollapsed()"
      (toggleMobileNav)="sidenav.toggle()"></app-header>


    <main class="pageWrapper maxWidth">
      <!-- ============================================================== -->
      <!-- Outlet -->
      <!-- ============================================================== -->
      <router-outlet></router-outlet>
    </main>
  </mat-sidenav-content>

</mat-sidenav-container>