import { Routes } from '@angular/router';
import { FullComponent } from './layouts/full/full.component';
import { UnknownComponent } from './components/unknown/unknown.component';
import { RestrictedComponent } from './components/restricted/restricted.component';
import { HomeComponent } from './components/home/<USER>';
import { AuthGuard } from './services/auth.guard';
import { BlankComponent } from './layouts/blank/blank.component';
import { SutViewComponent } from './components/sut-view/sut-view.component';
import { VersionViewComponent } from './components/version-view/version-view.component';
import { DiagramBuilderComponent } from './components/diagram-builder/diagram-builder.component';
import { ElementViewComponent } from './components/element-view/element-view.component';

export const routes: Routes = [
  {
    path: '',
    component: FullComponent,
    children: [
      {
        path: '',
        pathMatch: 'full',
        component: HomeComponent,
        title: 'Home | PRACTIS',
        canActivate: [AuthGuard],
      },
      {
        path: 'sut/:sutId',
        component: SutViewComponent,
        title: 'SUT | PRACTIS',
        canActivate: [AuthGuard],
      },
      {
        path: 'sut/:sutId/version/:versionId',
        component: VersionViewComponent,
        title: 'Version | PRACTIS',
        canActivate: [AuthGuard],
      },
      {
        path: 'sut/:sutId/version/:versionId/component/:componentId',
        component: ElementViewComponent,
        title: 'Component | PRACTIS',
        canActivate: [AuthGuard],
      },
      {
        path: 'sut/:sutId/version/:versionId/component/:componentId/subcomponent/:subcomponentId',
        component: ElementViewComponent,
        title: 'Sub Component | PRACTIS',
        canActivate: [AuthGuard],
      },
      {
        path: 'sut/:sutId/version/:versionId/component/:componentId/port/:portId',
        component: ElementViewComponent,
        title: 'Port | PRACTIS',
        canActivate: [AuthGuard],
      },
    ],
  },
  {
    path: '',
    component: BlankComponent,
    children: [
      {
        path: 'restricted',
        component: RestrictedComponent,
        title: 'Restricted | PRACTIS',
      },
      {
        path: '**',
        component: UnknownComponent,
        title: 'Unknown | PRACTIS',
      },
    ],
  },
];
