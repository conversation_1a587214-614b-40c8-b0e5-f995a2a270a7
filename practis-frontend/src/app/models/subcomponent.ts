import { ComponentModel } from './component';
import { FlowExecution } from './flow-execution';
import { Parameter, ParameterModel } from './parameter';
import { MiniSut } from './sut';
import { Version } from './version';
import { Vulnerability } from './vulnerability';
import { ImageFile } from './image-file';
export interface SubcomponentModel {
  id?: number;
  availability: boolean;
  confidentiality: boolean;
  description: string;
  integrity: boolean;
  name: string;
  notes: string;
  version: string;
  component: string;
  images?: ImageFile[];

  parameters?: ParameterModel[];
}

export interface SubcomponentGetData {
  id?: number;
  availability: boolean;
  confidentiality: boolean;
  description: string;
  integrity: boolean;
  name: string;
  notes: string;
  images?: ImageFile[];

  sut: MiniSut;
  version: Version;
  component?: ComponentModel;
  parameters?: Parameter[];
  vulnerabilities?: Vulnerability[];
  flowexecutions?: FlowExecution[];
}
