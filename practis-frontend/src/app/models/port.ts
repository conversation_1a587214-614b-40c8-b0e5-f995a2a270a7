import { ComponentModel } from './component';
import { FlowExecution } from './flow-execution';
import { Parameter, ParameterModel } from './parameter';
import { MiniSut } from './sut';
import { Version } from './version';
import { Vulnerability } from './vulnerability';

export interface PortModel {
  id?: number;
  availability: boolean;
  confidentiality: boolean;
  description: string;
  integrity: boolean;
  name: string;
  notes: string;
  version: string;
  component: string;

  parameters?: ParameterModel[];
}

export interface PortGetData {
  id?: number;
  availability: boolean;
  confidentiality: boolean;
  description: string;
  integrity: boolean;
  name: string;
  notes: string;

  sut: MiniSut;
  version: Version;
  component?: ComponentModel;
  parameters?: Parameter[];
  vulnerabilities?: Vulnerability[];
  flowexecutions?: FlowExecution[];
}
