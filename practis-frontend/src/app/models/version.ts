import { Sut } from './sut';
import { ImageFile } from './image-file';
import { ObjectivesAndScope } from './objective-scope';
import { RiskAnalysisWithRisks } from './risk-analysis';
import { Requirement } from './requirement';
import { FlowExecution } from './flow-execution';

export class Version {
  uuid?: string;
  name?: string;
  description?: string;
  notes?: string;
  status?: string;
  diagram_json?: string;
  sut?: Sut;
  images?: ImageFile[];
}

export class VersionWithAllData {
  uuid?: string;
  name?: string;
  description?: string;
  notes?: string;
  diagram_json?: string;
  status?: string;
  sut?: Sut;
  images?: ImageFile[];
  objectives?: ObjectivesAndScope[];
  riskanalysis?: RiskAnalysisWithRisks[];
  requirements?: Requirement[];
  flowexecutions?: FlowExecution[];
}
