import { Version } from './version';
import { ComponentModel } from './component';
import { InterfaceModel } from './interface';
import { SubcomponentModel } from './subcomponent';
import { PortModel } from './port';

export class Note {
  uuid?: string;
  text?: string;
  component?: ComponentModel;
  interface?: InterfaceModel;
  port?: PortModel;
  subComponent?: SubcomponentModel;

  constructor(init?: Partial<Note>) {
    Object.assign(this, init);
  }
}

