import { environment } from 'environments/environment';
import { ImageFile } from './image-file';

export class Address {
  city?: string;
  state?: string;
  street?: string;
  zip?: string;
}

export class Contact {
  name?: string;
  mail?: string;
  phone?: string;
  description?: string;
}

export class SutProvider {
  uuid?: string;
  name?: string;
  description?: string;
  address?: Address;
  contacts?: Contact[];
  images?: ImageFile[];

  constructor(init?: Partial<SutProvider>) {
    Object.assign(this, init);
  }

  get fullAddress(): string {
    if (!this.address) {
      return 'No address available';
    }
    return `${this.address?.street}, ${this.address?.city}, ${this.address?.state}, ${this.address?.zip}`;
  }
}
