import { FlowExecution } from './flow-execution';
import { Parameter, ParameterModel } from './parameter';
import { MiniSut } from './sut';
import { Version } from './version';
import { Vulnerability } from './vulnerability';
import { ImageFile } from './image-file';

export interface ComponentModel {
  id?: string;
  availability: boolean;
  confidentiality: boolean;
  description: string;
  integrity: boolean;
  name: string;
  notes: string;
  version: string;
  parameters?: ParameterModel[];
   images?: ImageFile[];
}

export interface ComponentGetData {
  id?: string;
  availability: boolean;
  confidentiality: boolean;
  description: string;
  integrity: boolean;
  name: string;
  notes: string;
   images?: ImageFile[];


  sut: MiniSut;
  version: Version;
  parameters?: Parameter[];
  vulnerabilities?: Vulnerability[];
  flowexecutions?: FlowExecution[];
}
