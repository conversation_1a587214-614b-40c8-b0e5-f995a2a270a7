import { ParameterModel } from './parameter';

export interface InterfaceModel {
    id?: string;
    availability: boolean;
    confidentiality: boolean;
    description: string;
    integrity: boolean;
    name: string;
    notes: string;
    version: string | null;

    parameters?: ParameterModel[];

    type: 'internal' | 'external';
    port_from: string;
    port_to_port?: string | null;
    port_to_subcomponent?: string | null;
}