import { ParameterType } from './parameter-type';

export class Flow {
  uuid?: string;
  name?: string;
  description?: string;
  address?: string;
  api_url?: string;
  parameter_type?: ParameterType[] = [];
  nb_conditions?: number = 0;

  constructor(init?: Partial<Flow>) {
    Object.assign(this, init);
  }

  get parameter_type_names(): string[] {
    return (
      this.parameter_type
        ?.filter((param) => param?.name !== undefined)
        .map((param) => param.name as string) || []
    );
  }

  get parameter_type_ids(): string[] {
    return (
      this.parameter_type
        ?.filter((param) => param?.id !== undefined)
        .map((param) => param.id as string) || []
    );
  }
}

export class FlowForm {
  uuid?: string;
  name?: string;
  description?: string;
  address?: string;
  api_url?: string;
  parameter_type?: string[];

  constructor(init?: Partial<Flow>) {
    Object.assign(this, init);
  }
}
