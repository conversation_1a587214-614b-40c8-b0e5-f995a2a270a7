import { TestCasePriority, TestCaseStatus } from './enum-types';
import { Requirement } from './requirement';
import { Vulnerability } from './vulnerability';

// Type to represent different file types
type FileType = 'pdf' | 'image' | 'document' | 'video' | 'other';

// Interface for each attachment
interface Attachment {
  id?: string;        // Unique id
  filename: string;   // File name
  originalName?: string; // Original file name before upload
  path: string;       // File path or URL
  mimeType: string;   // MIME type (e.g., 'application/pdf', 'image/jpeg')
  fileType: FileType; // Categorized file type
  size: number;       // Size in bytes
  uploadDate: Date;   // Upload date
  metadata?: Record<string, any>; // Additional metadata (optional)
}

export class TestCase {
  uuid?: string;
  name?: string;
  attachments: Attachment[] = [];
  attack_technique?: string;
  category?: string;
  description?: string;
  priority?: TestCasePriority;
  recommendations?: string;
  source?: string;
  status?: TestCaseStatus;
  vulnerability?: Vulnerability;
  requirement?: Requirement;
  testPlan?: string;
  
  constructor(init?: Partial<TestCase>) {
    Object.assign(this, init);
  }
}