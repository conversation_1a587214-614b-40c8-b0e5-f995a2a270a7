import { TestPlan } from "./test-plan";
import { TestCase } from "./test-case";
import { Version } from "./version";
import { TestExecution } from "./test-execution";

export class TestRun {
    uuid?: string;
    name?: string;
    description?: string;
    start_date?: Date;
    end_date?: Date;
    version?: Version | string;
    plan?: TestPlan;
    test_cases?: TestCase[];
    test_executions?: TestExecution[];
    constructor(init?: Partial<TestRun>) {
        Object.assign(this, init);
      }
}