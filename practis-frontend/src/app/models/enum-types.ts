export enum VersionStatus {
  InDevelopment = 'in development',
  InProduction = 'in production',
}

export enum ObjectiveType {
  EvaluatingSecurity = 'evaluating security',
  ValidatingRiskAnalysis = 'validating risk analysis',
  VerifyingCorrectedVulnerabilities = 'verifying corrected vulnerabilities',
}

export enum RequirementPriority {
  Low = 'Low',
  Medium = 'Medium',
  High = 'High',
  Critical = 'Critical',
}

export enum RequirementStatus {
  Pending = 'Pending',
  InProgress = 'In Progress',
  Completed = 'Completed',
  Cancelled = 'Cancelled',
}

export enum RiskStatus {
  New = 'new',
  Confirmed = 'confirmed',
  Unconfirmed = 'unconfirmed',
  Treated = 'treated',
}

export enum RiskValue {
  Low = 'Low',
  Medium = 'Medium',
  High = 'High',
}

export enum VulnerabilityStatus {
  New = 'new',
  Confirmed = 'confirmed',
  Unconfirmed = 'unconfirmed',
  Resolved = 'resolved',
  ConfirmedResolved = 'confirmed and resolved',
}

export enum FlowExecutionResult {
  Passed = 'pass',
  Failed = 'fail',
  Pending = 'n/a',
}
export enum TestCasePriority {
  P1 = '1',
  P2 = '2',
  P3 = '3',
  P4 = '4',
  P5 = '5'
}

export enum TestCaseStatus {
  Proposed = 'PROPOSED',
  Confirmed = 'CONFIRMED',
  Disabled = 'DISABLED',
  NeedUpdate = 'NEED UPDATE',
}