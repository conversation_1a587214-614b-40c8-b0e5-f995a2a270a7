import { Risk } from './risk';
import { ComponentModel } from './component';
import { SubcomponentModel } from './subcomponent';
import { PortModel } from './port';
import { InterfaceModel } from './interface';
import { VulnerabilityStatus } from './enum-types';

export class Vulnerability {
  uuid?: string;
  gvm_uuid?: string;
  name?: string;
  description?: string;
  recommendations?: string;
  cve_cwe?: string;
  attack_path_or_vector?: string;
  status?: VulnerabilityStatus = VulnerabilityStatus.New;
  risks?: Risk[];
  component?: ComponentModel;
  subcomponent?: SubcomponentModel;
  port?: PortModel;
  interface?: InterfaceModel;

  get risk_uuids(): string[] {
    return (
      this.risks
        ?.filter((risk) => risk?.uuid !== undefined)
        .map((risk) => risk.uuid as string) || []
    );
  }

  constructor(init?: Partial<Vulnerability>) {
    Object.assign(this, init);
  }
}

export class VulnerabilityForm {
  uuid?: string;
  name?: string;
  description?: string;
  recommendations?: string;
  cve_cwe?: string;
  attack_path_or_vector?: string;
  status?: VulnerabilityStatus = VulnerabilityStatus.New;
  risks?: string[];
  component?: string;
  subcomponent?: string;
  port?: string;
  interface?: string;

  constructor(init?: Partial<VulnerabilityForm>) {
    Object.assign(this, init);
  }
}
