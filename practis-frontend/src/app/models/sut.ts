import { ImageFile } from './image-file';
import { SutProvider } from './sut-provider';
import { Version } from './version';

export class Sut {
  uuid?: string;
  name?: string;
  description?: string;
  tcms_external_uuid?: string;
  sut_provider?: SutProvider;
  images?: ImageFile[];

  constructor(init?: Partial<Sut>) {
    Object.assign(this, init);
  }
}

export class SutWithVersions extends Sut {
  versions?: Version[];
}

export class MiniSut {
  uuid?: string;
  name?: string;
  description?: string;
  tcms_external_uuid?: string;
  sut_provider?: string;
  images?: string[];
}
