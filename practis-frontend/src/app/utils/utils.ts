import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { ImageFile } from 'app/models/image-file'; // Adjust path as needed
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class UtilsService {
  constructor(private sanitizer: DomSanitizer) {}

  getSafeDescription(description: any): SafeHtml {
    // Check if description is longer than 200 characters
    const croppedDescription =
      description && description.length > 500
        ? description.slice(0, 500) + '...'
        : description;

    // Return the sanitized HTML with line breaks (if any)
    return this.sanitizer.bypassSecurityTrustHtml(
      croppedDescription?.replace(/(?:\r\n|\r|\n|\\n)/g, '<br>') || ''
    );
  }

  getDefaultImage(images: ImageFile[]): string {
    const defaultImage = images.find((image) => image.default);
    return defaultImage && defaultImage.file
      ? this.getImage(defaultImage.file)
      : '';
  }

  getSliderImages(images: ImageFile[]): Array<Object> {
    let imageObject: Array<Object> = [];

    // Find the default image
    const defaultImage = images.find((image) => image.default);

    if (defaultImage) {
      imageObject.push({
        image: this.getImage(defaultImage.file),
        thumbImage: this.getImage(defaultImage.file),
      });
    }

    // Add remaining images, excluding the default one (to avoid duplication)
    images.forEach((image) => {
      if (!image.default) {
        imageObject.push({
          image: this.getImage(image.file),
          thumbImage: this.getImage(image.file),
        });
      }
    });

    return imageObject;
  }

  getImage(image: any): string {
    return image ? environment.imagePrefixUrl + image : '';
  }

  getFullAddress(address: any): string {
    if (!address) {
      return 'No address available';
    }
    return `${address?.street}, ${address?.city}, ${address?.state}, ${address?.zip}`;
  }
}
