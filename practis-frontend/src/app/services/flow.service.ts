import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, of } from 'rxjs';
import { Flow } from '../models/flow';
import { catchError } from 'rxjs/operators'; // Add this import

@Injectable({
  providedIn: 'root',
})
export class FlowService {
  private apiUrl = environment.apiUrl;

  constructor(private _http: HttpClient) {}

  // Method to add a new flow
  addFlow(data: Flow): Observable<Flow> {
    return this._http.post<Flow>(`${this.apiUrl}/flow/`, data);
  }

  // Method to add a new flow
  launchFlow(data: any, url: string): Observable<any> {
    return this._http.post<any>(`${url}`, data);
  }

  // Method to update an existing flow
  updateFlow(uuid: string, data: Flow): Observable<Flow> {
    return this._http.put<Flow>(`${this.apiUrl}/flow/${uuid}/`, data);
  }

  // Method to get a list of flows
  getFlowList(): Observable<Flow[]> {
    return this._http.get<Flow[]>(`${this.apiUrl}/flow/`);
  }

  // Method to get a specific flow
  getFlowById(uuid: string): Observable<Flow> {
    return this._http.get<Flow>(`${this.apiUrl}/flow/${uuid}/`);
  }

  // Method to delete an flow
  deleteFlow(uuid: string): Observable<void> {
    return this._http
      .delete<void>(`${this.apiUrl}/flow/${uuid}/`, { observe: 'response' })
      .pipe(
        catchError((error) => {
          // You can handle the error here or let it propagate, depending on your needs.
          //console.error('Error during deletion:', error);
          return of(error); // Return an observable with the error to continue the stream
        })
      );
  }
}
