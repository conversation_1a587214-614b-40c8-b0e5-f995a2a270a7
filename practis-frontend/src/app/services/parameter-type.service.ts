import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, of } from 'rxjs';
import { ParameterType } from '../models/parameter-type';
import { catchError } from 'rxjs/operators'; // Add this import

@Injectable({
  providedIn: 'root',
})
export class ParameterTypeService {
  private apiUrl = environment.apiUrl;

  constructor(private _http: HttpClient) {}

  // Method to get a list of parameter-types
  getParameterTypeList(): Observable<ParameterType[]> {
    return this._http.get<ParameterType[]>(`${this.apiUrl}/parameter-type/`);
  }
}
