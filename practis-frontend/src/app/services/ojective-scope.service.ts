import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, of } from 'rxjs';
import { ObjectivesAndScope } from '../models/objective-scope';
import { catchError } from 'rxjs/operators'; // Add this import

@Injectable({
  providedIn: 'root',
})
export class OjectiveScopeService {
  private apiUrl = environment.apiUrl;

  constructor(private _http: HttpClient) {}

  // Method to add a new objectivesandscope
  addOjectiveScope(data: any): Observable<ObjectivesAndScope> {
    return this._http.post<ObjectivesAndScope>(
      `${this.apiUrl}/objectivesandscope/`,
      data
    );
  }

  // Method to update an existing objectivesandscope
  updateOjectiveScope(uuid: string, data: any): Observable<ObjectivesAndScope> {
    return this._http.put<ObjectivesAndScope>(
      `${this.apiUrl}/objectivesandscope/${uuid}/`,
      data
    );
  }

  // Method to get a list of riskanalysiss
  getOjectiveScopeList(): Observable<ObjectivesAndScope[]> {
    return this._http.get<ObjectivesAndScope[]>(
      `${this.apiUrl}/objectivesandscope/`
    );
  }

  // Method to get a specific riskanalysis
  getOjectiveScopeById(uuid: string): Observable<ObjectivesAndScope> {
    return this._http.get<ObjectivesAndScope>(
      `${this.apiUrl}/objectivesandscope/${uuid}/`
    );
  }

  // Method to delete an riskanalysis
  deleteOjectiveScope(uuid: string): Observable<void> {
    return this._http
      .delete<void>(`${this.apiUrl}/objectivesandscope/${uuid}/`, {
        observe: 'response',
      })
      .pipe(
        catchError((error) => {
          // You can handle the error here or let it propagate, depending on your needs.
          //console.error('Error during deletion:', error);
          return of(error); // Return an observable with the error to continue the stream
        })
      );
  }
}
