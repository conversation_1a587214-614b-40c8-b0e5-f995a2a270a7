import { Injectable } from '@angular/core';
import {
  KeycloakAngularModule,
  KeycloakService,
  KeycloakEventType,
} from 'keycloak-angular';

@Injectable({
  providedIn: 'root',
})
export class AuthenticationService {
  constructor(private _keycloakService: KeycloakService) {
    /*_keycloakService.keycloakEvents$.subscribe({
      next(event) {
        if (event.type == KeycloakEventType.OnTokenExpired) {
          _keycloakService.updateToken(20);
        }
      },
    });*/
  }

  public login(url?: any) {
    if (url) {
      this._keycloakService.login(url);
    } else {
      this._keycloakService.login();
    }
  }

  public logout() {
    this._keycloakService.logout(window.location.origin);
  }

  public isLoggedIn() {
    return this._keycloakService.isLoggedIn();
  }
}
