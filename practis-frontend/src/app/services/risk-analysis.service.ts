import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, of } from 'rxjs';
import { RiskAnalysis } from '../models/risk-analysis';
import { catchError } from 'rxjs/operators'; // Add this import

@Injectable({
  providedIn: 'root',
})
export class RiskAnalysisService {
  private apiUrl = environment.apiUrl;

  constructor(private _http: HttpClient) {}

  // Method to add a new riskanalysis
  addRiskAnalysis(data: any): Observable<RiskAnalysis> {
    return this._http.post<RiskAnalysis>(`${this.apiUrl}/riskanalysis/`, data);
  }

  // Method to update an existing riskanalysis
  updateRiskAnalysis(uuid: string, data: any): Observable<RiskAnalysis> {
    return this._http.put<RiskAnalysis>(
      `${this.apiUrl}/riskanalysis/${uuid}/`,
      data
    );
  }

  // Method to get a list of riskanalysiss
  getRiskAnalysisList(): Observable<RiskAnalysis[]> {
    return this._http.get<RiskAnalysis[]>(`${this.apiUrl}/riskanalysis/`);
  }

  // Method to get a specific riskanalysis
  getRiskAnalysisById(uuid: string): Observable<RiskAnalysis> {
    return this._http.get<RiskAnalysis>(`${this.apiUrl}/riskanalysis/${uuid}/`);
  }

  // Method to delete an riskanalysis
  deleteRiskAnalysis(uuid: string): Observable<void> {
    return this._http
      .delete<void>(`${this.apiUrl}/riskanalysis/${uuid}/`, {
        observe: 'response',
      })
      .pipe(
        catchError((error) => {
          // You can handle the error here or let it propagate, depending on your needs.
          //console.error('Error during deletion:', error);
          return of(error); // Return an observable with the error to continue the stream
        })
      );
  }
}
