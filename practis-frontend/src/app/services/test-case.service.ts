import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, of } from 'rxjs';
import { TestCase } from '../models/test-case';
import { catchError } from 'rxjs/operators'; // Add this import

@Injectable({
  providedIn: 'root',
})
export class TestCaseService {
  private apiUrl = environment.apiUrl;

  constructor(private _http: HttpClient) {}

  // Method to add a new test-case
  addTestCase(data: TestCase): Observable<TestCase> {
    return this._http.post<TestCase>(`${this.apiUrl}/testcase/`, data);
  }

  // Method to update an existing test-case
  updateTestCase(id: string, data: TestCase): Observable<TestCase> {
    return this._http.put<TestCase>(`${this.apiUrl}/testcase/${id}/`, data);
  }

  // Method to get a list of test-cases
  getTestCaseList(): Observable<TestCase[]> {
    return this._http.get<TestCase[]>(`${this.apiUrl}/testcase/`);
  }

  // Method to get a specific test-case
  getTestCaseById(id: string): Observable<TestCase> {
    return this._http.get<TestCase>(`${this.apiUrl}/testcase/${id}/`);
  }

  // Method to delete an test-case
  deleteTestCase(id: string): Observable<void> {
    return this._http
      .delete<void>(`${this.apiUrl}/testcase/${id}/`, { observe: 'response' })
      .pipe(
        catchError((error) => {
          // You can handle the error here or let it propagate, depending on your needs.
          //console.error('Error during deletion:', error);
          return of(error); // Return an observable with the error to continue the stream
        })
      );
  }
}
