import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { NavItem } from 'app/layouts/full/sidebar/nav-item/nav-item';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class MenuService {
  private apiUrl = environment.apiUrl;
  private menuItemsSource = new BehaviorSubject<NavItem[]>([]);
  menuItems$ = this.menuItemsSource.asObservable();

  constructor(private _http: HttpClient) {}

  // Method to get a list of menus
  getMenuList() {
    return this._http
      .get<NavItem[]>(`${this.apiUrl}/menu/`)
      .subscribe((data) => {
        this.menuItemsSource.next(data);
      });
  }
}
