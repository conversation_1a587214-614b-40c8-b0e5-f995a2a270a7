import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, of } from 'rxjs';
import { Vulnerability, VulnerabilityForm } from '../models/vulnerability';
import { catchError } from 'rxjs/operators'; // Add this import

@Injectable({
  providedIn: 'root',
})
export class VulnerabilityService {
  private apiUrl = environment.apiUrl;

  constructor(private _http: HttpClient) {}

  // Method to add a new vulnerability
  addVulnerability(data: VulnerabilityForm): Observable<Vulnerability> {
    return this._http.post<Vulnerability>(
      `${this.apiUrl}/vulnerability/`,
      data
    );
  }

  // Method to update an existing vulnerability
  updateVulnerability(
    uuid: string,
    data: VulnerabilityForm
  ): Observable<Vulnerability> {
    return this._http.put<Vulnerability>(
      `${this.apiUrl}/vulnerability/${uuid}/`,
      data
    );
  }

  // Method to get a list of vulnerabilitys
  getVulnerabilityList(): Observable<Vulnerability[]> {
    return this._http.get<Vulnerability[]>(`${this.apiUrl}/vulnerability/`);
  }

  // Method to get a specific vulnerability
  getVulnerabilityById(uuid: string): Observable<Vulnerability> {
    return this._http.get<Vulnerability>(
      `${this.apiUrl}/vulnerability/${uuid}/`
    );
  }

  // Method to delete an vulnerability
  deleteVulnerability(uuid: string): Observable<void> {
    return this._http
      .delete<void>(`${this.apiUrl}/vulnerability/${uuid}/`, {
        observe: 'response',
      })
      .pipe(
        catchError((error) => {
          // You can handle the error here or let it propagate, depending on your needs.
          //console.error('Error during deletion:', error);
          return of(error); // Return an observable with the error to continue the stream
        })
      );
  }
}
