import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, of } from 'rxjs';
import { TestPlan } from '../models/test-plan';
import { catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class TestPlanService {
  private apiUrl = environment.apiUrl;

  constructor(private _http: HttpClient) {}

  // Method to add a new test-plan
  addTestPlan(data: TestPlan): Observable<TestPlan> {
    return this._http.post<TestPlan>(`${this.apiUrl}/testplan/`, data);
  }

  // Method to update an existing test-plan
  updateTestPlan(id: string, data: TestPlan): Observable<TestPlan> {
    return this._http.put<TestPlan>(`${this.apiUrl}/testplan/${id}/`, data);
  }

  // Method to get a list of test-plans
  getTestPlanList(): Observable<TestPlan[]> {
    return this._http.get<TestPlan[]>(`${this.apiUrl}/testplan/`);
  }

  // Method to get a specific test-plan
  getTestPlanById(id: string): Observable<TestPlan> {
    return this._http.get<TestPlan>(`${this.apiUrl}/testplan/${id}/`);
  }

  // Method to delete an test-plan
  deleteTestPlan(id: string): Observable<void> {
    return this._http
      .delete<void>(`${this.apiUrl}/testplan/${id}/`, { observe: 'response' })
      .pipe(
        catchError((error) => {
          //console.error('Error during deletion:', error);
          return of(error); // Return an observable with the error
        })
      );
  }
}
