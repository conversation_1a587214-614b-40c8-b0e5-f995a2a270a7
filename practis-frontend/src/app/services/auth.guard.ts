import { Injectable } from '@angular/core';
import { CanActivate, RouterStateSnapshot } from '@angular/router';
import { KeycloakService } from 'keycloak-angular';

@Injectable({ providedIn: 'root' })
export class AuthGuard implements CanActivate {
  constructor(private _keycloakService: KeycloakService) {}

  async canActivate(route: any, state: RouterStateSnapshot): Promise<boolean> {
    const isAuthenticated = await this._keycloakService.isLoggedIn();

    if (!isAuthenticated) {
      // Redirect to Keycloak login with the intended route as the redirectUri
      await this._keycloakService.login({
        redirectUri: window.location.origin + state.url, // Redirect to the next page after login
      });
      return false; // Prevent navigation until login is successful
    }

    return true; // Allow access if the user is authenticated
  }
}
