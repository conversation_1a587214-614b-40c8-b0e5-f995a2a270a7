import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, of } from 'rxjs';
import { Version, VersionWithAllData } from '../models/version';
import { catchError } from 'rxjs/operators'; // Add this import

@Injectable({
  providedIn: 'root',
})
export class VersionService {
  private apiUrl = environment.apiUrl;

  constructor(private _http: HttpClient) {}

  // Method to add a new version
  addVersion(data: any): Observable<Version> {
    return this._http.post<Version>(`${this.apiUrl}/version/`, data);
  }

  // Method to update an existing version
  updateVersion(uuid: string, data: any): Observable<Version> {
    return this._http.put<Version>(`${this.apiUrl}/version/${uuid}/`, data);
  }

  // Method to get a list of versions
  getVersionList(): Observable<Version[]> {
    return this._http.get<Version[]>(`${this.apiUrl}/version/`);
  }

  // Method to get a specific version
  getVersionById(uuid: string): Observable<VersionWithAllData> {
    return this._http.get<Version>(`${this.apiUrl}/version/${uuid}/`);
  }

  // Method to delete an version
  deleteVersion(uuid: string): Observable<void> {
    return this._http
      .delete<void>(`${this.apiUrl}/version/${uuid}/`, { observe: 'response' })
      .pipe(
        catchError((error) => {
          // You can handle the error here or let it propagate, depending on your needs.
          //console.error('Error during deletion:', error);
          return of(error); // Return an observable with the error to continue the stream
        })
      );
  }
}
