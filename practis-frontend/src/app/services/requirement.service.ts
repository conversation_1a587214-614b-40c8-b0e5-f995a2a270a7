import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, of } from 'rxjs';
import { Requirement } from '../models/requirement';
import { catchError } from 'rxjs/operators'; // Add this import

@Injectable({
  providedIn: 'root',
})
export class RequirementService {
  private apiUrl = environment.apiUrl;

  constructor(private _http: HttpClient) {}

  // Method to add a new requirement
  addRequirement(data: any): Observable<Requirement> {
    return this._http.post<Requirement>(`${this.apiUrl}/requirement/`, data);
  }

  // Method to update an existing requirement
  updateRequirement(uuid: string, data: any): Observable<Requirement> {
    return this._http.put<Requirement>(
      `${this.apiUrl}/requirement/${uuid}/`,
      data
    );
  }

  // Method to get a list of requirements
  getRequirementList(): Observable<Requirement[]> {
    return this._http.get<Requirement[]>(`${this.apiUrl}/requirement/`);
  }

  // Method to get a specific requirement
  getRequirementById(uuid: string): Observable<Requirement> {
    return this._http.get<Requirement>(`${this.apiUrl}/requirement/${uuid}/`);
  }

  // Method to delete an requirement
  deleteRequirement(uuid: string): Observable<void> {
    return this._http
      .delete<void>(`${this.apiUrl}/requirement/${uuid}/`, {
        observe: 'response',
      })
      .pipe(
        catchError((error) => {
          // You can handle the error here or let it propagate, depending on your needs.
          //console.error('Error during deletion:', error);
          return of(error); // Return an observable with the error to continue the stream
        })
      );
  }
}
