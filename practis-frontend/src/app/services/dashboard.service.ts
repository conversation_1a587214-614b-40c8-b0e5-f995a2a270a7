import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators'; // Add this import

@Injectable({
  providedIn: 'root',
})
export class DashboardService {
  private apiUrl = environment.apiUrl;

  constructor(private _http: HttpClient) {}

  // Method to get a list of dashboards
  getDashboardData(): Observable<any> {
    return this._http.get<any>(`${this.apiUrl}/dashboard/`);
  }
}
