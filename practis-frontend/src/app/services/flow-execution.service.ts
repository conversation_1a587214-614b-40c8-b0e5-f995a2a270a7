import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, of } from 'rxjs';
import { FlowExecution } from '../models/flow-execution';
import { catchError } from 'rxjs/operators'; // Add this import

@Injectable({
  providedIn: 'root',
})
export class FlowExecutionService {
  private apiUrl = environment.apiUrl;

  constructor(private _http: HttpClient) {}

  // Method to add a new flow-execution
  addFlowExecution(data: FlowExecution): Observable<FlowExecution> {
    return this._http.post<FlowExecution>(
      `${this.apiUrl}/flowexecution/`,
      data
    );
  }

  // Method to update an existing flow-execution
  updateFlowExecution(
    uuid: string,
    data: FlowExecution
  ): Observable<FlowExecution> {
    return this._http.put<FlowExecution>(
      `${this.apiUrl}/flowexecution/${uuid}/`,
      data
    );
  }

  // Method to get a list of flow-executions
  getFlowExecutionList(): Observable<FlowExecution[]> {
    return this._http.get<FlowExecution[]>(`${this.apiUrl}/flowexecution/`);
  }

  // Method to get a specific flow-execution
  getFlowExecutionById(uuid: string): Observable<FlowExecution> {
    return this._http.get<FlowExecution>(
      `${this.apiUrl}/flowexecution/${uuid}/`
    );
  }

  // Method to delete an flow-execution
  deleteFlowExecution(uuid: string): Observable<void> {
    return this._http
      .delete<void>(`${this.apiUrl}/flowexecution/${uuid}/`, {
        observe: 'response',
      })
      .pipe(
        catchError((error) => {
          // You can handle the error here or let it propagate, depending on your needs.
          //console.error('Error during deletion:', error);
          return of(error); // Return an observable with the error to continue the stream
        })
      );
  }
}
