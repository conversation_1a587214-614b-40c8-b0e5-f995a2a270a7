import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { TestRun } from '../models/test-run';


@Injectable({
  providedIn: 'root',
})
export class TestRunService {
  private apiUrl = environment.apiUrl;

  constructor(private _http: HttpClient) {}

  // Method to add a new testrun
  addTestRun(data: TestRun): Observable<TestRun> {
    console.log('Adding test run with data:', JSON.stringify(data));
    return this._http.post<TestRun>(`${this.apiUrl}/testrun/`, data)
      .pipe(
        catchError((error) => {
          console.log('Server response for creation:', error);
          // Si le backend retourne un code 201 (Created) mais avec un format non prévu
          if (error.status === 201) {
            return of(error.error || {} as TestRun);
          }
          throw error; // Propager l'erreur si c'est vraiment une erreur
        })
      );
  }

  // Method to update an existing testrun
  updateTestRun(id: string, data: TestRun): Observable<TestRun> {
    console.log('Updating test run with data:', JSON.stringify(data));
    return this._http.put<TestRun>(`${this.apiUrl}/testrun/${id}/`, data)
      .pipe(
        catchError((error) => {
          console.log('Server response for update:', error);
          // Si le code de statut est 204 (No Content) ou 200 (OK), c'est en fait un succès
          if (error.status === 204 || error.status === 200) {
            return of({} as TestRun); // Retourner un objet vide mais considéré comme un succès
          }
          throw error; // Propager l'erreur si c'est vraiment une erreur
        })
      );
  }

  // Method to get a list of testruns
  getTestRunList(): Observable<TestRun[]> {
    return this._http.get<TestRun[]>(`${this.apiUrl}/testrun/`);
  }

  // Method to get a specific testrun
  getTestRunById(id: string): Observable<TestRun> {
    return this._http.get<TestRun>(`${this.apiUrl}/testrun/${id}/`);
  }

  // Method to delete an testrun
  deleteTestRun(id: string): Observable<void> {
    return this._http
      .delete<void>(`${this.apiUrl}/testrun/${id}/`, { observe: 'response' })
      .pipe(
        catchError((error) => {
          // You can handle the error here or let it propagate, depending on your needs.
          //console.error('Error during deletion:', error);
          return of(error); // Return an observable with the error to continue the stream
        })
      );
  }
}
