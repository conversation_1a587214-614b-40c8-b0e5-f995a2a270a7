import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, of } from 'rxjs';
import { Risk } from '../models/risk';
import { catchError } from 'rxjs/operators'; // Add this import

@Injectable({
  providedIn: 'root',
})
export class RiskService {
  private apiUrl = environment.apiUrl;

  constructor(private _http: HttpClient) {}

  // Method to add a new risk
  addRisk(data: any): Observable<Risk> {
    return this._http.post<Risk>(`${this.apiUrl}/risk/`, data);
  }

  // Method to update an existing risk
  updateRisk(uuid: string, data: any): Observable<Risk> {
    return this._http.put<Risk>(`${this.apiUrl}/risk/${uuid}/`, data);
  }

  // Method to get a list of risks
  getRiskList(): Observable<Risk[]> {
    return this._http.get<Risk[]>(`${this.apiUrl}/risk/`);
  }

  // Method to get a specific risk
  getRiskById(uuid: string): Observable<Risk> {
    return this._http.get<Risk>(`${this.apiUrl}/risk/${uuid}/`);
  }

  // Method to delete an risk
  deleteRisk(uuid: string): Observable<void> {
    return this._http
      .delete<void>(`${this.apiUrl}/risk/${uuid}/`, {
        observe: 'response',
      })
      .pipe(
        catchError((error) => {
          // You can handle the error here or let it propagate, depending on your needs.
          //console.error('Error during deletion:', error);
          return of(error); // Return an observable with the error to continue the stream
        })
      );
  }
}
