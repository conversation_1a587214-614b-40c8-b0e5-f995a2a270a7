import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, of } from 'rxjs';
import { SutProvider } from '../models/sut-provider';
import { catchError } from 'rxjs/operators'; // Add this import

@Injectable({
  providedIn: 'root',
})
export class SutProviderService {
  private apiUrl = environment.apiUrl;

  constructor(private _http: HttpClient) {}

  // Method to add a new sut-provider
  addSutProvider(data: FormData): Observable<SutProvider> {
    return this._http.post<SutProvider>(`${this.apiUrl}/sut-provider/`, data);
  }

  // Method to update an existing sut-provider
  updateSutProvider(uuid: string, data: FormData): Observable<SutProvider> {
    return this._http.put<SutProvider>(
      `${this.apiUrl}/sut-provider/${uuid}/`,
      data
    );
  }

  // Method to get a list of sut-providers
  getSutProviderList(): Observable<SutProvider[]> {
    return this._http.get<SutProvider[]>(`${this.apiUrl}/sut-provider/`);
  }

  // Method to get a specific sut-provider
  getSutProviderById(id: number): Observable<SutProvider> {
    return this._http.get<SutProvider>(`${this.apiUrl}/sut-provider/${id}/`);
  }

  // Method to delete an sut-provider
  deleteSutProvider(id: number): Observable<void> {
    return this._http
      .delete<void>(`${this.apiUrl}/sut-provider/${id}/`, {
        observe: 'response',
      })
      .pipe(
        catchError((error) => {
          // You can handle the error here or let it propagate, depending on your needs.
          //console.error('Error during deletion:', error);
          return of(error); // Return an observable with the error to continue the stream
        })
      );
  }
}
