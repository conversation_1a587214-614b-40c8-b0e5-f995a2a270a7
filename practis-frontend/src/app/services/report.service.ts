import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators'; // Add this import

@Injectable({
  providedIn: 'root',
})
export class ReportService {
  private rulesReportUrl = environment.rulesReportUrl;

  constructor(private _http: HttpClient) {}

  // Method to add a new flow
  rulesEngagement(data: any): void {
    this._http
      .post(`${this.rulesReportUrl}`, data, { responseType: 'blob' })
      .subscribe(
        (response) => {
          const blob = new Blob([response], { type: response.type });
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = 'rulesEngagement.docx'; // Change the filename if needed
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          window.URL.revokeObjectURL(url);
        },
        (error) => {
          console.error('Download failed', error);
        }
      );
  }
}
