import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { TestExecution } from '../models/test-execution';

@Injectable({
  providedIn: 'root',
})
export class TestExecutionService {
  private apiUrl = environment.apiUrl;

  constructor(private _http: HttpClient) {}

  // Method to add a new test execution
  addTestExecution(data: TestExecution): Observable<TestExecution> {
    return this._http.post<TestExecution>(`${this.apiUrl}/testexecution/`, data)
      .pipe(
        catchError((error) => {
          console.log('Server response for creation:', error);
          if (error.status === 201) {
            return of(error.error || {} as TestExecution);
          }
          throw error;
        })
      );
  }

  // Method to update an existing test execution
  updateTestExecution(id: string, data: TestExecution): Observable<TestExecution> {
    return this._http.put<TestExecution>(`${this.apiUrl}/testexecution/${id}/`, data)
      .pipe(
        catchError((error) => {
          console.log('Server response for update:', error);
          if (error.status === 204 || error.status === 200) {
            return of({} as TestExecution);
          }
          throw error;
        })
      );
  }

  // Method to get a list of test executions
  getTestExecutionList(): Observable<TestExecution[]> {
    return this._http.get<TestExecution[]>(`${this.apiUrl}/testexecution/`);
  }

  // Method to get test executions for a specific test run
  getTestExecutionsForRun(runId: string): Observable<TestExecution[]> {
    return this._http.get<TestExecution[]>(`${this.apiUrl}/testexecution/?test_run=${runId}`);
  }

  // Method to get a specific test execution
  getTestExecutionById(id: string): Observable<TestExecution> {
    return this._http.get<TestExecution>(`${this.apiUrl}/testexecution/${id}/`);
  }

  // Method to delete a test execution
  deleteTestExecution(id: string): Observable<void> {
    return this._http
      .delete<void>(`${this.apiUrl}/testexecution/${id}/`, { observe: 'response' })
      .pipe(
        catchError((error) => {
          return of(error);
        })
      );
  }
}
