import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, of } from 'rxjs';
import { Sut } from '../models/sut';
import { catchError } from 'rxjs/operators'; // Add this import

@Injectable({
  providedIn: 'root',
})
export class SutService {
  private apiUrl = environment.apiUrl;

  constructor(private _http: HttpClient) {}

  // Method to add a new sut
  addSut(data: any): Observable<Sut> {
    return this._http.post<Sut>(`${this.apiUrl}/sut/`, data);
  }

  // Method to update an existing sut
  updateSut(uuid: string, data: any): Observable<Sut> {
    return this._http.put<Sut>(`${this.apiUrl}/sut/${uuid}/`, data);
  }

  // Method to get a list of suts
  getSutList(): Observable<Sut[]> {
    return this._http.get<Sut[]>(`${this.apiUrl}/sut/`);
  }

  // Method to get a specific sut
  getSutById(uuid: string): Observable<Sut> {
    return this._http.get<Sut>(`${this.apiUrl}/sut/${uuid}/`);
  }

  // Method to delete an sut
  deleteSut(uuid: string): Observable<void> {
    return this._http
      .delete<void>(`${this.apiUrl}/sut/${uuid}/`, { observe: 'response' })
      .pipe(
        catchError((error) => {
          // You can handle the error here or let it propagate, depending on your needs.
          //console.error('Error during deletion:', error);
          return of(error); // Return an observable with the error to continue the stream
        })
      );
  }
}
