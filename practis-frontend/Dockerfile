# Step 1: Build Angular app (node container)
FROM node:22.11.0 AS build

# Set the working directory to /app in the build container
WORKDIR /app

# Copy the package.json and package-lock.json files
COPY package.json package-lock.json ./

# Install the dependencies
RUN npm install --legacy-peer-deps

# Copy all the source code to the container
COPY . .

EXPOSE 4200
# Build the Angular project with production configuration

RUN npm run build --prod && chmod -R 777 dist/

CMD ["npm", "run", "start"]
