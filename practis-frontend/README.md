# PRACTIS Frontend

This frontend is designed using Angular v18.


## Recommended development setup

See [README.md](../README.md) in the project root to start the backend.

Install `pm2` and the project's dependencies (you also can use `pnpm`)

```bash
npm install pm2 -g
npm install @angular/cli -g
npm install
```

If you want to make developments to the frontend we recommend using

```bash
pm2-dev start "ng serve" practis-frontend
```

`pm2-dev` is a development tool that allows to start an application and auto-restart it on file change.

Otherwise, use the `start.sh` script that ends being

```bash
pm2 start "ng serve" --name practis-frontend
```