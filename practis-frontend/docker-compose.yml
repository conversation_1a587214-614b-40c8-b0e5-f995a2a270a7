version: '3.3'
services:
  practis-front:
    build:
      context: .
    image: practis-front:latest  # Make sure this is defined
    ports:
      - "4200:4200"
    container_name: practis-front
    volumes:
      - ./src:/usr/src/app/src
      - ./dist:/app/dist
    networks:
      - practis-network
    depends_on:
      - nginx

  nginx:
    image: nginx:latest
    container_name: nginx
    ports:
      - "8080:4200"
    volumes:
      - ./dist/cyrus_frontend/browser:/usr/share/nginx/html
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/nginx.conf:/etc/nginx/conf.d/default.conf
      
    command: ["nginx", "-g", "daemon off;"]
    networks:
      - practis-network

