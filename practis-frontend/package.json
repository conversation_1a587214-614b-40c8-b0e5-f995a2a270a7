{"name": "practis-frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --host 0.0.0.0 --port 4200 --disable-host-check --ssl", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "start:dev": "ng serve --configuration=development", "start:prod": "ng serve --configuration=production", "build:dev": "ng build --configuration=development", "build:prod": "ng build --configuration=production"}, "private": true, "dependencies": {"@angular/animations": "^18.2.0", "@angular/cdk": "^18.2.12", "@angular/common": "^18.2.0", "@angular/compiler": "^18.2.0", "@angular/core": "^18.2.0", "@angular/forms": "^18.2.0", "@angular/material": "^18.2.12", "@angular/platform-browser": "^18.2.0", "@angular/platform-browser-dynamic": "^18.2.0", "@angular/router": "^18.2.0", "@swimlane/ngx-charts": "^22.0.0-alpha.0", "apexcharts": "^3.54.1", "chart.js": "^4.4.9", "html2canvas": "^1.4.1", "jointjs": "^3.7.7", "keycloak-angular": "^16.1.0", "keycloak-js": "^26.0.6", "lodash-es": "^4.17.21", "ng-apexcharts": "^1.12.0", "ng-image-slider": "^10.0.0", "ngx-quill": "^26.0.10", "ngx-scrollbar": "^16.0.0", "pm2": "^5.4.3", "quill": "^2.0.3", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.11", "@angular/cli": "^18.2.11", "@angular/compiler-cli": "^18.2.0", "@types/jasmine": "~5.1.0", "@types/jointjs": "^1.0.5", "@types/lodash": "^4.17.13", "@types/quill": "^2.0.14", "jasmine-core": "~5.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.2"}}