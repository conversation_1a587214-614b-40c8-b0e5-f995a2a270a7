# vim: ft=yaml.ansible
# code: language=ansible

- name: Install stack
  hosts: all
  roles:
    - role: geerlingguy.docker
      become: true
      when: install_docker | bool
  tasks:
    - name: Install pip
      become: true
      ansible.builtin.apt:
        name: python3-pip
        update_cache: true

    - name: Install docker packages for pip
      ansible.builtin.pip:
        name:
          - docker
          - docker-compose

    - name: Create practis directory
      become: true
      ansible.builtin.file:
        path: "{{ install_path }}"
        state: directory
        owner: "{{ ansible_user_id }}"
        group: "{{ ansible_user_id }}"
        mode: "755"

    - name: Clone repo
      ansible.builtin.git:
        repo: "https://{{ git_user }}:{{ git_token }}@{{ practis_repo }}"
        dest: "{{ install_path }}"
        version: "{{ practis_branch }}"
        clone: true
        force: true
        update: true

    - name: Make sure directories exists
      ansible.builtin.file:
        path: "{{ install_path }}/{{ item.1 }}"
        state: directory
        mode: "750"
      with_subelements:
        - "{{ generated_secrets | dict2items | map(attribute='value') }}"
        - paths

    - name: "Create secret file "
      ansible.builtin.lineinfile:
        path: "{{ install_path }}/{{ item.1 }}/{{ item.0.name }}.txt"
        line: "{{ item.0.value }}"
        mode: "640"
        create: true
      with_subelements:
        - "{{ generated_secrets | dict2items | map(attribute='value') }}"
        - paths

    - name: Create API .env
      ansible.builtin.template:
        src: ansible/api.env.j2
        dest: "{{ install_path }}/practis-backend/practis-api/.env"
        mode: "640"

    - name: Create TCMS .env
      ansible.builtin.template:
        src: ansible/tcms.env.j2
        dest: "{{ install_path }}/practis-kiwi/kiwi/.env"
        mode: "640"

    - name: Create TCMS broker .env
      ansible.builtin.template:
        src: ansible/tcms_broker.env.j2
        dest: "{{ install_path }}/practis-kiwi/PractisKiwi/.env"
        mode: "640"

    - name: Create init script for postgres
      ansible.builtin.template:
        src: ansible/init-db.sh.j2
        dest: "{{ install_path }}/practis-backend/postgres/postgres-data/init-db.sh"
        mode: "755"

    - name: Create environment directory
      ansible.builtin.file:
        path: "{{ install_path }}/practis-frontend/src/environments"
        state: directory
        mode: "750"

    - name: Create frontend environment.ts
      ansible.builtin.template:
        src: ansible/environment.ts.j2
        dest: "{{ install_path }}/practis-frontend/src/environments/environment.ts"
        mode: "640"

    - name: Create frontend environment.ts
      ansible.builtin.template:
        src: ansible/environment.ts.j2
        dest: "{{ install_path }}/practis-frontend/src/environments/environment.development.ts"
        mode: "640"

    - name: Create ssl certificats directory
      ansible.builtin.file:
        path: "{{ install_path }}/practis-frontend/nginx/ssl"
        state: directory
        mode: "700"

    - name: Create private key
      community.crypto.openssl_privatekey:
        path: "{{ install_path }}/practis-frontend/nginx/ssl/server.key"

    - name: Create CSR
      community.crypto.openssl_csr:
        path: "{{ install_path }}/practis-frontend/nginx/ssl/server.csr"
        privatekey_path: "{{ install_path }}/practis-frontend/nginx/ssl/server.key"
        common_name: "10.129.1.65"
        organization_name: "CETIC"
        country_name: "BE"

    - name: Create self signed certificate
      community.crypto.x509_certificate:
        path: "{{ install_path }}/practis-frontend/nginx/ssl/server.crt"
        csr_path: "{{ install_path }}/practis-frontend/nginx/ssl/server.csr"
        privatekey_path: "{{ install_path }}/practis-frontend/nginx/ssl/server.key"
        provider: selfsigned

    - name: Start practis
      community.docker.docker_compose_v2:
        project_src: "{{ install_path }}"
        files: ["docker-compose-include.yml"]
        state: present
        build: always
        project_name: practis
