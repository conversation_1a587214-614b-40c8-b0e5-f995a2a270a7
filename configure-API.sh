echo 'DEBUG=0' > .env
#echo 'DEV_SECRET_KEY_FILE_=dev_secrets.txt' >> API/.env
echo 'PROD_SECRET_KEY_FILE=secrets/prod_django_secret_key.txt' >> .env
echo 'DB_HOST=postgres' >> .env
echo 'DB_PORT=5432' >> .env
echo 'DB_NAME_FILE=secrets/db_name.txt' >> .env
#echo 'DEV_DB_NAME=cyrusdev' >> API/.env
echo 'DB_USERNAME_FILE=secrets/db_username.txt' >> .env
echo 'DB_PASS_FILE=secrets/db_password.txt' >> .env
echo 'CLIENT_ID=secrets/sso_client_id.txt' >> .env
echo 'REALM_FILE=secrets/sso_realm.txt' >> .env
echo 'CLIENT_SECRET_FILE=secrets/sso_client_secret.txt' >> .env
#echo 'WELL_KNOWN_FILE=well-known.txt' >> API/.env
mkdir -p secrets
echo cyrus > secrets/db_name.txt
#echo cyrusdev > secrets/dev_db_name.txt
#pgadmin_pwd.txt
#touch API/secrets/sso_client_secret.txt #copy paste manually
touch secrets/sso_client_secret.txt #copy paste manually
echo manually configure secrets/sso_client_secret.txt
#touch API/secrets/db_password.txt #copy paste manually
touch secrets/db_password.txt #copy paste manually
echo manually configure secrets/db_password.txt
#dev_db_password.txt
#touch API/secrets/prod_django_secret_key.txt #copy paste manually
touch secrets/prod_django_secret_key.txt #copy paste manually
echo manually configure secrets/prod_django_secret_key.txt
echo cyrus > secrets/sso_realm.txt
echo postgres > secrets/db_username.txt
#dev_django_secret_key.txt
echo cyrus > secrets/sso_client_id.txt
#touch API/secrets/sso_well_known.txt #copy paste manually
touch cyrus-deploy/secrets/sso_well_known.txt #copy paste manually
echo manually configure cyrus-deploy/secrets/sso_well_known.txt
