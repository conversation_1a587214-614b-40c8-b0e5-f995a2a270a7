variables:
  GIT_CLEAN_FLAGS: -ffdx

stages:
  - build
  - test
  - deploy

build:
  stage: build
  tags:
    - docker
    - devsecops1
  before_script:
    - rm -rf practis-frontend/dist/cyrus_frontend/browser || true
  script:
    - echo "Starting generating secrets for local test ... "
    - echo "********* Secrets and env for practis backend *********"
    - echo "creating..."
    - echo $(ls)
    - mkdir -p practis-backend/secrets
    - echo $KC_CLIENT_ID>practis-backend/secrets/api_kc_client_id.txt
    - echo $KC_CLIENT_SECRET>practis-backend/secrets/api_kc_client_secret.txt
    - echo $KC_REALM_NAME>practis-backend/secrets/api_kc_realm_name.txt
    - echo $KC_SERVER_URL>practis-backend/secrets/api_kc_server_url.txt
    - echo $KC_WELL_KNOWN>practis-backend/secrets/api_kc_well_known.txt
    - echo $KW_BROKER>practis-backend/secrets/api_kw_broker.txt
    - echo $API_PROD_DB>practis-backend/secrets/practis_api_prod_db.txt
    - echo $API_DEV_DB>practis-backend/secrets/practis_api_dev_db.txt
    - echo $API_DEV_KEY>practis-backend/secrets/practis_api_dev_key.txt
    - echo $API_PROD_KEY>practis-backend/secrets/practis_api_prod_key.txt
    - echo $KW_DB_NAME>practis-backend/secrets/db_kiwi_name.txt
    - echo $KW_DB_PASSWORD>practis-backend/secrets/db_kiwi_password.txt
    - echo $KW_DB_USERNAME>practis-backend/secrets/db_kiwi_username.txt
    - echo $OPENVAS_BROKER>practis-backend/secrets/openvas_broker.txt
    - echo "pg_pass">practis-backend/secrets/pg_admin_pwd.txt
    - echo "pg_pass">practis-backend/secrets/pgadmin_pwd.txt
    - echo $POSTGRES_PASSWORD>practis-backend/secrets/postgres_password.txt
    - echo $POSTGRES_USERNAME>practis-backend/secrets/postgres_username.txt
    - echo DB_HOST=$DB_HOST>practis-backend/practis-api/.env
    - echo DB_PORT=$DB_PORT>>practis-backend/practis-api/.env
    - echo API_PORT=$API_PORT>>practis-backend/practis-api/.env
    - echo DEBUG=$DEBUG>>practis-backend/practis-api/.env
    - openssl req -x509 -newkey rsa:4096 -keyout practis-backend/nginx/ssl/key.pem -out practis-backend/nginx/ssl/cert.pem -days 365 -nodes -subj "/C=BE/ST=Brussels/L=Brussels/O=CETIC/OU=IT Department/CN=localhost"
    - echo "files created for backend ✅"
    - echo "********* Secrets for practis kiwi *********"
    - echo "creating ..."
    - mkdir -p practis-kiwi/PractisKiwi/secrets
    - echo $DB_KIWI_PASSWORD>practis-kiwi/PractisKiwi/secrets/kiwi_db_password_init.txt
    - echo $KW_ADMIN_USERNAME>practis-kiwi/PractisKiwi/secrets/kiwi_admin_username.txt
    - echo $KW_API_SERVER>practis-kiwi/PractisKiwi/secrets/kiwi_api_kiwi_server.txt
    - echo $KIWI_API_SECRET_KEY>practis-kiwi/PractisKiwi/secrets/kiwi_api_secret_key.txt
    - echo DEBUG=$DEBUG>practis-kiwi/PractisKiwi/.env
    - echo TCMS_API_PORT_LOCAL=$KW_API_PORT>>practis-kiwi/PractisKiwi/.env
    - echo TCMS_API_PORT_CONTAINER=$KW_API_PORT>>practis-kiwi/PractisKiwi/.env
    - mkdir -p practis-kiwi/kiwi/secrets
    - echo $KIWI_ADMIN_PASSWORD>practis-kiwi/kiwi/secrets/kiwi_admin_password.txt
    - echo $KIWI_ADMIN_USERNAME>practis-kiwi/kiwi/secrets/kiwi_admin_username.txt
    - echo $KW_DB_NAME>practis-kiwi/kiwi/secrets/kiwi_db_name.txt
    - echo $KW_DB_PASSWORD>practis-kiwi/kiwi/secrets/kiwi_db_password.txt
    - echo $DB_KIWI_PASSWORD>practis-kiwi/kiwi/secrets/kiwi_db_password_init.txt
    - echo $KW_DB_USERNAME>practis-kiwi/kiwi/secrets/kiwi_db_username.txt
    - echo KIWI_DB_HOST=$KIWI_DB_HOST>practis-kiwi/kiwi/.env
    - echo KIWI_DB_PORT=$KIWI_DB_PORT>>practis-kiwi/kiwi/.env
    - echo KIWI_WEB_PORT_LOCAL_1=$KIWI_WEB_PORT_LOCAL_1>>practis-kiwi/kiwi/.env
    - echo KIWI_WEB_PORT_CONTAINER_1=$KIWI_WEB_PORT_CONTAINER_1>>practis-kiwi/kiwi/.env
    - echo KIWI_WEB_PORT_LOCAL_2=$KIWI_WEB_PORT_LOCAL_2>>practis-kiwi/kiwi/.env
    - echo KIWI_WEB_PORT_CONTAINER_2=$KIWI_WEB_PORT_CONTAINER_2>>practis-kiwi/kiwi/.env
    - echo PROXY_PORT_LOCAL=$PROXY_PORT_LOCAL>>practis-kiwi/kiwi/.env
    - echo PROXY_PORT_CONTAINER=$PROXY_PORT_CONTAINER>>practis-kiwi/kiwi/.env
    - echo "secrets files created for kiwi ✅"
    - echo "********* Secrets and environment for practis frontend *********"
    - echo "Generating certificates ..."
    - mkdir -p practis-frontend/nginx/ssl
    - openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout practis-frontend/nginx/ssl/server.key -out practis-frontend/nginx/ssl/server.crt -subj "/C=US/ST=State/L=City/O=Organization/OU=Department/CN=************"
    - echo "creating environment file ..."
    - mkdir -p practis-frontend/src/environments
    # - echo "export const environment = {">practis-frontend/src/environments/environment.ts
    # - echo -e "\n  production:true,">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  apiUrl:'/api',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  imagePrefixUrl:'/images',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  noderedUrl:'http://127.0.0.1:1880/',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  gvmUrl:'http://127.0.0.1:9392/',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  gvmInstance:'10.11.250.130',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  kiwiUrl:'https://************/',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  rulesReportUrl:'http://10.134.2.223:8000/api/process-template',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  importUrl:'$OPENVAS_IMPORT_URL',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  keycloakUrl:'$KC_SERVER_URL',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  keycloakRealm:'$KC_REALM_NAME',">>practis-frontend/src/environments/environment.ts   - echo "  keycloakClientId:'cyrus2',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n};">>practis-frontend/src/environments/environment.ts
    - cp practis-frontend/src/environments/environment.ts practis-frontend/src/environments/environment.development.ts
    - echo $(cat practis-frontend/src/environments/environment.development.ts) 
    - sed -i "s/production:true/production:false/g" practis-frontend/src/environments/environment.development.ts
    - echo "environment files created for frontend ✅"
    - DOCKER_BUILDKIT=0 docker-compose -f docker-compose-include.yml build
  rules:
    - changes: 
        - practis-backend/**/*
        - practis-kiwi/**/*
        - practis-frontend/**/*
        - .gitlab-ci.yml
    - if: '$CI_COMMIT_BRANCH == "main"'
  
test:
  stage: test
  tags:
    - docker
    - devsecops1
  script:
    - echo "Starting generating secrets for local test ... "
    - echo "********* Secrets and env for practis backend *********"
    - echo "creating..."
    - echo $(ls)
    - mkdir -p practis-backend/secrets
    - echo $KC_CLIENT_ID>practis-backend/secrets/api_kc_client_id.txt
    - echo $KC_CLIENT_SECRET>practis-backend/secrets/api_kc_client_secret.txt
    - echo $KC_REALM_NAME>practis-backend/secrets/api_kc_realm_name.txt
    - echo $KC_SERVER_URL>practis-backend/secrets/api_kc_server_url.txt
    - echo $KC_WELL_KNOWN>practis-backend/secrets/api_kc_well_known.txt
    - echo $KW_BROKER>practis-backend/secrets/api_kw_broker.txt
    - echo $API_PROD_DB>practis-backend/secrets/practis_api_prod_db.txt
    - echo $API_DEV_DB>practis-backend/secrets/practis_api_dev_db.txt
    - echo $API_DEV_KEY>practis-backend/secrets/practis_api_dev_key.txt
    - echo $API_PROD_KEY>practis-backend/secrets/practis_api_prod_key.txt
    - echo $KW_DB_NAME>practis-backend/secrets/db_kiwi_name.txt
    - echo $KW_DB_PASSWORD>practis-backend/secrets/db_kiwi_password.txt
    - echo $KW_DB_USERNAME>practis-backend/secrets/db_kiwi_username.txt
    - echo $OPENVAS_BROKER>practis-backend/secrets/openvas_broker.txt
    - echo "pg_pass">practis-backend/secrets/pg_admin_pwd.txt
    - echo "pg_pass">practis-backend/secrets/pgadmin_pwd.txt
    - echo $POSTGRES_PASSWORD>practis-backend/secrets/postgres_password.txt
    - echo $POSTGRES_USERNAME>practis-backend/secrets/postgres_username.txt
    - echo DB_HOST=$DB_HOST>practis-backend/practis-api/.env
    - echo DB_PORT=$DB_PORT>>practis-backend/practis-api/.env
    - echo API_PORT=$API_PORT>>practis-backend/practis-api/.env
    - echo DEBUG=$DEBUG>>practis-backend/practis-api/.env
    - openssl req -x509 -newkey rsa:4096 -keyout practis-backend/nginx/ssl/key.pem -out practis-backend/nginx/ssl/cert.pem -days 365 -nodes -subj "/C=BE/ST=Brussels/L=Brussels/O=CETIC/OU=IT Department/CN=localhost"
    - echo "files created for backend ✅"
    - echo "********* Secrets for practis kiwi *********"
    - echo "creating ..."
    - mkdir -p practis-kiwi/PractisKiwi/secrets
    - echo $DB_KIWI_PASSWORD>practis-kiwi/PractisKiwi/secrets/kiwi_db_password_init.txt
    - echo $KW_ADMIN_USERNAME>practis-kiwi/PractisKiwi/secrets/kiwi_admin_username.txt
    - echo $KIWI_ADMIN_PASSWORD>practis-kiwi/PractisKiwi/secrets/kiwi_admin_password.txt
    - echo $KW_API_SERVER>practis-kiwi/PractisKiwi/secrets/kiwi_api_kiwi_server.txt
    - echo $KIWI_API_SECRET_KEY>practis-kiwi/PractisKiwi/secrets/kiwi_api_secret_key.txt
    - echo DEBUG=$DEBUG>practis-kiwi/PractisKiwi/.env
    - echo TCMS_API_PORT_LOCAL=$KW_API_PORT>>practis-kiwi/PractisKiwi/.env
    - echo TCMS_API_PORT_CONTAINER=$KW_API_PORT>>practis-kiwi/PractisKiwi/.env
    - mkdir -p practis-kiwi/kiwi/secrets
    - echo $KIWI_ADMIN_PASSWORD>practis-kiwi/kiwi/secrets/kiwi_admin_password.txt
    - echo $KIWI_ADMIN_USERNAME>practis-kiwi/kiwi/secrets/kiwi_admin_username.txt
    - echo $KW_DB_NAME>practis-kiwi/kiwi/secrets/kiwi_db_name.txt
    - echo $KW_DB_PASSWORD>practis-kiwi/kiwi/secrets/kiwi_db_password.txt
    - echo $DB_KIWI_PASSWORD>practis-kiwi/kiwi/secrets/kiwi_db_password_init.txt
    - echo $KW_DB_USERNAME>practis-kiwi/kiwi/secrets/kiwi_db_username.txt
    - echo KIWI_DB_HOST=$KIWI_DB_HOST>practis-kiwi/kiwi/.env
    - echo KIWI_DB_PORT=$KIWI_DB_PORT>>practis-kiwi/kiwi/.env
    - echo KIWI_WEB_PORT_LOCAL_1=$KIWI_WEB_PORT_LOCAL_1>>practis-kiwi/kiwi/.env
    - echo KIWI_WEB_PORT_CONTAINER_1=$KIWI_WEB_PORT_CONTAINER_1>>practis-kiwi/kiwi/.env
    - echo KIWI_WEB_PORT_LOCAL_2=$KIWI_WEB_PORT_LOCAL_2>>practis-kiwi/kiwi/.env
    - echo KIWI_WEB_PORT_CONTAINER_2=$KIWI_WEB_PORT_CONTAINER_2>>practis-kiwi/kiwi/.env
    - echo PROXY_PORT_LOCAL=$PROXY_PORT_LOCAL>>practis-kiwi/kiwi/.env
    - echo PROXY_PORT_CONTAINER=$PROXY_PORT_CONTAINER>>practis-kiwi/kiwi/.env
    - echo "secrets files created for kiwi ✅"
    - echo "********* Secrets and environment for practis frontend *********"
    - echo "Generating certificates ..."
    - mkdir -p practis-frontend/nginx/ssl
    - openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout practis-frontend/nginx/ssl/server.key -out practis-frontend/nginx/ssl/server.crt -subj "/C=US/ST=State/L=City/O=Organization/OU=Department/CN=************"
    - echo "creating environment file ..."
    - mkdir -p practis-frontend/src/environments
    # - echo "export const environment = {">practis-frontend/src/environments/environment.ts
    # - echo -e "\n  production:true,">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  apiUrl:'/api',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  imagePrefixUrl:'/images',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  noderedUrl:'http://127.0.0.1:1880/',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  gvmUrl:'http://127.0.0.1:9392/',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  gvmInstance:'10.11.250.130',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  kiwiUrl:'https://************/',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  rulesReportUrl:'http://10.134.2.223:8000/api/process-template',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  importUrl:'$OPENVAS_IMPORT_URL',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  keycloakUrl:'$KC_SERVER_URL',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n  keycloakRealm:'$KC_REALM_NAME',">>practis-frontend/src/environments/environment.ts   - echo "  keycloakClientId:'cyrus2',">>practis-frontend/src/environments/environment.ts
    # - echo -e "\n};">>practis-frontend/src/environments/environment.ts
    - cp practis-frontend/src/environments/environment.ts practis-frontend/src/environments/environment.development.ts
    - echo $(cat practis-frontend/src/environments/environment.development.ts) 
    - sed -i "s/production:true/production:false/g" practis-frontend/src/environments/environment.development.ts
    - echo "environment files created for frontend ✅"
    # - DOCKER_BUILDKIT=0 docker-compose -f docker-compose-include.yml build
    - docker-compose -f docker-compose-include.yml down
    - docker ps -aq | xargs -r docker rm -f
    # - docker stop 4b07ae584acedda1533b236a0be5cb595aebfd848ebc3560eddf2e5dd4b7026a
    - docker-compose -f docker-compose-include.yml up -d --remove-orphans
    - sleep 30
    - curl -v http://127.0.0.1:8000/api/sut-provider/
    - sleep 7
    - docker-compose -f docker-compose-include.yml down
  rules:
    - changes: 
        - practis-backend/**/*
        - practis-kiwi/**/*
        - practis-frontend/**/*
        - .gitlab-ci.yml
    - if: '$CI_COMMIT_BRANCH == "main"'

deploy:
  stage: deploy
  tags:
    - devsecops1
    - docker
  before_script:
    - mkdir -p ~/.ssh
    - echo -----BEGIN OPENSSH PRIVATE KEY----- > ~/.ssh/practis224pvtkey
    - echo $practis224pvtkey >> ~/.ssh/practis224pvtkey
    - echo -----END OPENSSH PRIVATE KEY----- >> ~/.ssh/practis224pvtkey
    - chmod 600 ~/.ssh/practis224pvtkey
    - echo ************ >> ~/.ssh/known_hosts
    - chmod 600 ~/.ssh/known_hosts
    - echo Host ************ > ~/.ssh/config
    - echo   Hostname ************ >> ~/.ssh/config
    - echo   User crs-admin >> ~/.ssh/config
    - echo "StrictHostKeyChecking no" >> ~/.ssh/config
  script:
    - echo "Build Practis platform containers"
    - |
      if test -d "practis-all/.git"; then
        echo "Repository already exists. Performing git pull..."
        cd practis-all
        git pull
      else
        rm -rf practis-all
        echo "Repository does not exist. Cloning repository..."
        git credential-cache exit
        git clone https://gitlab-ci-token:${CI_JOB_TOKEN}@git.cetic.be/practis/platform/practis-all.git
        cd practis-all
      fi
    - |
      if ssh -i ~/.ssh/practis224pvtkey ************ "test -f practis-all/docker-compose.yml"; then
        echo "docker-compose.yml file exists. Performing docker-compose down..."
        ssh -i ~/.ssh/practis224pvtkey ************ "cd test && docker-compose down"
      else
        echo "docker-compose.yml file does not exist. ignoring the docker-compose down command..."
      fi
    - |
    - ssh -i ~/.ssh/crescendo-dev ************ "docker-compose -f practis-all/docker-compose-include.yml build && docker-compose -f docker-compose-include.yml up -d --remove-orphans"
  rules:
    - changes: 
        - practis-backend/**/*
        - practis-kiwi/**/*
        - practis-frontend/**/*
        - .gitlab-ci.yml
    - if: '$CI_COMMIT_BRANCH == "main"'
