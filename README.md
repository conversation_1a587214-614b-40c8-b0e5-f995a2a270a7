# Practis-all

This repository joins all the parts of the Practis project

## Getting started

To run the project, you have figure each part of it:

- Practis Backend
- Practis Frontend
- Practis Kiwi

### 1. Clone the Git Repository

First, generate a personal access token (referred to as `TOKEN_GENERATED`) and replace it in the command below:

```bash
git clone https://username:<EMAIL>/practis/platform/practis-all.git
cd practis-all
```

### 2. Set up the environment

In order to run the project, you need to configure your environments including credentials and environment variables required by the concerned services.

To set up your environment, execute the file [environment.sh](./environment.sh). This will create all your required environment variables and secrets files at the right place.

> source ./environment.sh

A Windows version does exist, named `environment.bat`.

(the 2 files file are available in a shared drive, `EQP-MBEDIS/Assets/PRACTIS/dev_tools`)

## Run the app

to access the app: run the command below:

> source ./start.sh

For windows, execute the file `start.bat`

Once all your services have started you can access the backend.

To run the frontend see the [frontend setup guide](./practis-frontend/README.md) file, afterwards you can access the frontend at 

http://localhost:4200

if everything is okay, you should land on the authentication page below.

![Practis login page](./practis-backend/images/practis_login.png)

Use your credentials provided by an Admin to log in. If you don't have an account yet, contact @ggi or @mbu.

Once authenticated, you will have the view below

![Practis homepage](./practis-backend/images/practis_home.png)

you can now enjoy the platform! 🚀
