version: '3.9'  
services:

  keycloak:
    image: keycloak/keycloak:26.1.1
    entrypoint:
      - /opt/keycloak/bin/kc.sh
      - start
      - --verbose
      - "--https-certificate-file=/opt/keycloak/keycloak.crt"
      - "--https-certificate-key-file=/opt/keycloak/keycloak.key"
    depends_on:
      keycloakdb:
        condition: service_healthy
    env_file:
      - ./.env
    environment:
      KEYCLOAK_HOSTNAME: ${KEYCLOAK_HOSTNAME}
      KEYCLOAK_DB_USER: keycloak
      KEYCLOAK_DB_DATABASE: keycloak
      KEYCLOAK_DB_PASSWORD: /run/secrets/postgres_password
      KEYCLOAK_ADMIN: ${KE<PERSON>CLOAK_ADMIN_USERNAME}
      KEYCLOAK_ADMIN_PASSWORD: /run/secrets/kc_admin_password
    volumes:
      - keycloak:/opt/keycloak/data/
      - ./ssl/keycloak.crt:/opt/keycloak/keycloak.crt
      - ./ssl/keycloak.key:/opt/keycloak/keycloak.key
    secrets:
      - postgres_password
      - kc_admin_password
    ports:
      - 8080:8080
      - 8443:8443
    restart: always
    networks:
      - keycloak_network


  keycloakdb:
    image: postgres:16
    container_name: keycloakdb
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: keycloak
      POSTGRES_USER: keycloak
      POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password
    secrets:
      - postgres_password
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U keycloak"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: always
    ports:
      - 5432:5432
    networks:
      - keycloak_network

networks:
  keycloak_network:
    driver: bridge

volumes:  
  keycloak:
  postgres_data:

secrets:
  postgres_password:
    file: ./secrets/postgres_password.txt
  kc_admin_password:  
    file: ./secrets/kc_admin_password.txt