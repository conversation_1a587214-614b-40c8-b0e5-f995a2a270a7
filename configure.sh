#docker exec cyrus-postgres-1 bash -c "psql -U postgres -c 'CREATE DATABASE keycloak;'"
#docker exec cyrus-postgres-1 bash -c "psql -U postgres -c 'CREATE DATABASE cyrus;'"
#docker exec cyrus-postgres-1 bash -c "psql -U postgres -c 'CREATE DATABASE jasper;'"
#docker exec cyrus-postgres-1 bash -c "psql -U postgres -c 'CREATE DATABASE kiwi;'"
docker exec cyrus-drupal-1 bash -c "composer require 'drupal/charts:^5.0'"
docker exec cyrus-drupal-1 bash -c "composer require 'drupal/charts:^5.0'"
docker exec cyrus-drupal-1 bash -c "composer require 'drupal/asset_injector:^2.20'"
docker exec cyrus-drupal-1 bash -c "composer require 'drupal/inline_entity_form:^3.0@RC'"
docker exec cyrus-drupal-1 bash -c "composer require 'drupal/autologout:^1.5'"
docker exec cyrus-drupal-1 bash -c "composer require 'drupal/keycloak:^1.8'"
docker exec cyrus-drupal-1 bash -c "composer require 'drupal/openid_connect:^1.4'"
docker exec cyrus-drupal-1 bash -c "composer require 'drupal/login_redirect_per_role:^1.9'"
docker exec cyrus-drupal-1 bash -c "composer require 'drupal/url_restriction_by_role:^1.1'"
docker exec cyrus-drupal-1 bash -c "composer require 'drupal/js_cookie:^1.0'"
docker exec cyrus-drupal-1 bash -c "composer require 'drupal/backup_migrate:^5.0'"
docker exec cyrus-drupal-1 bash -c "composer require --dev drush/drush"
docker cp key/10.134.2.197.crt cyrus-drupal-1:/usr/local/share/ca-certificates/
docker exec cyrus-drupal-1 bash -c "update-ca-certificates"
docker exec cyrus-drupal-1 bash -c "chown -R www-data:www-data /opt/drupal/web/sites/default/files"
