# vim: ft=yaml.ansible
# code: language=ansible

- name: Generate secrets
  hosts: localhost
  connection: local
  tasks:
    - name: "Create secret file"
      ansible.builtin.lineinfile:
        create: true
        path: "generated_secrets.yml"
        line: "generated_secrets:"
        mode: "600"

    - name: "Generate secrets"
      ansible.builtin.include_tasks:
        file: ansible/generate_secrets.yml
      loop: "{{ secrets | dict2items }}"
