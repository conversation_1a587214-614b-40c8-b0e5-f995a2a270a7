git_user: ""
git_token: ""
install_docker: false
practis_repo: "git.cetic.be/practis/platform/practis-all.git"
practis_branch: "main"
install_path: "/opt/practis"
kiwi_db_password: "secret12345"
practis_frontend_prod: false
secrets:
  postgres_username:
    value: practis
    paths:
      - practis-backend/secrets
  postgres_password:
    value: null
    length: 20
    chars:
      - ascii_letters
      - digits
    paths:
      - practis-backend/secrets
  practis_api_dev_db:
    value: practis
    paths:
      - practis-backend/secrets
  practis_api_prod_db:
    value: practis
    paths:
      - practis-backend/secrets
  pgadmin_pwd:
    value: null
    paths:
      - practis-backend/secrets
  practis_api_dev_key:
    value: null
    paths:
      - practis-backend/secrets
  practis_api_prod_key:
    value: null
    paths:
      - practis-backend/secrets
  api_kc_realm_name:
    value: practis
    paths:
      - practis-backend/secrets
  api_kc_client_id:
    value: practis
    paths:
      - practis-backend/secrets
  api_kc_client_secret:
    value: ""
    paths:
      - practis-backend/secrets
  api_kc_well_known:
    value: https://sso.apps.cyber-factory-core.cetic.be/realms/practis/.well-known/openid-configuration
    paths:
      - practis-backend/secrets
  api_kc_server_url:
    value: https://sso.apps.cyber-factory-core.cetic.be
    paths:
      - practis-backend/secrets
  api_kw_broker:
    value: http://tcms-api:8080
    paths:
      - practis-backend/secrets
  openvas_broker:
    value: "https://10.134.2.196:8000"
    paths:
      - practis-backend/secrets
  kiwi_name:
    value: kiwi
    paths:
      - practis-backend/secrets
  kiwi_db_name: # duplicate with db_kiwi_name
    value: kiwi
    paths:
      - practis-kiwi/kiwi/secrets
  kiwi_db_username: # duplicate with db_kiwi_username
    value: kiwi
    paths:
      - practis-kiwi/kiwi/secrets
  kiwi_db_password_init: # duplicate with db_kiwi_password
    value: "{{ kiwi_db_password }}"
    length: 20
    chars:
      - ascii_letters
      - digits
    paths:
      - practis-kiwi/kiwi/secrets
  db_kiwi_name:
    value: "kiwi"
    paths:
      - practis-backend/secrets
  db_kiwi_username:
    value: "kiwi"
    paths:
      - practis-backend/secrets
  db_kiwi_password:
    value: "{{ kiwi_db_password }}"
    length: 20
    chars:
      - ascii_letters
      - digits
    paths:
      - practis-backend/secrets
  kiwi_admin_username:
    value: kiwi_admin
    paths:
      - practis-kiwi/PractisKiwi/secrets
      - practis-kiwi/kiwi/secrets
  kiwi_admin_password:
    value: kiwi_admin
    paths:
      - practis-kiwi/PractisKiwi/secrets
      - practis-kiwi/kiwi/secrets
  kiwi_api_kiwi_server:
    value: https://web:8443/xml-rpc/
    paths:
      - practis-kiwi/PractisKiwi/secrets
  kiwi_api_secret_key:
    value: null
    paths:
      - practis-kiwi/PractisKiwi/secrets
  kiwi_broker_django_secrets:
    value: null
  postgres_host:
    value: postgres
  postgres_port:
    value: 5432
  practis_api_host:
    value: "practis-api"
  practis_api_port:
    value: 8000
  practis_debug_mode:
    value: 1
  nodered_url:
    value: ""
  gvm_url:
    value: ""
  kiwi_api_local_port_1:
    value: 8081
  kiwi_api_container_port_1:
    value: 8081
  kiwi_web_local_port_1:
    value: 80
  kiwi_web_container_port_1:
    value: 8080
  kiwi_web_local_port_2:
    value: 443
  kiwi_web_container_port_2:
    value: 8443
  proxy_port_local_port:
    value: 4343
  proxy_port_container_port:
    value: 8443
  frontend_host:
    value: null
