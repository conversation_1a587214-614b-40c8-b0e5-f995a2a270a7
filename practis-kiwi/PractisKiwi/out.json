[{'id': 1, 'create_date': <DateTime '20240524T14:11:49' at 0xffffb334c990>, 'is_automated': False, 'script': '', 'arguments': '', 'extra_link': None, 'summary': 'test case', 'requirement': None, 'notes': '', 'text': '**Scenario**: ... what behavior will be tested ...\n  **Given** ... conditions ...\n  **When** ... actions ...\n  **Then** ... expected results ...', 'case_status': 1, 'case_status__name': 'PROPOSED', 'category': 1, 'category__name': '--default--', 'priority': 1, 'priority__value': 'P1', 'author': 2, 'author__username': 'admin', 'default_tester': None, 'default_tester__username': None, 'reviewer': None, 'reviewer__username': None, 'setup_duration': 0.0, 'testing_duration': 0.0, 'expected_duration': 0.0}, {'id': 2, 'create_date': <DateTime '20240527T09:05:05' at 0xffffb31d4190>, 'is_automated': False, 'script': '', 'arguments': '', 'extra_link': None, 'summary': 'test acse cyrus', 'requirement': None, 'notes': '', 'text': '**Scenario**: ... what behavior will be tested ...\n  **Given** ... conditions ...\n  **When** ... actions ...\n  **Then** ... expected results ...', 'case_status': 1, 'case_status__name': 'PROPOSED', 'category': 1, 'category__name': '--default--', 'priority': 1, 'priority__value': 'P1', 'author': 2, 'author__username': 'admin', 'default_tester': None, 'default_tester__username': None, 'reviewer': None, 'reviewer__username': None, 'setup_duration': 0.0, 'testing_duration': 0.0, 'expected_duration': 0.0}]