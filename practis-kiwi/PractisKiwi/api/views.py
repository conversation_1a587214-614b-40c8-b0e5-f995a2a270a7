from .models import *
from .serializers import *
from rest_framework import viewsets
from rest_framework.response import Response
from tcms_api import TCMS
from django.conf import settings
from .responses import (
    server_response,
    auth_reponse,
    not_found_response,
    process_response,
    ok_response,
    deleted_response,
    log_response,
)
import ssl
import ast
import re
import base64

ssl._create_default_https_context = ssl._create_unverified_context


"""
Pour chaque vue, on a une variable fields, qui est un dictionnaire contenant les champs à retourner
Le dictionnaire est de la forme

fields = {
    "field1_kiwi": "field1_practis",
    "field2_kiwi": "field2_practis",
    "field3_kiwi": "field3_practis",
    ...
}

la clé représente le nom du champ dans l'API de Kiwi, et la valeur représente le nom du champ dans la base de données de Cyrus

"""


def fix_quotes(string):
    result = ""

    escaping = False
    state = "waiting_name"

    for c in string:
        if c == "'":
            if state == "waiting_name":
                state = "parsing_name"
                result = result + '"'
            elif state == "parsing_name":
                state = "name_parsed"
                result = result + '"'
            elif state == "waiting_value":
                state = "parsing_value"
                result = result + '"'
            elif state == "parsing_value":
                if escaping:
                    escaping = False
                    result = result + "'"
                else:
                    state = "waiting_name"
                    result = result + '"'
            else:
                result = result + c
        elif c == '"':
            if state == "waiting_value":
                state = "parsing_value_2"
                result = result + '"'
            elif state == "parsing_value_2":
                state = "waiting_name"
                result = result + '"'
            elif state == "parsing_value":
                result = result + '\\"'
        elif c == ":":
            if state == "name_parsed":
                state = "waiting_value"

            result = result + c
        elif c == "," or c == "}" or c == "]":
            if state == "waiting_value":
                state = "waiting_name"

            result = result + c
        elif c == "\\":
            if escaping:
                escaping = False
            else:
                escaping = True

            result = result + c
        else:
            result = result + c

    return result


def json_response(response):
    date_pattern = r"<DateTime \'(.+?)\' at 0x.*?>"
    json_string = re.sub(date_pattern, r"'\1',", str(response))
    json_string = re.sub(r",,", ",", json_string)

    return ast.literal_eval(fix_quotes(json_string))


def call_kiwi(getter):
    try:
        rpc = TCMS(settings.KIWI_SERVER, settings.KIWI_USER, settings.KIWI_PASS).exec
    except:
        return auth_reponse

    try:
        kiwi_response = getter(rpc)
        return ok_response(json_response(kiwi_response))

    except:
        return process_response


class ProductView(viewsets.ViewSet):

    queryset = Product.objects.all()
    serializer_class = ProductSerializer
    serializer = serializer_class(queryset, many=True)

    fields = {
        "name": "name",
        "description": "description",
    }

    def list(self, request):
        try:
            response = call_kiwi(lambda rpc: rpc.Product.filter({}))
            return response
        except:
            return server_response

    def retrieve(self, request, pk=None):
        try:
            response = call_kiwi(lambda rpc: rpc.Product.filter({"id": pk}))
            return response
        except:
            return server_response

    def create(self, request):
        data = request.data

        name = data.get("name")
        description = data.get("description")
        try:
            resp_class = call_kiwi(
                lambda rpc: rpc.Classification.filter({"name": "sut"})
            )
            classification = resp_class.data[0].get("id")
        except:
            resp_class = call_kiwi(
                lambda rpc: rpc.Classification.create({"name": "sut"})
            )
            classification = resp_class.data.get("id")
        try:
            response = call_kiwi(
                lambda rpc: rpc.Product.create(
                    {
                        "name": name,
                        "description": description,
                        "classification": classification,
                    }
                )
            )
            return response
        except:
            return server_response


class CategoryView(viewsets.ViewSet):

    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    serializer = serializer_class(queryset, many=True)

    def list(self, request):

        try:
            response = call_kiwi(lambda rpc: rpc.Category.filter({}))
            return response
        except:
            return server_response


class VersionView(viewsets.ViewSet):

    queryset = Version.objects.all()
    serializer_class = VersionSerializer
    serializer = serializer_class(queryset, many=True)

    def list(self, request):

        try:
            response = call_kiwi(lambda rpc: rpc.TestExecution.get_comments(7))
            return response
        except:
            return server_response


class TestCaseView(viewsets.ViewSet):

    queryset = TestCase.objects.all()
    serializer_class = TestCaseSerializer
    serializer = serializer_class(queryset, many=True)

    def list(self, request):

        try:
            response = call_kiwi(lambda rpc: rpc.TestCase.filter({}))
            return response
        except:
            return server_response

    def retrieve(self, request, pk=None):
        try:
            response = call_kiwi(lambda rpc: rpc.TestCase.filter({"id": pk}))
            return response
        except:
            return server_response

    def create(self, request):
        data = request.data
        summary = data.get("name")
        text = data.get("description")
        script = data.get("attack_technic")
        subject = data.get("subject", "None")

        try:
            id_class = (
                call_kiwi(lambda rpc: rpc.Classification.filter({"name": subject}))
                .data[0]
                .get("id")
            )
        except:
            id_class = call_kiwi(
                lambda rpc: rpc.Classification.create({"name": subject})
            ).data.get("id")

        try:
            id_vuln = (
                call_kiwi(
                    lambda rpc: rpc.Product.filter({"name": f"{subject}_product"})
                )
                .data[0]
                .get("id")
            )
        except:
            id_vuln = call_kiwi(
                lambda rpc: rpc.Product.create(
                    {
                        "name": f"{subject}_product",
                        "classification": id_class,
                        "description": f"Product reserved for {subject}",
                    }
                )
            ).data.get("id")

        status = (
            call_kiwi(lambda rpc: rpc.TestCaseStatus.filter({"name": data["status"]}))
            .data[0]
            .get("id")
        )
        category = (
            call_kiwi(lambda rpc: rpc.Category.filter({"name": "--default--"}))
            .data[0]
            .get("id")
        ) or None

        try:
            response = call_kiwi(
                lambda rpc: rpc.TestCase.create(
                    {
                        "summary": summary,
                        "text": text,
                        "product": id_vuln,
                        "script": script,
                        "case_status": status,
                        "priority": int(data["priority"]),
                        "category": category,
                    }
                )
            )

            return response
        except:
            return response

    def update(self, request, pk=None):
        data = request.data

        file = request.FILES.get("file")

        summary = data.get("name")
        text = data.get("description")
        extra_link = data.get("source")
        if file:
            try:
                response = call_kiwi(
                    lambda rpc: rpc.TestCase.add_attachment(
                        pk,
                        file.name,
                        file.read(),
                    )
                )
                return response
            except:
                return server_response

        try:
            response = call_kiwi(
                lambda rpc: rpc.TestCase.update(
                    pk,
                    {
                        "summary": summary,
                        "text": text,
                        "extra_link": extra_link,
                    },
                )
            )
            return response
        except:
            return server_response

    def destroy(self, request, pk=None):

        try:
            response = call_kiwi(lambda rpc: rpc.TestCase.remove({"pk__in": [pk]}))
            return deleted_response
        except:
            return not_found_response


class TestPlanView(viewsets.ViewSet):

    queryset = TestPlan.objects.all()
    serializer_class = TestPlanSerializer
    serializer = serializer_class(queryset, many=True)

    def list(self, request):

        try:
            response = call_kiwi(lambda rpc: rpc.TestPlan.filter({}))
            data = response.data.copy()

            for plan in data:
                attachments_response = call_kiwi(
                    lambda rpc: rpc.TestPlan.list_attachments(plan["id"])
                ).data

                attachments = {"files": []}
                for att in attachments_response:
                    attachments["files"].append(
                        f"uploads{'uploads'.join(att['url'].split('uploads')[1:])}"
                    )

                plan["attachments"] = attachments

            return Response(data, status=200)
        except:
            return server_response


class TestRunView(viewsets.ViewSet):

    queryset = TestRun.objects.all()
    serializer_class = TestRunSerializer
    serializer = serializer_class(queryset, many=True)

    def list(self, request):

        try:
            response = call_kiwi(lambda rpc: rpc.TestRun.filter({}))
            return response
        except:
            return server_response

    def create(self, request):
        data = request.data
        summary = data.get("name")
        notes = data.get("description")
        product = int(data.get("product_id"))
        build_resp = call_kiwi(lambda rpc: rpc.Build.filter({"version": product}))

        build_id = build_resp.data[0]["id"]
        manager = data.get("manager", 2)
        plan = data.get("test_plan", 1)
        start_date = data.get("start_date", None)
        stop_date = data.get("end_date", None)

        post_data = {
            "summary": summary,
            "product": product,
            "notes": notes,
            "plan": plan,
            "manager": manager,
            "build": build_id,
            "planned_start": start_date,
            "planned_stop": stop_date,
        }

        try:
            response = call_kiwi(lambda rpc: rpc.TestRun.create(post_data))
            return response
        except Exception as e:

            return log_response(e.__str__())

    def update(self, request, pk=None):
        data = request.data

        summary = data.get("name")
        notes = data.get("description")
        product = data.get("id_kiwi")

        try:
            response = call_kiwi(
                lambda rpc: rpc.TestRun.update(
                    pk,
                    {
                        "summary": summary,
                        "product": product,
                        "notes": notes,
                    },
                )
            )
            return response
        except:
            return server_response

    def destroy(self, request, pk=None):

        try:
            # TODO: uncomment when tag 13.5 is released:
            # response = call_kiwi(lambda rpc: rpc.TestRun.remove({"pk__in": [pk]}))
            response = call_kiwi(
                lambda rpc: rpc.TestRun.update(pk, {"summary": "DELETED"})
            )
            return deleted_response
        except:
            return not_found_response


class TestExecutionView(viewsets.ViewSet):

    queryset = TestExecution.objects.all()
    serializer_class = TestExecutionSerializer
    serializer = serializer_class(queryset, many=True)

    def list(self, request):

        try:
            response = call_kiwi(lambda rpc: rpc.TestExecution.filter({}))
            return response
        except:
            return server_response

    def retrieve(self, request, pk=None):

        try:
            response = call_kiwi(lambda rpc: rpc.TestExecution.filter({"id": pk}))
            return response
        except:
            return server_response

    def update(self, request, pk=None):
        data = request.data

        status = data.get("result")

        try:
            response = call_kiwi(
                lambda rpc: rpc.TestExecution.update(
                    pk,
                    {
                        "status": call_kiwi(
                            lambda rpc: rpc.TestExecutionStatus.filter({"name": status})
                        )
                        .data[0]
                        .get("id")
                    },
                )
            )
            return response
        except:
            return server_response

    def destroy(self, request, pk=None):

        try:
            response = call_kiwi(lambda rpc: rpc.TestExecution.remove({"id": pk}))
            return response
        except:
            return server_response


# class TestRunCaseView(viewsets.ViewSet):

#     queryset = TestRunCase.objects.all()
#     serializer_class = TestRunCaseSerializer
#     serializer = serializer_class(queryset, many=True)

#     def list(self, request):
#         runs_ids = [
#             r.get("id") for r in call_kiwi(lambda rpc: rpc.TestRun.filter({})).data
#         ]
#         try:

#             data = [
#                 {
#                     "run_id": id,
#                     "cases": [
#                         case.get("id")
#                         for case in call_kiwi(
#                             lambda rpc: rpc.TestRun.get_cases(id)
#                         ).data
#                     ],
#                 }
#                 for id in runs_ids
#             ]
#             return ok_response(data)
#         except:
#             return server_response

#     def retrieve(self, request, pk=None):
#         runs_ids = [
#             r.get("id") for r in call_kiwi(lambda rpc: rpc.TestRun.filter({})).data
#         ]
#         if int(pk) not in runs_ids:
#             return not_found_response
#         try:
#             data = {
#                 "run_id": pk,
#                 "cases": [
#                     case.get("id")
#                     for case in call_kiwi(lambda rpc: rpc.TestRun.get_cases(pk)).data
#                 ],
#             }
#             return ok_response(data)
#         except:
#             return not_found_response

#     def update(self, request, pk=None):
#         data = request.data
#         test_cases = data.get("test_cases")

#         if data.get("action") == "add":

#             try:
#                 response = [
#                     call_kiwi(
#                         lambda rpc: rpc.TestRun.add_case(pk, case_id).data.get("id")
#                     )
#                     for case_id in test_cases
#                 ]
#                 return Response(
#                     {"message": "The test cases have been added"},
#                     status=200,
#                 )
#             except:
#                 return server_response

#         elif data.get("action") == "remove":

#             try:
#                 response = [
#                     call_kiwi(
#                         lambda rpc: rpc.TestRun.remove_case(pk, case_id).data.get("id")
#                     )
#                     for case_id in test_cases
#                 ]
#                 return Response(
#                     {"message": "The test cases have been removed"},
#                     status=200,
#                 )
#             except:
#                 return server_response

#         else:
#             return Response(
#                 {"error": "The given action is not supported"},
#                 status=400,
#             )

#     def create(self, request):
#         data = request.data

#         test_run = data.get("test_run_id")
#         test_case = data.get("test_case_id")

#         try:
#             response = call_kiwi(
#                 lambda rpc: rpc.TestRunCase.create(
#                     {
#                         "test_run": test_run,
#                         "test_case": test_case,
#                     }
#                 )
#             )
#             return response
#         except:
#             return server_response

#     def destroy(self, request, pk=None):

#         try:
#             response = call_kiwi(lambda rpc: rpc.TestRunCase.remove({"id": pk}))
#             return response
#         except:
#             return server_response


class ExportView(viewsets.ViewSet):
    """
    A class to retrieve all TestExecution objects.

    It supports list and get.

    The list returns all TestExecution objects without any filtering.

    Use get with a product id to retrieve all test execution, only part of terminated test runs, for the given product
    Example: http://localhost:8081/api/export/13

    (notice that the product id is **not the SUT id** of Cyrus)
    """

    queryset = Export.objects.all()
    serializer_class = ExportSerializer
    serializer = serializer_class(queryset, many=True)

    def retrieve(self, request, pk=None):
        data = request.data
        try:
            response = call_kiwi(
                lambda rpc: rpc.TestExecution.filter({"build": int(data["product"])})
            )

            return response
        except:
            return server_response

    def get_comments(self, execution_id):
        response = call_kiwi(
            lambda rpc: rpc.TestExecution.get_comments(execution_id)
        ).data
        comments = [
            content.get("comment") if not isinstance(content, str) else ""
            for content in response
        ]

        return "\n------\n".join(comments)

    def list(self, request):
        pk = request.data["product"]
        test_runs = [
            (
                run.get("id"),
                run.get("summary"),
                run.get("notes"),
                run.get("build__version"),
                run.get("plan"),
            )
            for run in call_kiwi(
                lambda rpc: rpc.TestRun.filter({"build__version__product": pk})
            ).data
            if run.get("stop_date") != None
        ]

        test_executions = [
            sorted(
                call_kiwi(lambda rpc: rpc.TestExecution.filter({"run": item[0]})).data,
                key=lambda x: (x.get("case"), x.get("start_date")),
            )
            for item in test_runs
        ]

        def run_info(id):
            run = next((run for run in test_runs if run[0] == id), None)

            if run == None:
                return {"run_id": None}
            else:
                return {
                    "tcms_external_id": str(id),
                    "name": run[1],
                    "description": run[2],
                    "sut": run[3],
                    "plan": run[4],
                }

        data = [
            {
                "product_id": pk,
                "run": run_info(k[0].get("run")),
                "executions": [
                    {
                        "id": execution.get("id"),
                        "test_case": execution.get("case"),
                        "test_run": execution.get("run"),
                        "status": execution.get("status__name"),
                        "start": execution.get("start_date"),
                        "end": execution.get("stop_date"),
                        "comments": self.get_comments(execution.get("id")),
                    }
                    for execution in k
                ],
            }
            for k in test_executions
            if k != []
        ]

        return ok_response(data)
