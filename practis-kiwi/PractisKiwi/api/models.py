from django.db import models
from django.utils import timezone as tz


class Product(models.Model):

    CLASSIFICATIONS = [("sut", "sut"), ("vulnerability", "vulnerability")]

    name = models.CharField(max_length=200)
    description = models.CharField(max_length=500)
    classification = models.CharField(
        max_length=100, choices=CLASSIFICATIONS, default=CLASSIFICATIONS[0][0]
    )

    class Meta:
        db_table = "product"

    def __str__(self):
        return self.name


class Category(models.Model):

    name = models.CharField(max_length=200)
    description = models.CharField(max_length=300)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)

    class Meta:
        db_table = "category"

    def __str__(self):
        return self.name


class Version(models.Model):

    value = models.Char<PERSON>ield(max_length=250)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)

    class Meta:
        db_table = "version"

    def __str__(self):
        return self.value


class TestCase(models.Model):

    STATUS = [
        ("PROPOSED", "PROPOSED"),
        ("DISABLED", "DISABLED"),
        ("NEED_UPDATE", "NEED_UPDATE"),
        ("CONFIRMED", "CONFIRMED"),
    ]

    PRIORITY = [
        ("1", "P1"),
        ("2", "P2"),
        ("3", "P3"),
        ("4", "P4"),
        ("5", "P5"),
    ]

    summary = models.CharField(max_length=200)
    create_date = models.DateTimeField(default=tz.now)
    case_status_name = models.CharField(
        max_length=50, choices=STATUS, default=STATUS[0][0]
    )
    priority_value = models.CharField(
        max_length=50, choices=PRIORITY, default=PRIORITY[0][1]
    )
    is_automated = models.BooleanField(default=False)
    text = models.TextField(blank=True, null=True)
    notify = models.BooleanField(default=False)
    testing_duration = models.TimeField(null=True, blank=True)
    setup_duration = models.TimeField(null=True, blank=True)
    expected_duration = models.TimeField(null=True, blank=True)
    extra_link = models.URLField(null=True, blank=True)
    description = models.TextField(blank=True, null=True)
    script = models.TextField(blank=True, null=True)
    arguments = models.TextField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    product = models.ForeignKey(Product, on_delete=models.SET_NULL, null=True)
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True)

    class Meta:
        db_table = "test_case"

    def __str__(self):
        return self.summary


class TestPlan(models.Model):

    TYPES = [
        ("Acceptance", "Acceptance"),
        ("Function", "Function"),
        ("Installation", "Installation"),
        ("Integration", "Integration"),
        ("Interoperability", "Interoperability"),
        ("Performance", "Performance"),
        ("Product", "Product"),
        ("Regression", "Regression"),
        ("Smoke", "Smoke"),
        ("System", "System"),
    ]
    name = models.CharField(max_length=200)
    text = models.CharField(max_length=500)
    create_date = models.DateTimeField()
    is_active = models.BooleanField(default=False)
    type = models.CharField(max_length=50, choices=TYPES, default=TYPES[0][0])
    extra_link = models.CharField(max_length=250)
    notify = models.BooleanField(default=False)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    version = models.ForeignKey(Version, on_delete=models.CASCADE)
    test_cases = models.ManyToManyField(TestCase, blank=True)

    class Meta:
        db_table = "test_plan"

    def __str__(self):
        return self.name


class TestRun(models.Model):

    summary = models.CharField(max_length=250)
    build = models.CharField(max_length=100, blank=True, null=True)
    planned_start = models.DateTimeField(null=True, blank=True)
    planned_stop = models.DateTimeField(null=True, blank=True)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    test_plan = models.ForeignKey(TestPlan, on_delete=models.CASCADE)
    notes = models.TextField(blank=True, null=True)

    class Meta:
        db_table = "test_run"

    def __str__(self):
        return self.summary


class TestExecution(models.Model):

    test_run = models.ForeignKey(TestRun, on_delete=models.CASCADE)
    test_case = models.ForeignKey(TestCase, on_delete=models.CASCADE)
    status = models.CharField(max_length=50)
    start = models.DateTimeField()
    end = models.DateTimeField()
    notes = models.TextField(blank=True, null=True)
    extra_link = models.URLField()

    class Meta:
        db_table = "test_execution"

    def __str__(self):
        return self.test_case.summary


# class TestRunCase(models.Model):

#     test_run = models.ForeignKey(TestRun, on_delete=models.CASCADE)
#     test_case = models.ManyToManyField(TestCase)

#     class Meta:
#         db_table = "test_run_case"


class Export(models.Model):

    product = models.IntegerField()

    class Meta:
        db_table = "export"
