from rest_framework.response import Response


server_response = Response({"error": "The remote server can not be found"}, status=400)

auth_reponse = Response(
    {"error": "Failed to authenticate, provide correct credentials"}, status=401
)

not_found_response = Response(
    {"error": "The requested resource can not be found"}, status=404
)

process_response = Response(
    {"message": "An exception occured when processing the response"}, status=500
)

deleted_response = Response({"message": "The resource has been deleted"}, status=204)


def ok_response(data):
    return Response(data, status=200)


def created_response(data):
    return Response(data, status=201)


def log_response(text):
    return Response({"message": text}, status=500)
