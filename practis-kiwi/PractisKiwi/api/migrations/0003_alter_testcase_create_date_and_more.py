# Generated by Django 4.2.7 on 2024-06-07 12:55

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0002_category_product_testcase_testexecution_testplan_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="testcase",
            name="create_date",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name="testcase",
            name="expected_duration",
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="testcase",
            name="extra_link",
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="testcase",
            name="setup_duration",
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="testcase",
            name="testing_duration",
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name="TestRunCase",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("test_case", models.ManyToManyField(to="api.testcase")),
                (
                    "test_run",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="api.testrun"
                    ),
                ),
            ],
            options={
                "db_table": "test_run_case",
            },
        ),
    ]
