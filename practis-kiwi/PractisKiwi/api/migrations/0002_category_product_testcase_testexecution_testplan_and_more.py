# Generated by Django 4.2.7 on 2024-06-06 14:40

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Category",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("description", models.CharField(max_length=300)),
            ],
            options={
                "db_table": "category",
            },
        ),
        migrations.CreateModel(
            name="Product",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("description", models.CharField(max_length=500)),
                (
                    "classification",
                    models.Char<PERSON>ield(
                        choices=[("sut", "sut"), ("vulnerability", "vulnerability")],
                        default="sut",
                        max_length=100,
                    ),
                ),
            ],
            options={
                "db_table": "product",
            },
        ),
        migrations.CreateModel(
            name="TestCase",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("summary", models.CharField(max_length=200)),
                ("create_date", models.DateTimeField()),
                (
                    "case_status_name",
                    models.CharField(
                        choices=[
                            ("PROPOSED", "PROPOSED"),
                            ("DISABLED", "DISABLED"),
                            ("NEED_UPDATE", "NEED_UPDATE"),
                            ("CONFIRMED", "CONFIRMED"),
                        ],
                        default="PROPOSED",
                        max_length=50,
                    ),
                ),
                (
                    "priority_value",
                    models.CharField(
                        choices=[
                            ("1", "P1"),
                            ("2", "P2"),
                            ("3", "P3"),
                            ("4", "P4"),
                            ("5", "P5"),
                        ],
                        default="P1",
                        max_length=50,
                    ),
                ),
                ("is_automated", models.BooleanField(default=False)),
                ("text", models.TextField(blank=True, null=True)),
                ("notify", models.BooleanField(default=False)),
                ("testing_duration", models.TimeField()),
                ("setup_duration", models.TimeField()),
                ("expected_duration", models.TimeField()),
                ("extra_link", models.URLField()),
                ("description", models.TextField(blank=True, null=True)),
                ("script", models.TextField(blank=True, null=True)),
                ("arguments", models.TextField(blank=True, null=True)),
                ("notes", models.TextField(blank=True, null=True)),
                (
                    "category",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="api.category",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="api.product",
                    ),
                ),
            ],
            options={
                "db_table": "test_case",
            },
        ),
        migrations.CreateModel(
            name="TestExecution",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("status", models.CharField(max_length=50)),
                ("start", models.DateTimeField()),
                ("end", models.DateTimeField()),
                ("notes", models.TextField(blank=True, null=True)),
                ("extra_link", models.URLField()),
                (
                    "test_case",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="api.testcase"
                    ),
                ),
            ],
            options={
                "db_table": "test_execution",
            },
        ),
        migrations.CreateModel(
            name="TestPlan",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("text", models.CharField(max_length=500)),
                ("create_date", models.DateTimeField()),
                ("is_active", models.BooleanField(default=False)),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("Acceptance", "Acceptance"),
                            ("Function", "Function"),
                            ("Installation", "Installation"),
                            ("Integration", "Integration"),
                            ("Interoperability", "Interoperability"),
                            ("Performance", "Performance"),
                            ("Product", "Product"),
                            ("Regression", "Regression"),
                            ("Smoke", "Smoke"),
                            ("System", "System"),
                        ],
                        default="Acceptance",
                        max_length=50,
                    ),
                ),
                ("extra_link", models.CharField(max_length=250)),
                ("notify", models.BooleanField(default=False)),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="api.product"
                    ),
                ),
                ("test_cases", models.ManyToManyField(blank=True, to="api.testcase")),
            ],
            options={
                "db_table": "test_plan",
            },
        ),
        migrations.CreateModel(
            name="TestRun",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("summary", models.CharField(max_length=250)),
                ("build", models.CharField(blank=True, max_length=100, null=True)),
                ("start", models.DateTimeField(blank=True, null=True)),
                ("end", models.DateTimeField(blank=True, null=True)),
                ("notes", models.TextField(blank=True, null=True)),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="api.product"
                    ),
                ),
                (
                    "test_plan",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="api.testplan"
                    ),
                ),
            ],
            options={
                "db_table": "test_run",
            },
        ),
        migrations.CreateModel(
            name="Version",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("value", models.CharField(max_length=250)),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="api.product"
                    ),
                ),
            ],
            options={
                "db_table": "version",
            },
        ),
        migrations.DeleteModel(
            name="Script",
        ),
        migrations.AddField(
            model_name="testplan",
            name="version",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="api.version"
            ),
        ),
        migrations.AddField(
            model_name="testexecution",
            name="test_run",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="api.testrun"
            ),
        ),
        migrations.AddField(
            model_name="category",
            name="product",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="api.product"
            ),
        ),
    ]
