from rest_framework import serializers
from .models import *


class ProductSerializer(serializers.ModelSerializer):

    class Meta:

        model = Product
        fields = "__all__"


class CategorySerializer(serializers.ModelSerializer):

    class Meta:

        model = Category
        fields = "__all__"


class VersionSerializer(serializers.ModelSerializer):

    class Meta:
        model = Version
        fields = "__all__"


class TestCaseSerializer(serializers.ModelSerializer):

    class Meta:
        model = TestCase
        fields = "__all__"


class TestPlanSerializer(serializers.ModelSerializer):

    class Meta:
        model = TestPlan
        fields = "__all__"


class TestRunSerializer(serializers.ModelSerializer):

    class Meta:
        model = TestRun
        fields = "__all__"


class TestExecutionSerializer(serializers.ModelSerializer):

    class Meta:
        model = TestExecution
        fields = "__all__"


# class TestRunCaseSerializer(serializers.ModelSerializer):

#     class Meta:
#         model = TestRunCase
#         fields = "__all__"


class ExportSerializer(serializers.ModelSerializer):

    class Meta:
        model = Export
        fields = "__all__"
