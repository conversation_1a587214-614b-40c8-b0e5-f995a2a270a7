"""
URL configuration for PractisKiwi project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from api.views import *

router = DefaultRouter()

router.register(r"product", ProductView)
router.register(r"category", CategoryView)
router.register(r"version", VersionView)
router.register(r"testcase", TestCaseView)
router.register(r"testplan", TestPlanView)
router.register(r"testrun", TestRunView)
router.register(r"testexecution", TestExecutionView)
# router.register(r"testruncase", TestRunCaseView)
router.register(r"export", ExportView)


urlpatterns = [
    path("admin/", admin.site.urls),
    path("api/", include(router.urls)),
]
