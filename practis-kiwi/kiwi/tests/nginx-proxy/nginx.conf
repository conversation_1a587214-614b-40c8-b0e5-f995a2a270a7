user  nginx;
worker_processes  auto;

error_log /dev/stdout;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # note: this should be bigger than
    # FILE_UPLOAD_MAX_SIZE from Kiwi TCMS which defaults to 5M.
    client_max_body_size 6m;

    ssl_certificate     /etc/nginx/localhost.crt;
    ssl_certificate_key /etc/nginx/localhost.key;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers off;

    # default proxy settings
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /dev/stdout;

    server {
        # server_name  tcms.example.com;
        listen 8443 ssl;

        location / {
            proxy_pass https://kiwi_web:8443;
        }
    }
}
