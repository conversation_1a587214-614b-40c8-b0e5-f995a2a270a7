services:

    init:
        image: kiwitcms/kiwi:latest
        volumes:
            - ./kiwi/init.sh:/init.sh

        env_file:
            - ./.env
        environment:
            KIWI_DB_ENGINE: django.db.backends.postgresql
            KIWI_DB_HOST: ${KIWI_DB_HOST}
            KIWI_DB_PORT: ${KIWI_DB_PORT}
            # KIWI_DB_NAME: ${KIWI_DB_NAME}
            # KIWI_DB_USER: ${KIWI_DB_USER}
            # KIWI_DB_PASSWORD: ${KIWI_DB_PASSWORD}
            KIWI_USER_FILE: /run/secrets/kiwi_user
            KIWI_PASS_FILE: /run/secrets/kiwi_pass
        secrets:
            - kiwi_db_name
            - kiwi_db_user
            - kiwi_db_password_init
            - kiwi_user
            - kiwi_pass
        entrypoint: ["bash", "/init.sh"]
        depends_on:
            postgres:
                condition: service_healthy
        networks:
            - practis-network

    web:
        container_name: kiwi_web
        restart: always
        image: kiwitcms/kiwi:latest
        env_file:
            - ./.env
        ports:
            - ${KIWI_WEB_PORT_LOCAL_1}:${KIWI_WEB_PORT_CONTAINER_1}
            - ${KIWI_WEB_PORT_LOCAL_2}:${KIWI_WEB_PORT_CONTAINER_2}
        volumes:
            - uploads:/Kiwi/uploads:Z$
            - ./secrets:/run/secrets
        depends_on:
            - init
        environment:
            KIWI_DB_ENGINE: django.db.backends.postgresql
            KIWI_DB_HOST: ${KIWI_DB_HOST}
            KIWI_DB_PORT: ${KIWI_DB_PORT}
            # KIWI_DB_NAME: ${KIWI_DB_NAME}
            # KIWI_DB_USER: ${KIWI_DB_USER}
            # KIWI_DB_PASSWORD: ${KIWI_DB_PASSWORD}
            KIWI_SERVER: https://web:${KIWI_WEB_PORT_CONTAINER_2}/xml-rpc/
        networks:
            - practis-network

    proxy:
        container_name: reverse_proxy
        image: nginx
        volumes:
            - ./tests/nginx-proxy/nginx.conf:/etc/nginx/nginx.conf:Z
            - ./tests/nginx-proxy/localhost.key:/etc/nginx/localhost.key:Z
            - ./tests/nginx-proxy/localhost.crt:/etc/nginx/localhost.crt:Z
        ports:
            - 4343:8443
        depends_on:
            - web

volumes:
    uploads:

secrets:
    kiwi_db_name:
        file: ./secrets/kiwi_db_name.txt
    kiwi_db_user:
        file: ./secrets/kiwi_db_username.txt
    kiwi_db_password_init:
        file: ./secrets/kiwi_db_password_init.txt
    kiwi_user:
        file: ./secrets/kiwi_admin_username.txt
    kiwi_pass:
        file: ./secrets/kiwi_admin_password.txt
