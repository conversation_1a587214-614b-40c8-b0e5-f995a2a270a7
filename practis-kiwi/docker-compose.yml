include:
  - ./kiwi/docker-compose-kiwi.yml

services:
  tcms-api:
    container_name: practis-api-kiwi
    build:
      context: ./PractisKiwi/
    restart: always
    env_file:
      - ./PractisKiwi/.env
    environment:
      KIWI_USER_FILE: /run/secrets/kiwi_admin_username
      KIWI_PASS_FILE: /run/secrets/kiwi_admin_password
      KIWI_SERVER_FILE: /run/secrets/kiwi_api_kiwi_server
      DJANGO_SECRET_KEY_FILE: /run/secrets/kiwi_api_secret_key
    secrets:
      - kiwi_admin_username
      - kiwi_admin_password
      - kiwi_api_kiwi_server
      - kiwi_api_secret_key
    networks:
      - practis-network
    ports:
      - 8081:8081
    

secrets:
  kiwi_admin_username:
    file: ./PractisKiwi/secrets/kiwi_admin_username.txt
  kiwi_admin_password:
    file: ./PractisKiwi/secrets/kiwi_admin_password.txt
  kiwi_api_kiwi_server:
    file: ./PractisKiwi/secrets/kiwi_api_kiwi_server.txt
  kiwi_api_secret_key:
    file: ./PractisKiwi/secrets/kiwi_api_secret_key.txt

