# Define default settings for all jobs
default:
  tags:
    - ci  # Use runners tagged with 'ci'

# Include external templates for different job configurations
include:
  - project: 'practis/practis/ci-templates'
    file: '/python-jobs/static_analysis/vulture.tpl.yml'  # Vulture dead code analyzer template
  - project: 'practis/practis/ci-templates'
    file: '/python-jobs/type_checking/mypy.tpl.yml'  # Mypy type checker template
  - project: 'practis/practis/ci-templates'
    file: '/python-jobs/security/bandit.tpl.yml'  # Bandit security linter template
  - project: 'practis/practis/ci-templates'
    file: '/python-jobs/license_management/pylic.tpl.yml'  # Pylic license checker template
  - project: 'practis/practis/ci-templates'
    file: '/other-jobs/sonarqube/sonarqube.tpl.yml'  # SonarQube scanner template
  - project: 'practis/practis/ci-templates'
    file: '/docker-jobs/image_build/docker_build.tpl.yml'  # Docker build template
  - project: 'practis/practis/ci-templates'
    file: '/docker-jobs/security_scanning/grype.tpl.yml'  # Docker build template
  - project: 'practis/practis/ci-templates'
    file: '/nexus-jobs/nexus_upload.tpl.yml'  # Docker build template

# Define the stages of the pipeline
stages:
  - quality  # Code quality checks stage
  - security  # Security checks stage
  - sonarqube  # SonarQube analysis stage
  - build  # Docker image build stage

# Set pipeline-wide variables
variables:
  API_IMAGE_NAME: practis-kiwi  # Name of the Docker image to build
  DOCKERFILE_PATH: ./PractisKiwi/Dockerfile  # Path to the Dockerfile

vulture:
  stage: quality
  extends: .vulture  # Extend from the vulture dead code analyzer template
  when: always

mypy:
  stage: quality
  extends: .mypy  # Extend from the mypy type checker template
  when: always

pylic:
  stage: security
  extends: .pylic  # Extend from the pylic license checker template
  when: always

bandit:
  stage: security
  extends: .bandit  # Extend from the bandit security linter template
  when: always

sonarqube-check:
  stage: sonarqube
  extends: .sonarqube  # Extend from the SonarQube scanner template
  when: always  # Run this job always, even if previous jobs fail

build:
  stage: build
  extends: .docker_build  # Extend from the Docker build template

grype:
  stage: build
  extends: .grype

push_nexus:
  stage: build
  extends: .push_nexus