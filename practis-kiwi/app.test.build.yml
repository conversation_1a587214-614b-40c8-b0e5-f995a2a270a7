version: '3.8'

networks:
  practis-network:
    driver: bridge
  pg-network:
    driver: bridge

services:

  postgres:
    container_name: postgres
    build:
      context: ./postgres/
    restart: always
    environment:
      POSTGRES_USER_FILE: /run/secrets/db_username
      POSTGRES_PASSWORD_FILE: /run/secrets/db_password
      POSTGRES_DB_FILE: /run/secrets/db_name
    secrets:
      - db_password
      - db_name
      - db_username
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./postgres/postgres-data:/docker-entrypoint-initdb.d/
    ports:
      - 5432:5432
    networks:
      - practis-network
      - pg-network

  practis-api:
    container_name: practis-api
    build:
      dockerfile: ./ci/Dockerfile.pytest
    restart: no
    depends_on:
      - postgres
    env_file:
      - ./api/.env
    environment:
      DEV_SECRET_KEY_FILE: /run/secrets/dev_secret_key
      PROD_SECRET_KEY_FILE: /run/secrets/prod_secret_key
      DB_PASS_FILE: /run/secrets/db_password
      DEV_DB_NAME_FILE: /run/secrets/dev_db_name
      DB_NAME_FILE: /run/secrets/db_name
      DB_USERNAME_FILE: /run/secrets/db_username
      REALM_FILE: /run/secrets/realm
      CLIENT_ID_FILE: /run/secrets/client_id
      CLIENT_SECRET_FILE: /run/secrets/client_secret
      WELL_KNOWN_FILE: /run/secrets/well_known
      OPENVAS_BROKER_FILE: /run/secrets/openvas_broker
    volumes:
      - ./api/media:/api/media
    secrets:
      - dev_secret_key
      - prod_secret_key
      - db_password
      - dev_db_name
      - db_name
      - db_username
      - realm
      - client_id
      - client_secret
      - well_known
      - kiwi_broker
      - openvas_broker
    ports:
      - 8000:8000
    networks:
      - practis-network
  
  pgadmin:
    container_name: pgadmin
    image: dpage/pgadmin4:latest
    restart: always
    depends_on:
      - postgres
    env_file:
      - .env
    environment:
      PGADMIN_DEFAULT_PASSWORD_FILE: /run/secrets/pgadmin_pwd
    secrets:
      - pgadmin_pwd
    volumes:
      - ./pgadmin/servers.json:/pgadmin4/servers.json
      - ./pgadmin/pgpass.txt:/pgpass
    ports:
      - 5050:80
    networks:
      - pg-network

volumes:
  postgres-data:

secrets:
  db_username:
    file: ./secrets/db_username.txt
  db_password:
    file: ./secrets/db_password.txt
  dev_db_name:
    file: ./secrets/dev_db_name.txt
  db_name:
    file: ./secrets/db_name.txt
  pgadmin_pwd:
    file: ./secrets/pgadmin_pwd.txt
  dev_secret_key:
    file: ./secrets/dev_django_secret_key.txt
  prod_secret_key:
    file: ./secrets/prod_django_secret_key.txt
  realm:
    file: ./secrets/sso_realm.txt
  client_id:
    file: ./secrets/sso_client_id.txt
  client_secret:
    file: ./secrets/sso_client_secret.txt
  well_known:
    file: ./secrets/sso_well_known.txt
  kiwi_broker:
    file: ./secrets/kiwi_broker.txt
  openvas_broker:
    file: ./secrets/openvas_broker.txt