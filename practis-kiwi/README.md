# CYRUS TCMS

This part contains the tcms server and the tcms broker that allow the APi to interact with the tcms server. You can't dissociate them.

## Set the TCMS server

Create a .env file at ./kiwi/.env to define your environment variables:

```bash
KIWI_DB_HOST=$your_db_host
KIWI_DB_PORT=$your_db_port
KIWI_WEB_PORT_LOCAL_1=$tcms_local_port1
KIWI_WEB_PORT_CONTAINER_1=$tcms_remote_port1
KIWI_WEB_PORT_LOCAL_2=$tcms_local_port2
KIWI_WEB_PORT_CONTAINER_2=$tcms_remote_port2
PROXY_PORT_LOCAL=$your_proxy_local_port
PROXY_PORT_CONTAINER=$your_proxy_remote_port

# ⛔️ Avoid mentionning your secret data here!!! ⛔️
# use secrets instead!
```

Now, create a __secrets__ folder at ./kiwi/secrets and create all your secrets files. To know which secret to create, please check the file [docker-compose.yml](./kiwi/docker-compose-kiwi.yml).

your tcms server is ready!

## Set the TCMS Broker

Create a .env file at ./PractisKiwi/.env to define your environment variables

```bash
DEBUG=$debug_value (1 for dev and 0 for prod)
DJANGO_SECRET_KEY=$your_django_key
TCMS_API_PORT_LOCAL=$tcms_local_port
TCMS_API_PORT_CONTAINER=$tcms_container_port

# ⛔️ Avoid mentionning your secret data here!!! ⛔️
# use secrets instead!
```

Now create a __secrets__ folder at ./PractisKiwi/secrets and create all your secrets files. To know which secret to create, please check the file [docker-compose.yml](./docker-compose.yml).

> 📝 If you have a doubt about the value to fill the placeholder, you can contact one of the dev team members (@tnn, @jla or @apc)

you're all set!