networks:
  practis-network:
    driver: bridge

services:

  postgres:
    container_name: postgres
    image: postgres:16.1
    restart: always
    env_file:
      - ./practis-api/.env
    environment:
      POSTGRES_USER_FILE: /run/secrets/postgres_username
      POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password
      POSTGRES_DB_FILE: /run/secrets/practis_api_prod_db
      LOG_LEVEL: INFO
    healthcheck:
      test: ["CMD-SHELL","pg_isready -U $(cat /run/secrets/postgres_username) -d $(cat /run/secrets/practis_api_prod_db)"]
      interval: 10s
      timeout: 5s
      retries: 7
      start_period: 30s
    secrets:
      - postgres_password
      - practis_api_prod_db
      - postgres_username
    volumes:
      - postgres-data-practis:/var/lib/postgresql/data
      - ./postgres/postgres-data:/docker-entrypoint-initdb.d/
      
    ports:
      - 5432:5432
    networks:
      - practis-network

  practis-api:
    container_name: practis-api
    build:
      context: ./practis-api/
    restart: always
    depends_on:
      postgres:
        condition: service_healthy
    env_file:
      - ./practis-api/.env
    environment:
      DEV_SECRET_KEY_FILE: /run/secrets/practis_api_dev_key
      PROD_SECRET_KEY_FILE: /run/secrets/practis_api_prod_key
      DB_PASS_FILE: /run/secrets/postgres_password
      DEV_DB_NAME_FILE: /run/secrets/practis_api_dev_db
      DB_NAME_FILE: /run/secrets/practis_api_prod_db
      DB_USERNAME_FILE: /run/secrets/postgres_username
      REALM_FILE: /run/secrets/api_kc_realm_name
      CLIENT_ID_FILE: /run/secrets/api_kc_client_id
      CLIENT_SECRET_FILE: /run/secrets/api_kc_client_secret
      WELL_KNOWN_FILE: /run/secrets/api_kc_well_known
      SERVER_URL_FILE: /run/secrets/api_kc_server_url
      OPENVAS_BROKER_FILE: /run/secrets/openvas_broker
      
    volumes:
      - uploads:/api/uploads:Z$
      - logs:/var/run/logs:Z$
      - ./practis-api/media:/api/media
      - ./practis-api/practis/logs:/var/run/logs
      - ./practis-api/practis/media:/api/practis/media
    secrets:
      - practis_api_dev_key
      - practis_api_prod_key
      - postgres_password
      - practis_api_dev_db
      - practis_api_prod_db
      - postgres_username
      - api_kc_realm_name
      - api_kc_client_id
      - api_kc_client_secret
      - api_kc_well_known
      - api_kc_server_url
      - api_kw_broker
      - openvas_broker
    ports:
      - 8000:8000
    networks:
      - practis-network

volumes:
  postgres-data-practis:
  uploads:
  logs:

secrets:
  postgres_username:
    file: ./secrets/postgres_username.txt
  postgres_password:
    file: ./secrets/postgres_password.txt
  practis_api_dev_db:
    file: ./secrets/practis_api_dev_db.txt
  practis_api_prod_db:
    file: ./secrets/practis_api_prod_db.txt
  pgadmin_pwd:
    file: ./secrets/pgadmin_pwd.txt
  practis_api_dev_key:
    file: ./secrets/practis_api_dev_key.txt
  practis_api_prod_key:
    file: ./secrets/practis_api_prod_key.txt
  api_kc_realm_name:
    file: ./secrets/api_kc_realm_name.txt
  api_kc_client_id:
    file: ./secrets/api_kc_client_id.txt
  api_kc_client_secret:
    file: ./secrets/api_kc_client_secret.txt
  api_kc_well_known:
    file: ./secrets/api_kc_well_known.txt
  api_kc_server_url:
    file: ./secrets/api_kc_server_url.txt
  api_kw_broker:
    file: ./secrets/api_kw_broker.txt
  openvas_broker:
    file: ./secrets/openvas_broker.txt
