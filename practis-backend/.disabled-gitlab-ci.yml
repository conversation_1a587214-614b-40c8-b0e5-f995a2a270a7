# This file is a template, and might need editing before it works on your project.
# This is a sample GitLab CI/CD configuration file that should run without any modifications.
# It demonstrates a basic 3 stage CI/CD pipeline. Instead of real tests or scripts,
# it uses echo commands to simulate the pipeline execution.
#
# A pipeline is composed of independent jobs that run scripts, grouped into stages.
# Stages run in sequential order, but jobs within stages run in parallel.
#
# For more information, see: https://docs.gitlab.com/ee/ci/yaml/index.html#stages
#
# You can copy and paste this template into a new `.gitlab-ci.yml` file.
# You should not add this template to an existing `.gitlab-ci.yml` file by using the `include:` keyword.
#
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Getting-Started.gitlab-ci.yml
variables:
  GIT_STRATEGY: none

stages:          # List of stages for jobs, and their order of execution
  - build
  - test
  - deploy

build-api:
  stage: build
  tags: 
    - devsecops2
  script:
    - echo "Build cyrus api container" 
    - rm -rf secrets
    - mkdir secrets
    - echo $db_name > secrets/db_name.txt
    - echo $db_password > secrets/db_password.txt
    - echo $dev_db_name > secrets/dev_db_name.txt
    - echo $dev_db_password > secrets/dev_db_password.txt
    - echo $db_username > secrets/db_username.txt
    - echo $dev_django_secret_key > secrets/dev_django_secret_key.txt
    - echo $prod_django_secret_key > secrets/prod_django_secret_key.txt
    - echo $pgadmin_pwd > secrets/pgadmin_pwd.txt
    - echo $sso_client_id > secrets/sso_client_id.txt
    - echo $sso_client_secret > secrets/sso_client_secret.txt
    - echo $sso_realm > secrets/sso_realm.txt
    - echo $sso_well_known > secrets/sso_well_known.txt
    - echo $kiwi_broker > secrets/kiwi_broker.txt
    - echo $openvas_broker > secrets/openvas_broker.txt
    - echo PGADMIN_DEFAULT_EMAIL=$pgadmin > .env
    - echo DEBUG=$debug > api/.env
    - echo DB_HOST=$db_host >> api/.env
    - echo DB_PORT=$db_port >> api/.env
    - echo $pgadmin_json > pgadmin/servers.json
    - echo $pgpass > pgadmin/pgpass
    - DOCKER_BUILDKIT=0 docker-compose build
  # rules:
  #   - if: '$db_name != null'
  #     changes:
  #     - api/*

test-api:
  stage: test
  tags: 
    - devsecops2
  script:
    - echo "Build cyrus api container and test if running" 
    - rm -rf secrets
    - mkdir secrets
    - echo $db_name > secrets/db_name.txt
    - echo $db_password > secrets/db_password.txt
    - echo $dev_db_name > secrets/dev_db_name.txt
    - echo $dev_db_password > secrets/dev_db_password.txt
    - echo $db_username > secrets/db_username.txt
    - echo $dev_django_secret_key > secrets/dev_django_secret_key.txt
    - echo $prod_django_secret_key > secrets/prod_django_secret_key.txt
    - echo $pgadmin_pwd > secrets/pgadmin_pwd.txt
    - echo $db_username > secrets/db_username.txt
    - echo $kiwi_db_user > secrets/db_kiwi_username.txt
    - echo $kiwi_db_pass > secrets/db_kiwi_password.txt
    - echo $kiwi_db_name > secrets/db_kiwi_name.txt
    - echo $sso_client_id > secrets/sso_client_id.txt
    - echo $sso_client_secret > secrets/sso_client_secret.txt
    - echo $sso_realm > secrets/sso_realm.txt
    - echo $sso_well_known > secrets/sso_well_known.txt
    - echo $kiwi_broker > secrets/kiwi_broker.txt
    - echo $openvas_broker > secrets/openvas_broker.txt
    - echo PGADMIN_DEFAULT_EMAIL=$pgadmin > .env
    - echo DEBUG=$debug > api/.env
    - echo DB_HOST=$db_host >> api/.env
    - echo DB_PORT=$db_port >> api/.env
    - echo $pgadmin_json > pgadmin/servers.json
    - echo $pgpass > pgadmin/pgpass
    - docker-compose down
    - docker network rm practis-network
    # - |
    #   if docker network ls | grep -q practis-network; then
    #     echo "The docker network already exists. Skipping creation..."
    #   else
    #     echo "the docker network does not exist. Creating it..."
    #     docker network create practis-network
    #     echo "The docker network has been created successfully..."
    #   fi
    # - docker network rm practis-network
    - docker network create practis-network
    - DOCKER_BUILDKIT=0 docker-compose build
    - docker-compose up -d --remove-orphans
    - sleep 20
    - curl -v http://127.0.0.1:8000/api/riskanalysis/
    - sleep 5
    - docker-compose down
  # rules:
  #   - if: '$db_name != null'
  #     changes:
  #     - api/*

deploy-api:
  stage: deploy
  tags: 
    - devsecops2
  before_script:
    - mkdir -p ~/.ssh
    - echo -----BEGIN OPENSSH PRIVATE KEY----- > ~/.ssh/crescendo-dev
    - echo $crescendodevpvtkey >> ~/.ssh/crescendo-dev
    - echo -----END OPENSSH PRIVATE KEY----- >> ~/.ssh/crescendo-dev
    - chmod 600 ~/.ssh/crescendo-dev
    - echo ************ >> ~/.ssh/known_hosts
    - chmod 600 ~/.ssh/known_hosts
    - echo Host ************ > ~/.ssh/config
    - echo   Hostname ************ >> ~/.ssh/config
    - echo   User crs-admin >> ~/.ssh/config
    - echo "StrictHostKeyChecking no" >> ~/.ssh/config
  script:
    - echo "Build cyrus api container"
    - rm -rf kiwi
    - mkdir -p kiwi && cd kiwi
    # # - echo "Repository already exists. Performing git pull..."
    # # - git pull
    - |
      if test -d "practis-kiwi/.git"; then
        echo "Repository already exists. Performing git pull..."
        cd practis-kiwi
        git pull
      else
        echo "Repository does not exist. Cloning repository..."
        git credential-cache exit
        git clone ${kiwi_token}
        cd practis-kiwi
      fi
    # - git clone https://$git_user:$<EMAIL>/crescendo/practis-kiwi.git
    - mkdir -p PractisKiwi/secrets
    - echo $kiwi_user > PractisKiwi/secrets/kiwi_user.txt
    - echo $kiwi_pass > PractisKiwi/secrets/kiwi_pass.txt
    - echo $kiwi_server > PractisKiwi/secrets/kiwi_server.txt
    - echo $kiwi_django_secret_key > PractisKiwi/secrets/django_secret_key.txt
    - sed -i "s/--host--/$kiwi_db_host/g" kiwi/docker-compose-kiwi.yml
    - sed -i "s/--port--/$kiwi_db_port/g" kiwi/docker-compose-kiwi.yml
    - sed -i "s/--username--/$kiwi_db_user/g" kiwi/docker-compose-kiwi.yml
    - sed -i "s/--password--/$kiwi_db_pass/g" kiwi/docker-compose-kiwi.yml
    - sed -i "s/--database--/$kiwi_db_name/g" kiwi/docker-compose-kiwi.yml
    - mkdir kiwi/secrets
    - echo $kiwi_db_name > kiwi/secrets/db_name.txt
    - echo $kiwi_db_pass > kiwi/secrets/db_pass.txt
    - echo $kiwi_db_user > kiwi/secrets/db_user.txt
    - echo DEBUG=$debug > PractisKiwi/api/.env
    - cd ../..
    - mkdir secrets
    - echo $db_name > secrets/db_name.txt
    - echo $db_password > secrets/db_password.txt
    - echo $dev_db_name > secrets/dev_db_name.txt
    - echo $dev_db_password > secrets/dev_db_password.txt
    - echo $db_username > secrets/db_username.txt
    - echo $kiwi_db_user > secrets/db_kiwi_username.txt
    - echo $kiwi_db_pass > secrets/db_kiwi_password.txt
    - echo $kiwi_db_name > secrets/db_kiwi_name.txt
    - echo $dev_django_secret_key > secrets/dev_django_secret_key.txt
    - echo $prod_django_secret_key > secrets/prod_django_secret_key.txt
    - echo $kiwi_broker > secrets/kiwi_broker.txt
    - echo $openvas_broker > secrets/openvas_broker.txt
    - echo $pgadmin_pwd > secrets/pgadmin_pwd.txt
    - echo $sso_client_id > secrets/sso_client_id.txt
    - echo $sso_client_secret > secrets/sso_client_secret.txt
    - echo $sso_realm > secrets/sso_realm.txt
    - echo $sso_well_known > secrets/sso_well_known.txt
    - echo PGADMIN_DEFAULT_EMAIL=$pgadmin > .env
    - echo DEBUG=$debug > api/.env
    - echo DB_HOST=$db_host >> api/.env
    - echo DB_PORT=$db_port >> api/.env
    - echo $pgadmin_json > pgadmin/servers.json
    - echo $pgpass > pgadmin/pgpass
    - |
      if ssh -i ~/.ssh/crescendo-dev ************ "test -f test/docker-compose.yml"; then
        echo "docker-compose.yml file exists. Performing docker-compose down..."
        ssh -i ~/.ssh/crescendo-dev ************ "cd test && docker-compose down"
      else
        echo "docker-compose.yml file does not exist. ignoring the docker-compose down command..."
      fi
    - |
      if ssh -i ~/.ssh/crescendo-dev ************ "test -f test/kiwi/practis-kiwi/docker-compose.yml"; then
        echo "docker-compose.yml file exists. Performing docker-compose down..."
        ssh -i ~/.ssh/crescendo-dev ************ "cd test/kiwi/practis-kiwi && docker-compose down"
      else
        echo "docker-compose.yml file does not exist. ignoring the docker-compose down command..."
      fi
    - ssh -i ~/.ssh/crescendo-dev ************ "rm -Rf test && mkdir -p test"
    - scp -pr -i ~/.ssh/crescendo-dev ./{*,.env,api/.env} ************:~/test/
    - ssh -i ~/.ssh/crescendo-dev ************ "cd test/kiwi/practis-kiwi && docker compose -f docker-compose.yml build && docker compose -f docker-compose.yml up -d"
    - |
      if ssh -i ~/.ssh/crescendo-dev ************ "docker network ls | grep -q practis-network"; then
        echo "The docker network already exists. Skipping creation..."
      else
        echo "the docker network does not exist. Creating it..."
        ssh -i ~/.ssh/crescendo-dev ************ "docker network create practis-network"
        echo "The docker network has been created successfully..."
      fi
    - ssh -i ~/.ssh/crescendo-dev ************ "cd test && docker compose build && docker compose up -d --remove-orphans"
    - ssh -i ~/.ssh/crescendo-dev ************ "cd test/kiwi/practis-kiwi && docker compose build && docker compose up -d --remove-orphans"

  #rules:
  #  - if: '$db_name != null'
  #    changes:
  #    - api/*


