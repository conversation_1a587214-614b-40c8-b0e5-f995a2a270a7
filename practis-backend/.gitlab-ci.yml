# Define default settings for all jobs
default:
  tags:
    - ci  # Use runners tagged with 'ci'

# Include external templates for different job configurations
include:
  - project: 'cyrus/practis/ci-templates'
    file: '/python-jobs/static_analysis/vulture.tpl.yml'  # Vulture dead code analyzer template
  - project: 'cyrus/practis/ci-templates'
    file: '/python-jobs/type_checking/mypy.tpl.yml'  # Mypy type checker template
  - project: 'cyrus/practis/ci-templates'
    file: '/python-jobs/security/bandit.tpl.yml'  # Bandit security linter template
  - project: 'cyrus/practis/ci-templates'
    file: '/python-jobs/license_management/pylic.tpl.yml'  # Pylic license checker template
  - project: 'cyrus/practis/ci-templates'
    file: '/python-jobs/security/safety.tpl.yml'  # Safety security checker template
  - project: 'cyrus/practis/ci-templates'
    file: '/other-jobs/sonarqube/sonarqube.tpl.yml'  # SonarQube scanner template
  - project: 'cyrus/practis/ci-templates'
    file: '/docker-jobs/image_build/docker_build.tpl.yml'  # Docker build template
  - project: 'cyrus/practis/ci-templates'
    file: '/docker-jobs/security_scanning/grype.tpl.yml'  # Docker build template
  - project: 'cyrus/practis/ci-templates'
    file: '/nexus-jobs/nexus_upload.tpl.yml'  # Docker build template

# Define the stages of the pipeline
stages:
  - quality  # Code quality checks stage
  - security  # Security checks stage
  - sonarqube  # SonarQube analysis stage
  - build  # Docker image build stage

# Set pipeline-wide variables
variables:
  API_IMAGE_NAME: practis-api  # Name of the Docker image to build
  DOCKERFILE_PATH: ./api/Dockerfile  # Path to the Dockerfile
  PRODUCTION_ENV: false  # Indicates if the environment is production

vulture:
  stage: quality
  extends: .vulture  # Extend from the vulture dead code analyzer template
  when: always

mypy:
  stage: quality
  extends: .mypy  # Extend from the mypy type checker template
  when: always

pytest_&_pylint:
  stage: quality
  script:
    - echo "Build cyrus api container" 
    - rm -rf secrets
    - mkdir secrets
    - echo $db_name > secrets/db_name.txt
    - echo $db_password > secrets/db_password.txt
    - echo $dev_db_name > secrets/dev_db_name.txt
    - echo $dev_db_password > secrets/dev_db_password.txt
    - echo $db_username > secrets/db_username.txt
    - echo $dev_django_secret_key > secrets/dev_django_secret_key.txt
    - echo $prod_django_secret_key > secrets/prod_django_secret_key.txt
    - echo $pgadmin_pwd > secrets/pgadmin_pwd.txt
    - echo $db_username > secrets/db_username.txt
    - echo $kiwi_db_user > secrets/db_kiwi_username.txt
    - echo $kiwi_db_pass > secrets/db_kiwi_password.txt
    - echo $kiwi_db_name > secrets/db_kiwi_name.txt
    - echo $sso_client_id > secrets/sso_client_id.txt
    - echo $sso_client_secret > secrets/sso_client_secret.txt
    - echo $sso_realm > secrets/sso_realm.txt
    - echo $sso_well_known > secrets/sso_well_known.txt
    - echo $kiwi_broker > secrets/kiwi_broker.txt
    - echo $openvas_broker > secrets/openvas_broker.txt
    - echo DEBUG=$debug > api/.env
    - echo DB_HOST=$db_host >> api/.env
    - echo DB_PORT=$db_port >> api/.env
    - docker-compose -f app.test.build.yml up --build --abort-on-container-exit --remove-orphans

pylic:
  stage: security
  extends: .pylic  # Extend from the pylic license checker template
  when: always

bandit:
  stage: security
  extends: .bandit  # Extend from the bandit security linter template
  when: always

safety:
  stage: security
  extends: .safety  # Extend from the safety security checker template
  when: always

sonarqube-check:
  stage: sonarqube
  extends: .sonarqube  # Extend from the SonarQube scanner template
  when: always  # Run this job always, even if previous jobs fail

build:
  stage: build
  extends: .docker_build  # Extend from the Docker build template

grype:
  stage: build
  extends: .grype

nexus_upload:
  stage: build
  extends: .push_nexus


# build-api:
#   stage: build
#   script:
#     - echo "Build cyrus api container" 
#     - mkdir secrets
#     - echo $db_name > secrets/db_name.txt
#     - echo $db_password > secrets/db_password.txt
#     - echo $dev_db_name > secrets/dev_db_name.txt
#     - echo $dev_db_password > secrets/dev_db_password.txt
#     - echo $db_username > secrets/db_username.txt
#     - echo $dev_django_secret_key > secrets/dev_django_secret_key.txt
#     - echo $prod_django_secret_key > secrets/prod_django_secret_key.txt
#     - echo $pgadmin_pwd > secrets/pgadmin_pwd.txt
#     - echo $sso_client_id > secrets/sso_client_id.txt
#     - echo $sso_client_secret > secrets/sso_client_secret.txt
#     - echo $sso_realm > secrets/sso_realm.txt
#     - echo $sso_well_known > secrets/sso_well_known.txt
#     - echo $kiwi_broker > secrets/kiwi_broker.txt
#     - echo $openvas_broker > secrets/openvas_broker.txt
#     - echo PGADMIN_DEFAULT_EMAIL=$pgadmin > .env
#     - echo DEBUG=$debug > api/.env
#     - echo DB_HOST=$db_host >> api/.env
#     - echo DB_PORT=$db_port >> api/.env
#     - echo $pgadmin_json > pgadmin/servers.json
#     - echo $pgpass > pgadmin/pgpass
#     - DOCKER_BUILDKIT=0 docker-compose build
#   rules:
#     - if: '$db_name != null'
#       changes:
#       - api/*

# test-api:
#   stage: test
#   script:
#     - echo "Build cyrus api container and test if running" 
#     - mkdir secrets
#     - echo $db_name > secrets/db_name.txt
#     - echo $db_password > secrets/db_password.txt
#     - echo $dev_db_name > secrets/dev_db_name.txt
#     - echo $dev_db_password > secrets/dev_db_password.txt
#     - echo $db_username > secrets/db_username.txt
#     - echo $dev_django_secret_key > secrets/dev_django_secret_key.txt
#     - echo $prod_django_secret_key > secrets/prod_django_secret_key.txt
#     - echo $pgadmin_pwd > secrets/pgadmin_pwd.txt
#     - echo $sso_client_id > secrets/sso_client_id.txt
#     - echo $sso_client_secret > secrets/sso_client_secret.txt
#     - echo $sso_realm > secrets/sso_realm.txt
#     - echo $sso_well_known > secrets/sso_well_known.txt
#     - echo $kiwi_broker > secrets/kiwi_broker.txt
#     - echo $openvas_broker > secrets/openvas_broker.txt
#     - echo PGADMIN_DEFAULT_EMAIL=$pgadmin > .env
#     - echo DEBUG=$debug > api/.env
#     - echo DB_HOST=$db_host >> api/.env
#     - echo DB_PORT=$db_port >> api/.env
#     - echo $pgadmin_json > pgadmin/servers.json
#     - echo $pgpass > pgadmin/pgpass
#     - docker-compose down
#     - DOCKER_BUILDKIT=0 docker-compose build
#     - docker-compose up -d
#     - sleep 20
#     - curl -v http://127.0.0.1:8000/api/riskanalysis/
#     - sleep 5
#     - docker-compose down
#   rules:
#     - if: '$db_name != null'
#       changes:
#       - api/*

# deploy-api:
#   stage: deploy
#   tags: 
#     - devsecops2
#   before_script:
#     - mkdir -p ~/.ssh
#     - echo -----BEGIN OPENSSH PRIVATE KEY----- > ~/.ssh/crescendo-dev
#     - echo $crescendodevpvtkey >> ~/.ssh/crescendo-dev
#     - echo -----END OPENSSH PRIVATE KEY----- >> ~/.ssh/crescendo-dev
#     - chmod 600 ~/.ssh/crescendo-dev
#     - echo ************ >> ~/.ssh/known_hosts
#     - chmod 600 ~/.ssh/known_hosts
#     - echo Host ************ > ~/.ssh/config
#     - echo   Hostname ************ >> ~/.ssh/config
#     - echo   User crs-admin >> ~/.ssh/config
#     - echo "StrictHostKeyChecking no" >> ~/.ssh/config
#   script:
#     - echo "Build cyrus api container"
#     - mkdir -p kiwi && cd kiwi
#     - |
#       if [ -d "practis-kiwi/.git" ]; then
#         echo "Repository already exists. Performing git pull..."
#         cd practis-kiwi
#         git pull
#       else
#         echo "Repository does not exist. Cloning repository..."
#         git clone https://$git_user:$<EMAIL>/crescendo/cyrus-kiwi.git
#         cd practis-kiwi
#       fi
#     - mkdir -p PractisKiwi/secrets
#     - echo $kiwi_user > PractisKiwi/secrets/kiwi_user.txt
#     - echo $kiwi_pass > PractisKiwi/secrets/kiwi_pass.txt
#     - echo $kiwi_server > PractisKiwi/secrets/kiwi_server.txt
#     - echo $kiwi_django_secret_key > PractisKiwi/secrets/django_secret_key.txt
#     - sed -i "s/--host--/$kiwi_db_host/g" kiwi/docker-compose-kiwi.yml
#     - sed -i "s/--port--/$kiwi_db_port/g" kiwi/docker-compose-kiwi.yml
#     - sed -i "s/--username--/$kiwi_db_user/g" kiwi/docker-compose-kiwi.yml
#     - sed -i "s/--password--/$kiwi_db_pass/g" kiwi/docker-compose-kiwi.yml
#     - sed -i "s/--database--/$kiwi_db_name/g" kiwi/docker-compose-kiwi.yml
#     - mkdir kiwi/secrets
#     - echo $kiwi_db_name > kiwi/secrets/db_name.txt
#     - echo $kiwi_db_pass > kiwi/secrets/db_pass.txt
#     - echo $kiwi_db_user > kiwi/secrets/db_user.txt
#     - DOCKER_BUILDKIT=0 docker-compose -f docker-compose.yml build && docker-compose -f docker-compose.yml up -d
#     - echo DEBUG=$debug > PractisKiwi/api/.env
#     - cd ../..
#     - mkdir secrets
#     - echo $db_name > secrets/db_name.txt
#     - echo $db_password > secrets/db_password.txt
#     - echo $dev_db_name > secrets/dev_db_name.txt
#     - echo $dev_db_password > secrets/dev_db_password.txt
#     - echo $db_username > secrets/db_username.txt
#     - echo $dev_django_secret_key > secrets/dev_django_secret_key.txt
#     - echo $prod_django_secret_key > secrets/prod_django_secret_key.txt
#     - echo $kiwi_broker > secrets/kiwi_broker.txt
#     - echo $openvas_broker > secrets/openvas_broker.txt
#     - echo $pgadmin_pwd > secrets/pgadmin_pwd.txt
#     - echo $sso_client_id > secrets/sso_client_id.txt
#     - echo $sso_client_secret > secrets/sso_client_secret.txt
#     - echo $sso_realm > secrets/sso_realm.txt
#     - echo $sso_well_known > secrets/sso_well_known.txt
#     - echo PGADMIN_DEFAULT_EMAIL=$pgadmin > .env
#     - echo DEBUG=$debug > api/.env
#     - echo DB_HOST=$db_host >> api/.env
#     - echo DB_PORT=$db_port >> api/.env
#     - echo $pgadmin_json > pgadmin/servers.json
#     - echo $pgpass > pgadmin/pgpass
#     - |
#       if ssh -i ~/.ssh/crescendo-dev ************ "test -f test/docker-compose.yml"; then
#         echo "docker-compose.yml file exists. Performing docker-compose down..."
#         ssh -i ~/.ssh/crescendo-dev ************ "cd test && docker-compose down"
#       else
#         echo "docker-compose.yml file does not exist. ignoring the docker-compose down command..."
#       fi
#     - |
#       if ssh -i ~/.ssh/crescendo-dev ************ "test -f test/kiwi/cyrus-kiwi/docker-compose.yml"; then
#         echo "docker-compose.yml file exists. Performing docker-compose down..."
#         # ssh -i ~/.ssh/crescendo-dev ************ "cd test/kiwi/cyrus-kiwi && docker-compose down"
#       else
#         echo "docker-compose.yml file does not exist. ignoring the docker-compose down command..."
#       fi
#     - ssh -i ~/.ssh/crescendo-dev ************ "rm -Rf test && mkdir -p test"
#     - scp -pr -i ~/.ssh/crescendo-dev ./{*,.env,api/.env} ************:~/test/
#     # - ssh -i ~/.ssh/crescendo-dev ************ "cd test/kiwi/cyrus-kiwi && docker compose -f docker-compose.yml build && docker compose -f docker-compose.yml up -d"
#     - ssh -i ~/.ssh/crescendo-dev ************ "cd test && docker compose build && docker compose up -d"
  #rules:
  #  - if: '$db_name != null'
  #    changes:
  #    - api/*