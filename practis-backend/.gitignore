.env
.vscode/
.pytest_cache/
api/api/migrations/__pycache__/
api/api/savefile.py
api/practis/db.sqlite3
api/api/__pycache__/
api/practis/__pycache__/
api/practis/.pytest_cache/
api/__pycache__/
api/.pytest_cache/
api/tests/__pycache__/
api/tests/api/__pycache__/
api/tests/api/endpoints/__pycache__/
api/practis/settings/__pycache__/
api/practis/settings/settings.py
api/practis/tests/api/endpoints/__pycache__/
api/practis/tests/api/__pycache__/
api/practis/db.sqlite3
practis-env
db_password.txt
secrets
pgadmin/pgpass
pgadmin/servers.json
cyrus
roles/.DS_Store
roles/api/.DS_Store
roles/api/files/.DS_Store
roles/compose/.DS_Store
roles/install-docker/.DS_Store
roles/pgadmin/.DS_Store
roles/postgres/.DS_Store
api/api/templatetags/__pycache__/dashboard_helpers.cpython-312.pyc
api/api/templatetags/__pycache__/__init__.cpython-312.pyc
api/practis/db1.sqlite3
api/.coverage 2
api/.DS_Store
.DS_Store
api/.coverage*
api/media/
api/tests/api/models/__pycache__/test_models.cpython-312-pytest-7.4.3.pyc
api/report.txt
api/practis/settings/settings.py
.coverage
cov/
api/practis/practis_api.log
api/api/auth/__pycache__/
practis-api/manage.py
