# Gitlab CI/CD pipeline

## Introduction
A GitLab CI (Continuous Integration) / CD (Continuous Deployment) pipeline is a series of automated steps configured in a YAML file (.gitlab-ci.yml) that enable code to be built, tested and deployed continuously.

The pipeline automates a number of processes, such as compiling code, building its Docker image and testing code quality and security. It allows you to integrate, validate and regularly check the code you write.

All the jobs carried out by the pipeline come from the [CI-Template project](https://git.cetic.be/crescendo/ci-templates/-/tree/develop?ref_type=heads), the aim of which is to bring together the elements of pipelines in order to simplify modifications or installations.

The pipeline is executed by a simple Git push on the API project.

### Tags (and runner's machine)
The pipeline needs a runner on a machine with **Docker**, **black** (python library) and **isort** (python library) installed. The runner's machine must be in CETIC's internal network to access a Nexus registry and the Sonarqube instance. 

[Docker](https://www.docker.com/)  /  [Black](https://pypi.org/project/black/)  /  [Isort](https://pycqa.github.io/isort/)

Currently, it works on runners with the ``ci`` tag. The pipeline was developed on machine #360 (UaMyQfLqu).

## Stages
The pipeline is divided into four different stages: ``format``, ``quality``, ``security``, ``sonarqube``, ``build``. The separation has been made according to the role of the actions carried out, making it easier to read the results.

Each stage has a specific role.
* ``format`` where the code format is checked.
* ``quality`` where the quality of the project's source code is tested.
* ``security`` where the security of the project is checked.
* ``sonarqube`` where a sonar scan is carried out.
* ``build`` where the project's Docker image is built and pushed.


#### Format's stage
The course involves running two tools that format the code: [Black](https://pypi.org/project/black/) and [Isort](https://pypi.org/project/isort/). Black focuses on formatting errors, whereas Isort concentrates on sort imports. These tools are run by including two jobs to the pipeline, ``.black`` and ``.isort``, which are defined in the CI-Template project.

[.black job](https://git.cetic.be/crescendo/ci-templates/-/blob/develop/python-ci-templates/jobs-templates/format/black.tpl.yml?ref_type=heads) | [.isort job](https://git.cetic.be/crescendo/ci-templates/-/blob/develop/python-ci-templates/jobs-templates/format/isort.tpl.yml?ref_type=heads)

**If the code is not correctly formatted, the pipeline will be blocked** until the errors are corrected. A command to be performed at the root of your project will be indicated.

More information here : [Format's jobs template](https://git.cetic.be/crescendo/ci-templates/-/blob/develop/python-ci-templates/jobs-templates/format/README.md?ref_type=heads)

> The format stage may be removed from the pipeline because formatting code at this level is of little interest (no feedback on the code itself). Perhaps formatting should be checked before committing?

#### Quality's stage
The aim of the quality's stage is to ensure that the code complies with quality standards and functions correctly. It comprises several stages and tools dedicated to different code quality checks.

* [Pylint tools](https://pypi.org/project/pylint/) : a code analysis tool that checks Python code for compliance with PEP 8 style standards and detects potential errors in the code. The analysis is launched by the ``.pylint`` job defined in the CI-Template project. | [.pylint job](https://git.cetic.be/crescendo/ci-templates/-/blob/develop/python-ci-templates/jobs-templates/test/pylint.tpl.yml?ref_type=heads)
* [Vulture tools]() : a tool that finds unused parts of code in a Python project. It helps keep code clean by removing unused functions, variables and classes. The searched is done by the ``.vulture`` job defined in the CI-Template project. | [.vulture job](https://git.cetic.be/crescendo/ci-templates/-/blob/develop/python-ci-templates/jobs-templates/test/vulture.tpl.yml?ref_type=heads)
* [Mypy](https://pypi.org/project/mypy/) : a type checker for Python that analyses code for type inconsistencies. This prevents data type errors before execution. The analysis is launched by the ``.mypy`` job defined in the CI-Template project. | [.mypy job](https://git.cetic.be/crescendo/ci-templates/-/blob/develop/python-ci-templates/jobs-templates/test/mypy.tpl.yml?ref_type=heads)

The jobs described above are performed in a Docker container whose image must be retrieved from the Nexus registry. This is why, for the pipeline to work properly, **the runner machine must be in CETIC's internal network** and the following variables must be defined with the pipeline's variable environment : ``NEXUS_URL``, ``NEXUS_REPO``, ``NEXUS_USERNAME``, ``NEXUS_PASSWORD`` and ``CI_IMAGE_NAME``.

More information here : [Quality's jobs template](https://git.cetic.be/crescendo/ci-templates/-/blob/develop/python-ci-templates/jobs-templates/test/README.md?ref_type=heads)

##### .pytest job
The .pytest job has a different configuration to the other jobs because it does not depend on a template. Its execution requires the construction of the project image.

In the ``.gitlab-ci.yml``, the .pytest job will pass the pipeline environment variables to a docker-compose which will build the project. This docker-compose, ``app.test.build.yml`` (located at the root of the project), will then build the project image with the environment variables from the file ``ci/Dockerfile.pytest``. This file installs all the project dependencies and the [Pytest](https://docs.pytest.org/en/8.2.x/) tool, before executing the ``ci/pytest.sh`` script. This script uses unit tests (``/api/tests/``) to check that the code coverage is greater than 60%.

> A production version has been initiated but not activated, enabling the success condition to be raised to 90% depending on the PRODUCTION_ENV variable.

#### Security's stage
The security course aims to identify and correct potential vulnerabilities in the code and dependencies before they are put into production. It includes several specialised tools for detecting security flaws.

* [Pylic](https://pypi.org/project/pylic/) : a tool for checking dependency licences. It ensures that all the libraries and packages used in the project comply with the defined licence policies, which is crucial for avoiding legal compliance problems. Verification is carried out by ``.pylic`` jobs defined in the CI-Template project. | [.pylic job](https://git.cetic.be/crescendo/ci-templates/-/blob/develop/python-ci-templates/jobs-templates/test/pylic.tpl.yml?ref_type=heads)
Pylic is associated to the ``./pyproject.toml`` configuration file. It contains the configuration of the tool, including the licences accepted.
* [Bandit](https://pypi.org/project/bandit/) : a tool to analyses Python source code to find common security flaws. It scans the code for vulnerabilities such as code injection, misconfigurations and unsafe coding practices. The job launching the analysis is ``.bandit``, defined in the CI-Template project. | [.bandit job](https://git.cetic.be/crescendo/ci-templates/-/blob/develop/python-ci-templates/jobs-templates/test/bandit.tpl.yml?ref_type=heads)
Bandit tool is associated to a configuration file ``./bandit.yaml``.
* [Safety](https://pypi.org/project/safety/) : a tool to checks the project's dependencies against a database of known vulnerabilities. It identifies package versions with security holes and recommends updates to correct them. Which is launched by the ``.safety`` job, definied in the CI-Template projetc. | [.safety job](https://git.cetic.be/crescendo/ci-templates/-/blob/develop/python-ci-templates/jobs-templates/test/safety.tpl.yml?ref_type=heads)
For the test to work, the ``SAFETY_API_KEY`` variable must be set in the environment variables. This variable is given when a Safety CLI account is created.

> To use safety, a free account must be created on Safety CLI. This limits usage to 100 per month and is not a permanent situation as it uses a personal account (for the moment).

As the Quality's stage, the jobs described are performed in a Docker container whose image must be retrieved from the Nexus registry. This is why, for the pipeline to work properly, **the runner machine must be in CETIC's internal network** and the following variables must be defined with the pipeline's variable environment : ``NEXUS_URL``, ``NEXUS_REPO``, ``NEXUS_USERNAME``, ``NEXUS_PASSWORD`` and ``CI_IMAGE_NAME``.

More information here : [Security's jobs template](https://git.cetic.be/crescendo/ci-templates/-/blob/develop/python-ci-templates/jobs-templates/test/README.md?ref_type=heads)

#### SonarQube's stage
As its name suggests, this course is dedicated to the use of Sonarqube, which includes both quality and safety checks. It consists of a single job ``.sonarqube-check``, also defined in the CI-Template project.`| [.sonarqube job](https://git.cetic.be/crescendo/ci-templates/-/blob/develop/python-ci-templates/jobs-templates/test/sonarqube.tpl.yml?ref_type=heads)

This job runs a docker container with the sonarsource/sonar-scanner-cli image and the project source code mounted in volume to perform a complete scan of the project. The pipeline informs us whether or not the scan was successful and the results are sent to the Sonarqube instance : http://sonarqube.int.cetic.be/dashboard?id=cyrus-api-ci

For the scan to connect to the instance, two environment variables must be added to the pipeline variable: ``SONAR_HOST_URL`` and ``SONAR_TOKEN``.

More information here : [SonarQube's jobs template](https://git.cetic.be/crescendo/ci-templates/-/blob/develop/python-ci-templates/jobs-templates/test/README.md?ref_type=heads#sonarqubetplyml)

#### Build's stage
The final stage of the CI/CD pipeline consists of creating the Docker image of the project. To do this, we use the ``.docker_build`` job defined in the CI-Template project. | [.docker_build job](https://git.cetic.be/crescendo/ci-templates/-/blob/develop/python-ci-templates/jobs-templates/build/docker_build.tpl.yml?ref_type=heads)

The job will build the Docker image from the Dockerfile supplied by the ``DOCKERFILE_PATH`` variable and with the name given by the ``IMAGE_NAME`` variable, both defined in the ``.gitlab-ci.yml`` file. Once built, the image is pushed into the Nexus registry where it can be retrieved by other projects.

Link to the Nexus registry : https://nexus.cetic.be/#browse/welcome

More information here : [Build's jobs template](https://git.cetic.be/crescendo/ci-templates/-/blob/develop/python-ci-templates/jobs-templates/build/README.md?ref_type=heads)



