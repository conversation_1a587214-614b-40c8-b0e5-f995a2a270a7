<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="23.50cm" height="24.82cm" viewBox="0 0 888 938">
<title></title>
<desc>Created with Enterprise Architect (Build: 1628) 2</desc>

<g style="fill:#FFFFFF;fill-opacity:1.00;">
	 <rect x="0" y="0" width="888" height="938" shape-rendering="auto"/>
</g><g style="stroke-width:1;stroke-linecap:square;stroke-linejoin:miter; fill:#000000;fill-opacity:0.00; stroke:#000000; stroke-opacity:1.00">
  <path d="M 6 6 L 6 932 L 882 932 L 882 6 L 6 6" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:square;stroke-linejoin:miter; fill:#FFFFFF;fill-opacity:1.00; stroke:#000000; stroke-opacity:1.00">
  <polygon points="6 26 137 26 150 12 150 6 6 6 6 26" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:square;stroke-linejoin:miter; fill:#000000;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="11.00" y="19.00" textLength="124" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#000000;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 11.00 19.00)">deployment Cyrus Deployment</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F0FFFF;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="341 40 539 40 531 48 333 48 341 40" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#AAC2D2;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="539 40 539 425 531 433 531 48 539 40" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#CDE5F5;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="333 48 531 48 531 433 333 433 333 48" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="418.00" y="67.00" textLength="28" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 418.00 67.00)">docker</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#000000;fill-opacity:0.00; stroke:#9A8484; stroke-opacity:1.00">
  <path d="M 333 75 L 530 75" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="353" y="91" width="132" height="225" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="389.00" y="110.00" textLength="39" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 389.00 110.00)">Cyrus API</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#000000;fill-opacity:0.00; stroke:#9A8484; stroke-opacity:1.00">
  <path d="M 353 118 L 484 118" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="469" y="96" width="11" height="17" rx="0.00" shape-rendering="auto"  />
 <rect x="466" y="98" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="466" y="105" width="7" height="5" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F0FFFF;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="651 44 838 44 830 52 643 52 651 44" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#AAC2D2;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="838 44 838 422 830 430 830 52 838 44" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#CDE5F5;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="643 52 830 52 830 430 643 430 643 52" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="722.00" y="71.00" textLength="28" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 722.00 71.00)">docker</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#000000;fill-opacity:0.00; stroke:#9A8484; stroke-opacity:1.00">
  <path d="M 643 79 L 829 79" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F0FFFF;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="381 135 463 135 455 143 373 143 381 135" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#AAC2D2;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="463 135 463 172 455 180 455 143 463 135" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#CDE5F5;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="373 143 455 143 455 180 373 180 373 143" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="396.00" y="159.00" textLength="35" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 396.00 159.00)">«image»</text>
	<text x="390.00" y="172.00" textLength="48" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 390.00 172.00)">Python 3.11</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F0FFFF;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="381 188 463 188 455 196 373 196 381 188" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#AAC2D2;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="463 188 463 231 455 239 455 196 463 188" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#CDE5F5;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="373 196 455 196 455 239 373 239 373 196" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="396.00" y="212.00" textLength="35" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 396.00 212.00)">«image»</text>
	<text x="386.00" y="225.00" textLength="56" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 386.00 225.00)">postgres:16.1</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F0FFFF;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="437 364 519 364 511 372 429 372 437 364" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#AAC2D2;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="519 364 519 401 511 409 511 372 519 364" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#CDE5F5;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="429 372 511 372 511 409 429 409 429 372" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="450.00" y="388.00" textLength="40" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 450.00 388.00)">«volume»</text>
	<text x="436.00" y="401.00" textLength="68" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 436.00 401.00)">Database/Media</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F0FFFF;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="381 248 463 248 455 256 373 256 381 248" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#AAC2D2;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="463 248 463 287 455 295 455 256 463 248" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#CDE5F5;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="373 256 455 256 455 295 373 295 373 256" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="396.00" y="272.00" textLength="35" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 396.00 272.00)">«image»</text>
	<text x="392.00" y="285.00" textLength="43" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 392.00 285.00)">nginx:1.25</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="674" y="91" width="126" height="108" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="707.00" y="110.00" textLength="39" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 707.00 110.00)">OpenVAS</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#000000;fill-opacity:0.00; stroke:#9A8484; stroke-opacity:1.00">
  <path d="M 674 118 L 799 118" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="784" y="96" width="11" height="17" rx="0.00" shape-rendering="auto"  />
 <rect x="781" y="98" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="781" y="105" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="676" y="211" width="122" height="131" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="692.00" y="230.00" textLength="70" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 692.00 230.00)">OpenVAS Broker</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#000000;fill-opacity:0.00; stroke:#9A8484; stroke-opacity:1.00">
  <path d="M 676 238 L 797 238" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="782" y="216" width="11" height="17" rx="0.00" shape-rendering="auto"  />
 <rect x="779" y="218" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="779" y="225" width="7" height="5" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F0FFFF;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="701 253 783 253 775 261 693 261 701 253" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#AAC2D2;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="783 253 783 294 775 302 775 261 783 253" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#CDE5F5;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="693 261 775 261 775 302 693 302 693 261" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="716.00" y="277.00" textLength="35" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 716.00 277.00)">«image»</text>
	<text x="709.00" y="290.00" textLength="49" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 709.00 290.00)">python:3.11</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F0FFFF;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="702 132 784 132 776 140 694 140 702 132" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#AAC2D2;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="784 132 784 169 776 177 776 140 784 132" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#CDE5F5;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="694 140 776 140 776 177 694 177 694 140" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="717.00" y="156.00" textLength="35" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 717.00 156.00)">«image»</text>
	<text x="713.00" y="169.00" textLength="44" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 713.00 169.00)">greenbone</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F0FFFF;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="43 42 235 42 227 50 35 50 43 42" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#AAC2D2;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="235 42 235 423 227 431 227 50 235 42" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#CDE5F5;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="35 50 227 50 227 431 35 431 35 50" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="117.00" y="69.00" textLength="28" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 117.00 69.00)">docker</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#000000;fill-opacity:0.00; stroke:#9A8484; stroke-opacity:1.00">
  <path d="M 35 77 L 226 77" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="69" y="104" width="126" height="94" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="101.00" y="123.00" textLength="42" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 101.00 123.00)">Kiwi TCMS</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="179" y="109" width="11" height="17" rx="0.00" shape-rendering="auto"  />
 <rect x="176" y="111" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="176" y="118" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="69" y="220" width="126" height="103" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="98.00" y="239.00" textLength="47" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 98.00 239.00)">Kiwi Broker</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#000000;fill-opacity:0.00; stroke:#9A8484; stroke-opacity:1.00">
  <path d="M 69 247 L 194 247" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="179" y="225" width="11" height="17" rx="0.00" shape-rendering="auto"  />
 <rect x="176" y="227" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="176" y="234" width="7" height="5" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F0FFFF;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="95 257 177 257 169 265 87 265 95 257" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#AAC2D2;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="177 257 177 294 169 302 169 265 177 257" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#CDE5F5;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="87 265 169 265 169 302 87 302 87 265" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="103.00" y="284.00" textLength="49" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 103.00 284.00)">python:3.11</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F0FFFF;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="351 524 531 524 523 532 343 532 351 524" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#AAC2D2;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="531 524 531 897 523 905 523 532 531 524" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#CDE5F5;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="343 532 523 532 523 905 343 905 343 532" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="419.00" y="551.00" textLength="28" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 419.00 551.00)">docker</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#000000;fill-opacity:0.00; stroke:#9A8484; stroke-opacity:1.00">
  <path d="M 343 559 L 522 559" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="370" y="589" width="130" height="184" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="407.00" y="608.00" textLength="35" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 407.00 608.00)">Cyrus UI</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#000000;fill-opacity:0.00; stroke:#9A8484; stroke-opacity:1.00">
  <path d="M 370 616 L 499 616" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="484" y="594" width="11" height="17" rx="0.00" shape-rendering="auto"  />
 <rect x="481" y="596" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="481" y="603" width="7" height="5" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F0FFFF;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="398 631 480 631 472 639 390 639 398 631" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#AAC2D2;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="480 631 480 663 472 671 472 639 480 631" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#CDE5F5;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="390 639 472 639 472 671 390 671 390 639" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="413.00" y="655.00" textLength="35" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 413.00 655.00)">«image»</text>
	<text x="405.00" y="668.00" textLength="51" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 405.00 668.00)">drupal:latest</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F0FFFF;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="398 689 480 689 472 697 390 697 398 689" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#AAC2D2;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="480 689 480 721 472 729 472 697 480 689" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#CDE5F5;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="390 697 472 697 472 729 390 729 390 697" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="413.00" y="713.00" textLength="35" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 413.00 713.00)">«image»</text>
	<text x="407.00" y="726.00" textLength="48" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 407.00 726.00)">postgres:16</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F0FFFF;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="400 829 482 829 474 837 392 837 400 829" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#AAC2D2;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="482 829 482 866 474 874 474 837 482 829" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#CDE5F5;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
  <polygon points="392 837 474 837 474 874 392 874 392 837" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="413.00" y="853.00" textLength="40" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 413.00 853.00)">«volume»</text>
	<text x="414.00" y="866.00" textLength="38" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 414.00 866.00)">Database</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#000000;fill-opacity:0.00; stroke:#69738C; stroke-opacity:1.00">
  <path d="M 379 316 L 379 386 L 429 386" shape-rendering="auto"/>
  <path d="M 539 236 L 643 236" shape-rendering="auto"/>
  <path d="M 554 242 L 539 236 L 554 230" shape-rendering="auto"/>
  <path d="M 235 236 L 333 236" shape-rendering="auto"/>
  <path d="M 318 242 L 333 236 L 318 230" shape-rendering="auto"/>
  <path d="M 250 242 L 235 236 L 250 230" shape-rendering="auto"/>
  <path d="M 436 829 L 436 773" shape-rendering="auto"/>
  <path d="M 436 524 L 436 433" shape-rendering="auto"/>
  <path d="M 442 448 L 436 433 L 430 448" shape-rendering="auto"/>
  <path d="M 442 509 L 436 524 L 430 509" shape-rendering="auto"/>
</g>
</svg>
