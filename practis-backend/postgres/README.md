# Postgres


We are using the postgres image here.\
You don't have anything special to do here, but there is a precison about initialization.

If you want to initialize your postgres inside your [docker-compose.yml](../docker-compose.yml) file, you can follow the instructions about the [docker-compose.yml](../docker-compose.yml) file to set your postgres user and password, and your database name.

If you want to initialize from a sql script, create a `init.sql` file inside the [postgres-data](./postgres-data/) directory. The script will be executed once the container starts.

Your `init.sql` should contain at least:
- Database creation
- User creation with password

> ℹ️ `Note`\
> If you use the `init.sql` option, you don't have to set postgres environment variables in your [docker-compose.yml](../docker-compose.yml) file anymore.

Back to the [main ReadMe file](../README.md)

