#!/bin/bash
set -e
echo "initializing the database..."
psql -v ON_ERROR_STOP=1 -U $POSTGRES_USER_N -d $POSTGRES_DB_N <<-EOSQL
    CREATE USER $DB_KIWI_USERNAME WITH ENCRYPTED PASSWORD '$DB_KIWI_PASSWORD';
    CREATE DATABASE $DB_KIWI_NAME;
    ALTER DATABASE $DB_KIWI_NAME OWNER TO $DB_KIWI_USERNAME;
    CREATE SCHEMA IF NOT EXISTS api;
    CREATE SCHEMA IF NOT EXISTS public;
EOSQL
echo "initialization of the database done ✅"
