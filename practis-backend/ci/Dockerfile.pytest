# Use the official Python 3.11 image as the base image
FROM python:3.11

# Set the working directory inside the container to /api
WORKDIR /api

# Update the package lists and install the 'bc' package without unnecessary recommended packages,
# then clean up to reduce the image size
RUN apt update \
  && apt install -y --no-install-recommends bc \
  && apt clean

# Copy the requirements file for the API from the host machine to the container
COPY ./api/requirements.txt .

# Install Python dependencies from the requirements file without using the pip cache to save space
RUN pip install --no-cache-dir -r requirements.txt

# Install pytest-cov for running tests with coverage reports
RUN pip install pytest-cov pylint

# Copy the entire current directory from the host machine to the container's /api directory
COPY . .

# Make the pytest.sh script executable
RUN chmod +x ./ci/pytest.sh

# Set the default command to run the pytest.sh script
CMD ["./ci/pytest.sh"]
