#!/bin/bash

# Initialize exit code to 0 (success)
export exitcode=0

echo "RUN PYLINT -------------------------"

# Run pylint on the 'api' directory with specified options directly in the command
pylint --fail-under=8.0 --errors-only --disable=W0613,C0114,C0103,C0301,R0903,R0205,C0115 **/**/*.py

if [ "$?" -ne "0" ]; then
    echo "Pylint failed - exit code 1"
    export exitcode=1  # Set exit code to 1 (failure)
else
    echo "Pylint successful"
fi

echo "PYLINT FINISHED --------------------"

echo "RUN TESTS --------------------------"
mkdir cov
export TEST_VALUE=80

# Run pytest with configuration file, show short traceback, generate HTML and terminal coverage reports
pytest --ds=practis.settings.test_settings -c api/pytest.ini -v --tb=long --cov-report=html:./cov/ --cov-report=term

# Check if pytest command failed
if [ "$?" -ne "0" ]; then
    echo "Pytest failed - exit code 1"
    export exitcode=1  # Set exit code to 1 (failure)
else
    echo "Pytest successful"
fi

# Extract the coverage percentage from the HTML report
export cov=`grep "pc_cov" ./cov/index.html | cut -d ">" -f2 | cut -d "<" -f1 | cut -d "%" -f1 | awk '{ printf( "%s ", $1 ); } END { printf( "\n" ); }'`

# Check if coverage is below the threshold
if [[ $(bc -l <<< "$cov<$TEST_VALUE") -eq 1 ]] ; then
    echo "documentation below $TEST_VALUE% - (${cov}%)"
    export exitcode=1  # Set exit code to 1 (failure)
else
    echo "documentation above $TEST_VALUE% - (${cov}%)"
fi

echo "TESTS FINISHED ---------------------"
exit $exitcode
