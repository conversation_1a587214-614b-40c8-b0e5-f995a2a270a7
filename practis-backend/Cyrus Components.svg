<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="22.04cm" height="15.35cm" viewBox="0 0 833 580">
<title></title>
<desc>Created with Enterprise Architect (Build: 1628) 2</desc>

<g style="fill:#FFFFFF;fill-opacity:1.00;">
	 <rect x="0" y="0" width="833" height="580" shape-rendering="auto"/>
</g><g style="stroke-width:1;stroke-linecap:square;stroke-linejoin:miter; fill:#000000;fill-opacity:0.00; stroke:#000000; stroke-opacity:1.00">
  <path d="M 6 6 L 6 574 L 827 574 L 827 6 L 6 6" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:square;stroke-linejoin:miter; fill:#FFFFFF;fill-opacity:1.00; stroke:#000000; stroke-opacity:1.00">
  <polygon points="6 26 107 26 120 12 120 6 6 6 6 26" shape-rendering="auto"   style="fill-rule:evenodd;"/>
</g>
<g style="stroke-width:1;stroke-linecap:square;stroke-linejoin:miter; fill:#000000;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="11.00" y="19.00" textLength="94" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#000000;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 11.00 19.00)">cmp Cyrus Components</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="294" y="304" width="110" height="60" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="319.00" y="323.00" textLength="39" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 319.00 323.00)">Cyrus API</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="388" y="309" width="11" height="17" rx="0.00" shape-rendering="auto"  />
 <rect x="385" y="311" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="385" y="318" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="292" y="487" width="110" height="60" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="316.00" y="506.00" textLength="42" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 316.00 506.00)">Node-RED</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="386" y="492" width="11" height="17" rx="0.00" shape-rendering="auto"  />
 <rect x="383" y="494" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="383" y="501" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="35" y="304" width="110" height="60" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="62.00" y="323.00" textLength="35" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 62.00 323.00)">Cyrus UI</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="129" y="309" width="11" height="17" rx="0.00" shape-rendering="auto"  />
 <rect x="126" y="311" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="126" y="318" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="160" y="432" width="110" height="60" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="187.00" y="451.00" textLength="36" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 187.00 451.00)">Keycloak</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="254" y="437" width="11" height="17" rx="0.00" shape-rendering="auto"  />
 <rect x="251" y="439" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="251" y="446" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="614" y="243" width="169" height="183" rx="0.00" shape-rendering="auto"  />
 <rect x="763" y="252" width="15" height="13" rx="0.00" shape-rendering="auto"  />
 <rect x="764" y="250" width="7" height="3" rx="0.00" shape-rendering="auto"  />
 <rect x="759" y="254" width="8" height="4" rx="0.00" shape-rendering="auto"  />
 <rect x="759" y="259" width="8" height="4" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="686.00" y="274.00" textLength="24" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 686.00 274.00)">TCMS</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="644" y="287" width="110" height="52" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="668.00" y="306.00" textLength="42" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 668.00 306.00)">Kiwi TCMS</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="738" y="292" width="11" height="17" rx="0.00" shape-rendering="auto"  />
 <rect x="735" y="294" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="735" y="301" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="644" y="355" width="110" height="52" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="665.00" y="374.00" textLength="47" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 665.00 374.00)">Kiwi Broker</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="738" y="360" width="11" height="17" rx="0.00" shape-rendering="auto"  />
 <rect x="735" y="362" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="735" y="369" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="270" y="40" width="159" height="182" rx="0.00" shape-rendering="auto"  />
 <rect x="409" y="49" width="15" height="13" rx="0.00" shape-rendering="auto"  />
 <rect x="410" y="47" width="7" height="3" rx="0.00" shape-rendering="auto"  />
 <rect x="405" y="51" width="8" height="4" rx="0.00" shape-rendering="auto"  />
 <rect x="405" y="56" width="8" height="4" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="330.00" y="71.00" textLength="39" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 330.00 71.00)">OpenVAS</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="295" y="89" width="110" height="52" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="320.00" y="108.00" textLength="39" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 320.00 108.00)">OpenVAS</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="389" y="94" width="11" height="17" rx="0.00" shape-rendering="auto"  />
 <rect x="386" y="96" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="386" y="103" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="295" y="151" width="110" height="52" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="305.00" y="170.00" textLength="70" style="font-family:Carlito; font-weight:700; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 305.00 170.00)">OpenVAS Broker</text>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#F9EAEA;fill-opacity:1.00; stroke:#9A8484; stroke-opacity:1.00">
 <rect x="389" y="156" width="11" height="17" rx="0.00" shape-rendering="auto"  />
 <rect x="386" y="158" width="7" height="5" rx="0.00" shape-rendering="auto"  />
 <rect x="386" y="165" width="7" height="5" rx="0.00" shape-rendering="auto"  />
</g>
<g style="stroke-width:1;stroke-linecap:square;stroke-linejoin:round; fill:#000000;fill-opacity:0.00; stroke:#69738C; stroke-opacity:1.00" stroke-dasharray="6,3">
  <path d="M 349 304 L 349 222 M 349 304" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#000000;fill-opacity:0.00; stroke:#69738C; stroke-opacity:1.00">
  <path d="M 344 289 L 349 304 L 354 289" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:square;stroke-linejoin:round; fill:#000000;fill-opacity:0.00; stroke:#69738C; stroke-opacity:1.00" stroke-dasharray="6,3">
  <path d="M 614 319 L 404 319 M 614 319" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#000000;fill-opacity:0.00; stroke:#69738C; stroke-opacity:1.00">
  <path d="M 599 324 L 614 319 L 599 314" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:square;stroke-linejoin:round; fill:#000000;fill-opacity:0.00; stroke:#69738C; stroke-opacity:1.00" stroke-dasharray="6,3">
  <path d="M 404 353 L 614 353 M 404 353" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#000000;fill-opacity:0.00; stroke:#69738C; stroke-opacity:1.00">
  <path d="M 419 348 L 404 353 L 419 358" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:square;stroke-linejoin:round; fill:#000000;fill-opacity:0.00; stroke:#69738C; stroke-opacity:1.00" stroke-dasharray="6,3">
  <path d="M 294 334 L 145 334 M 294 334" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#000000;fill-opacity:0.00; stroke:#69738C; stroke-opacity:1.00">
  <path d="M 279 339 L 294 334 L 279 329" shape-rendering="auto"/>
  <path d="M 160 339 L 145 334 L 160 329" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:square;stroke-linejoin:round; fill:#000000;fill-opacity:0.00; stroke:#69738C; stroke-opacity:1.00" stroke-dasharray="6,3">
  <path d="M 90 131 L 90 304 M 270 131 L 90 131 M 270 131" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#000000;fill-opacity:0.00; stroke:#69738C; stroke-opacity:1.00">
  <path d="M 255 136 L 270 131 L 255 126" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:square;stroke-linejoin:round; fill:#000000;fill-opacity:0.00; stroke:#69738C; stroke-opacity:1.00" stroke-dasharray="6,3">
  <path d="M 90 517 L 90 364 M 292 517 L 90 517 M 292 517" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#000000;fill-opacity:0.00; stroke:#69738C; stroke-opacity:1.00">
  <path d="M 277 522 L 292 517 L 277 512" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:square;stroke-linejoin:round; fill:#000000;fill-opacity:0.00; stroke:#69738C; stroke-opacity:1.00" stroke-dasharray="6,3">
  <path d="M 348 364 L 347 487 M 348 364" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#000000;fill-opacity:0.00; stroke:#69738C; stroke-opacity:1.00">
  <path d="M 353 379 L 348 364 L 343 379" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:square;stroke-linejoin:round; fill:#000000;fill-opacity:0.00; stroke:#69738C; stroke-opacity:1.00" stroke-dasharray="6,3">
  <path d="M 316 364 L 219 432 M 316 364" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#000000;fill-opacity:0.00; stroke:#69738C; stroke-opacity:1.00">
  <path d="M 307 377 L 316 364 L 301 369" shape-rendering="auto"/>
  <path d="M 234 427 L 219 432 L 228 419" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:square;stroke-linejoin:round; fill:#000000;fill-opacity:0.00; stroke:#69738C; stroke-opacity:1.00" stroke-dasharray="6,3">
  <path d="M 185 432 L 119 364 M 185 432" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#000000;fill-opacity:0.00; stroke:#69738C; stroke-opacity:1.00">
  <path d="M 171 425 L 185 432 L 178 418" shape-rendering="auto"/>
  <path d="M 126 378 L 119 364 L 133 371" shape-rendering="auto"/>
</g>
<g style="stroke-width:1;stroke-linecap:round;stroke-linejoin:bevel; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00">
	<text x="133.00" y="391.00" textLength="59" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 133.00 391.00)">Security Token</text>
	<text x="137.00" y="409.00" textLength="27" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 137.00 409.00)">«flow»</text>
	<text x="339.00" y="421.00" textLength="16" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 339.00 421.00)">SUT</text>
	<text x="334.00" y="438.00" textLength="27" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 334.00 438.00)">«flow»</text>
	<text x="179.00" y="530.00" textLength="24" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 179.00 530.00)">«use»</text>
	<text x="463.00" y="349.00" textLength="92" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 463.00 349.00)">TestRun, TestExecution</text>
	<text x="496.00" y="366.00" textLength="27" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 496.00 366.00)">«flow»</text>
	<text x="206.00" y="347.00" textLength="27" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 206.00 347.00)">«flow»</text>
	<text x="168.00" y="144.00" textLength="24" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 168.00 144.00)">«use»</text>
	<text x="324.00" y="259.00" textLength="50" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 324.00 259.00)">Vulnerability</text>
	<text x="336.00" y="276.00" textLength="27" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 336.00 276.00)">«flow»</text>
	<text x="238.00" y="394.00" textLength="59" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 238.00 394.00)">Security Token</text>
	<text x="254.00" y="411.00" textLength="27" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 254.00 411.00)">«flow»</text>
	<text x="481.00" y="315.00" textLength="57" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 481.00 315.00)">TestCase, SUT</text>
	<text x="496.00" y="332.00" textLength="27" style="font-family:Carlito; font-weight:0; font-style:normal; font-size:10px; fill:#595959;fill-opacity:1.00; stroke:#000000; stroke-opacity:0.00 stroke-width:0; white-space: pre;" xml:space="preserve" transform="rotate(-0.00 496.00 332.00)">«flow»</text>
</g>
</svg>
