networks:
  practis-network:
    driver: bridge

services:

  postgres:
    container_name: postgres
    image: postgres:16.1
    restart: always
    environment:
      POSTGRES_USER_FILE: /run/secrets/db_username
      POSTGRES_PASSWORD_FILE: /run/secrets/db_password
      POSTGRES_DB_FILE: /run/secrets/db_name
      LOG_LEVEL: DEBUG
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $(cat /run/secrets/db_username) -d $(cat /run/secrets/db_name) -p 5432"]
      interval: 10s
      timeout: 5s
      retries: 7
      start_period: 30s
    secrets:
      - db_password
      - db_name
      - db_username
      - db_kiwi_username
      - db_kiwi_name
      - db_kiwi_password
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./postgres/postgres-data:/docker-entrypoint-initdb.d/
    ports:
      - 5432:5432
    networks:
      - practis-network

  cyrus-api:
    container_name: cyrus-api
    build:
      dockerfile: ./ci/Dockerfile.pytest
    restart: no
    depends_on:
      postgres:
        condition: service_healthy
    env_file:
      - ./api/.env
    environment:
      DEV_SECRET_KEY_FILE: /run/secrets/dev_secret_key
      PROD_SECRET_KEY_FILE: /run/secrets/prod_secret_key
      DB_PASS_FILE: /run/secrets/db_password
      DEV_DB_NAME_FILE: /run/secrets/dev_db_name
      DB_NAME_FILE: /run/secrets/db_name
      DB_USERNAME_FILE: /run/secrets/db_username
      REALM_FILE: /run/secrets/realm
      CLIENT_ID_FILE: /run/secrets/client_id
      CLIENT_SECRET_FILE: /run/secrets/client_secret
      WELL_KNOWN_FILE: /run/secrets/well_known
      KIWI_BROKER_FILE: /run/secrets/kiwi_broker
      OPENVAS_BROKER_FILE: /run/secrets/openvas_broker
      HOST: postgres
      PORT: 5432
    volumes:
      - ./api/media:/api/media
    secrets:
      - dev_secret_key
      - prod_secret_key
      - db_password
      - dev_db_name
      - db_name
      - db_username
      - realm
      - client_id
      - client_secret
      - well_known
      - kiwi_broker
      - openvas_broker
    ports:
      - 8000:8000
    networks:
      - practis-network

volumes:
  postgres-data:

secrets:
  db_username:
    file: ./secrets/db_username.txt
  db_password:
    file: ./secrets/db_password.txt
  dev_db_name:
    file: ./secrets/dev_db_name.txt
  db_name:
    file: ./secrets/db_name.txt
  pgadmin_pwd:
    file: ./secrets/pgadmin_pwd.txt
  dev_secret_key:
    file: ./secrets/dev_django_secret_key.txt
  prod_secret_key:
    file: ./secrets/prod_django_secret_key.txt
  realm:
    file: ./secrets/sso_realm.txt
  client_id:
    file: ./secrets/sso_client_id.txt
  client_secret:
    file: ./secrets/sso_client_secret.txt
  well_known:
    file: ./secrets/sso_well_known.txt
  kiwi_broker:
    file: ./secrets/kiwi_broker.txt
  openvas_broker:
    file: ./secrets/openvas_broker.txt
  db_kiwi_username:
    file: ./secrets/db_kiwi_username.txt
  db_kiwi_name:
    file: ./secrets/db_kiwi_name.txt
  db_kiwi_password:
    file: ./secrets/db_kiwi_password.txt
