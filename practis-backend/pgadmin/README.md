# PGAdmin


We are using the pgAdmin image here.

To access your postgres databases in the pdAdmin interface, you have to create a server for postgres in order to contains all databases related to your postgres container.

To do so, create a `server.json` in the [pgadmin](./) folder. The json file must contain some information about your postgres container as shown below:

```json
{
  "Servers": {
    "1": {
      "Name": "$your_server_name",
      "Group": "Servers",
      "Host": "postgres",
      "Port": 5432,
      "MaintenanceDB": "postgres",
      "Username": "$your_postgres_user",
      "PassFile": "/pgpass",
      "SSLMode": "prefer"
    }
  }
}
```
To avoid hardcoding your postgres credentials in the server.json file, you should create a PassFile and pass it as the value of `PassFile` in the `server.json`. Your pass file should look like:
```bash
****************************************************************/$your_db_name

```

Now you are good! 
Back to the [main ReadMe file](../README.md)

