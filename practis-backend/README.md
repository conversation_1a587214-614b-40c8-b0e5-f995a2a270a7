# PRACTIS Backend

To run this project, you need to have python 3.X, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> installed. Here are some useful settings to apply before launching your compose. You can run the backend independently of the other parts of the project

## Set up your environment

In order to start your backend services, you need to create your environmenet variables an secrets files. To do so, execute the [environment.sh](../environment.sh) to create it for you. This file is not versioned, ask it to one of the dev team (@tnn, @apc, or @jla)

## Access your services

Run the file [start.sh](./start.sh)
> source ./start.sh

Go to __localhost:8000/api/__ in your browser to access the API.

That means all your configurations were correct!

Now you are good! 🚀