# This is an article to export class diagram from django models

## Intall the django app django-extension

> pip3 install django-extension

then add it in your installed apps in your django settings

```python
INSTALLED_APPS = [
    ... ,
    "django_extensions",
    ... ,
]
```

## Install graphviz
To install graphviz on your mac, run the command below:

> brew install graphviz

Once you have graphviz installed, install it using pip (or pip3)

> pip3 install pydotplus
> pip3 install graphviz

Now you have everything set correctly, run the following command to generate your image.

> python3 manage.py graph_models -a -o models.png

To include only a list of models in the diagram, run the following

> python3 manage.py graph_models -a -I model1,model2 -o models.png

To exclude a list of models run the following

> python3 manage.py graph_model -a -X model1,model2 -o models.png

To include and exclude following a pattern, ruen the command below

> python3 manage.py graph_models -a -I model* -X *model -o models.png

If you just need your app models without django models, run the following

> python3 manage.py graph_models -a -X Session,AbstractBaseSession,LogEntry,User,AbstractUser,Group,Permission,AbstractBaseUser,PermissionMixin,ContentType -o models.png


