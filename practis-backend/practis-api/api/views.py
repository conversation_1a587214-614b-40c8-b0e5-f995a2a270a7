import os
import uuid6 as uuid
import uuid as UUID
import shutil
import logging
from pathlib import Path
from django.http import HttpResponse
from django.views import View
from django.core.files.base import ContentFile
import requests
from rest_framework.response import Response
from rest_framework import viewsets
from rest_framework import status
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view, renderer_classes
from rest_framework.renderers import <PERSON><PERSON><PERSON>ender<PERSON>
from .models import *
from .serializers import *
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework.parsers import FormParser, MultiPartParser
from rest_framework.decorators import api_view
import json
from django.conf import settings
from django.http import FileResponse
from diagram.serializers import (
    SubComponentSerializer,
    ComponentSerializer,
    PortSerializer,
    ParameterSerializer,
    ParameterTypeSerializer,
)
from diagram.models import SubComponent, Component, Port, Parameter, ParameterType
import shutil
from django.core.files.storage import default_storage
from django.db.models import Count, Q


logger = logging.getLogger("api")


class ImageFileView(viewsets.ViewSet):
    """
    A viewset for handling CRUD operations on ImageFile objects.

    - GET: Retrieves a list of all ImageFile objects.
    - GET (with pk): Retrieves a single ImageFile object by its primary key.
    - POST: Creates a new ImageFile object.
    - PUT: Updates an existing ImageFile object by its primary key.
    - DELETE: Deletes an ImageFile object by its primary key.
    """

    queryset = ImageFile.objects.all()
    serializer_class = ImageFileSerializer

    def list(self, request):
        queryset = ImageFile.objects.all()
        serializer = ImageFileSerializer(queryset, many=True)
        logger.info("ImageFile list retrieved")
        return Response(serializer.data)

    def retrieve(self, request, pk):
        try:
            item = ImageFile.objects.get(pk=pk)
            serializer = ImageFileSerializer(item)
            logger.info(f"ImageFile {pk} retrieved")
            return Response(serializer.data, status=status.HTTP_200_OK)
        except:
            return Response(
                {"message": "The object does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )

    def create(self, request):
        serializer = ImageFileSerializer(data=request.data)
        if serializer.is_valid():
            image_file = serializer.save()
            logger.info(f"ImageFile {image_file.uuid} created")
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, pk):
        image_file = ImageFile.objects.get(pk=pk)
        serializer = ImageFileSerializer(image_file, data=request.data)
        if serializer.is_valid():
            serializer.save()
            logger.info(f"ImageFile {pk} updated")
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        try:
            image_file = ImageFile.objects.get(pk=pk)
            full_path = image_file.file.path
            if os.path.exists(full_path):
                os.remove(full_path)  # Delete from filesystem
                logger.info(f"Deleted old image: {full_path}")
            image_file.delete()
            logger.info(f"ImageFile {pk} deleted")
            return Response(
                status=status.HTTP_204_NO_CONTENT,
            )
        except:
            return Response(
                {"message": "The object does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )


class SutProviderView(viewsets.ViewSet):
    """
    A viewset for handling CRUD operations on SutProvider objects.

    - GET: Retrieves a list of all SutProvider objects.
    - GET (with pk): Retrieves a single SutProvider object by its primary key.
    - POST: Creates a new SutProvider object.
    - PUT: Updates an existing SutProvider object by its primary key.
    - DELETE: Deletes a SutProvider object by its primary key.
    """

    queryset = SutProvider.objects.all()
    serializer_class = SutProviderSerializer
    serializer = SutProviderSerializer(queryset, many=True)
    parser_classes = (MultiPartParser, FormParser)

    def list(self, request):
        queryset = SutProvider.objects.all()
        serializer = SutProviderGETSerializer(queryset, many=True)
        logger.info("SutProvider list retrieved")
        return Response(serializer.data)

    def retrieve(self, request, pk):
        try:
            item = SutProvider.objects.get(pk=pk)
            serializer = SutProviderGETSerializer(item)
            logger.info(f"SutProvider {pk} retrieved")
            return Response(serializer.data, status=status.HTTP_200_OK)
        except:
            logger.error(f"SutProvider {pk} not found")
            return Response(
                {"message": "The object does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @swagger_auto_schema(request_body=SutProviderSerializer)
    def create(self, request):

        data = request.data
        files = request.FILES.getlist("files")
        sut_data = {
            "name": data["name"].strip('"'),
            "description": data["description"].strip('"'),
            "address": json.loads(data["address"]),
            "contacts": json.loads(data["contacts"]),
        }

        sut_provider = self.serializer_class(data=sut_data)
        sut_provider_instance = None
        if sut_provider.is_valid():
            sut_provider_instance = sut_provider.save()
        else:
            return Response(sut_provider.errors, status=400)

        pk = sut_provider_instance.uuid

        path = f"images/sut_provider/{pk}/"
        ImageFile.file.field.upload_to = path
        # if not os.path.exists(path):
        #     os.makedirs(path)
        sut_provider_images = []
        for img, file in zip(json.loads(data["images"]), files):
            image = img.copy()
            image["file"] = file
            try:
                imgSerializer = ImageFileSerializer(data=image)

                if imgSerializer.is_valid():
                    image_instance = imgSerializer.save(uuid=uuid.uuid7())
                    sut_provider_images.append(image_instance)

                else:
                    return Response(imgSerializer.errors, status=500)
            except Exception as e:
                return Response({"message": e.__str__()}, status=500)

        sut_provider_instance.images.set(sut_provider_images)
        serializer = SutProviderGETSerializer(sut_provider_instance)
        logger.info(f"New sut_provider created")
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @swagger_auto_schema(request_body=SutProviderSerializer)
    def update(self, request, pk):
        try:
            data = request.data
            files = request.FILES.getlist("files")

            sut_provider = SutProvider.objects.get(pk=pk)

            sut_data = {
                "name": data["name"].strip('"'),
                "description": data["description"].strip('"'),
                "address": json.loads(data["address"]),
                "contacts": json.loads(data["contacts"]),
            }

            path = f"images/sut_provider/{pk}/"
            ImageFile.file.field.upload_to = path
            # if not os.path.exists(path):
            #     os.makedirs(path)
            sut_provider_images = []
            current_images = [img.uuid for img in sut_provider.images.all()]
            inc = 0
            for img in json.loads(data["images"]):
                image = img.copy()
                if image["uuid"] == "":
                    file = files[inc]
                    image["file"] = file
                    imgSerializer = ImageFileSerializer(data=image)

                    if imgSerializer.is_valid():
                        image_instance = imgSerializer.save(uuid=uuid.uuid7())
                        sut_provider_images.append(image_instance)
                    else:
                        return Response(imgSerializer.errors, status=500)
                    inc += 1
                else:
                    try:
                        img_update = ImageFile.objects.get(pk=image["uuid"])
                        try:
                            img_update.default = image["default"]
                            img_update.save(update_fields=["default"])
                        except Exception as e:
                            return Response({"message": e.__str__()}, status=500)
                        sut_provider_images.append(img_update)
                    except ImageFile.DoesNotExist as e:
                        return Response(
                            {"message": e.__str__()},
                            status=status.HTTP_404_NOT_FOUND,
                        )

            for item in [
                img
                for img in current_images
                if img not in [img.uuid for img in sut_provider_images]
            ]:
                img = ImageFile.objects.get(pk=item)
                full_path = img.file.path
                if os.path.exists(full_path):
                    os.remove(full_path)  # Delete from filesystem
                    logger.info(f"Deleted old image: {full_path}")

                img.delete()

            try:
                sut_provider.images.set(sut_provider_images)
                sut_provider.name = sut_data["name"]
                sut_provider.description = sut_data["description"]
                sut_provider.address = sut_data["address"]
                sut_provider.contacts = sut_data["contacts"]
                sut_provider.save()
                logger.info(f"SutProvider {pk} updated")
            except Exception as e:
                return Response({"message": e.__str__()}, status=500)

            serializer = SutProviderGETSerializer(sut_provider)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error updating SutProvider {pk}")
            return Response({"message": e.__str__()}, status=400)

    def destroy(self, request, pk):
        try:
            sut_provider = SutProvider.objects.get(pk=pk)
            # Delete associated images (both from database and filesystem)
            for img in sut_provider.images.all():
                if img.file and os.path.exists(img.file.path):
                    os.remove(img.file.path)  # Delete the actual file
                    logger.info(f"Deleted image: {img.file.path}")
                img.delete()  # Remove from database

            sut_provider.delete()
            folder_path = f"{settings.MEDIA_ROOT}/images/sut_provider/{pk}"
            if os.path.exists(folder_path):
                shutil.rmtree(folder_path)
                os.rmdir(folder_path)

            logger.info(f"SutProvider {pk} deleted")
            return Response(
                status=status.HTTP_204_NO_CONTENT,
            )
        except SutProvider.DoesNotExist as e:
            return Response({"message": e.__str__()}, status=status.HTTP_404_NOT_FOUND)


class SutView(viewsets.ViewSet):
    """
    A viewset for handling CRUD operations on Sut objects.

    - GET: Retrieves a list of all Sut objects.
    - GET (with pk): Retrieves a single Sut object by its primary key.
    - POST: Creates a new Sut object.
    - PUT: Updates an existing Sut object by its primary key.
    - DELETE: Deletes a Sut object by its primary key.
    """

    queryset = Sut.objects.all()
    serializer_class = SutSerializer
    serializer = SutSerializer(queryset, many=True)
    parser_classes = (MultiPartParser, FormParser)

    def list(self, request):
        queryset = Sut.objects.all()
        serializer = SutGETSerializer(queryset, many=True)
        logger.info("Sut list retrieved")
        return Response(serializer.data)

    def retrieve(self, request, pk):
        try:
            sut = Sut.objects.get(pk=pk)
            serializer = SutGETWithVersionsSerializer(sut)
            logger.info(f"Sut {pk} retrieved")
            return Response(serializer.data, status=status.HTTP_200_OK)
        except:
            return Response(
                {"message": "The object does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @swagger_auto_schema(request_body=SutSerializer)
    def create(self, request):
        data = request.data
        files = request.FILES.getlist("files")
        sut_data = {
            "name": data["name"].strip('"'),
            "description": data["description"].strip('"'),
            "sut_provider": data["sut_provider"].strip('"'),
        }

        sut = self.serializer_class(data=sut_data)
        sut_instance = None
        if sut.is_valid():
            sut_instance = sut.save()
        else:
            return Response(sut.errors, status=400)

        pk = sut_instance.uuid

        path = f"images/sut/{pk}/"
        ImageFile.file.field.upload_to = path

        sut_images = []
        for img, file in zip(json.loads(data["images"]), files):
            image = img.copy()
            image["file"] = file
            try:
                imgSerializer = ImageFileSerializer(data=image)

                if imgSerializer.is_valid():
                    image_instance = imgSerializer.save(uuid=uuid.uuid7())
                    sut_images.append(image_instance)

                else:
                    return Response(imgSerializer.errors, status=500)
            except Exception as e:
                return Response({"message": e.__str__()}, status=500)

        sut_instance.images.set(sut_images)
        serializer = SutGETSerializer(sut_instance)
        logger.info(f"New sut created")
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @swagger_auto_schema(request_body=SutSerializer)
    def update(self, request, pk):
        try:
            data = request.data
            files = request.FILES.getlist("files")

            sut = Sut.objects.get(pk=pk)

            sut_data = {
                "name": data["name"].strip('"'),
                "description": data["description"].strip('"'),
                "sut_provider": data["sut_provider"].strip('"'),
            }

            path = f"images/sut/{pk}/"
            ImageFile.file.field.upload_to = path
            # if not os.path.exists(path):
            #     os.makedirs(path)
            sut_images = []
            current_images = [img.uuid for img in sut.images.all()]
            inc = 0
            for img in json.loads(data["images"]):
                image = img.copy()
                if image["uuid"] == "":
                    file = files[inc]
                    image["file"] = file
                    imgSerializer = ImageFileSerializer(data=image)

                    if imgSerializer.is_valid():
                        image_instance = imgSerializer.save(uuid=uuid.uuid7())
                        sut_images.append(image_instance)
                    else:
                        return Response(imgSerializer.errors, status=500)
                    inc += 1
                else:
                    try:
                        img_update = ImageFile.objects.get(pk=image["uuid"])
                        try:
                            img_update.default = image["default"]
                            img_update.save(update_fields=["default"])
                        except Exception as e:
                            return Response({"message": e.__str__()}, status=500)
                        sut_images.append(img_update)
                    except ImageFile.DoesNotExist as e:
                        return Response(
                            {"message": e.__str__()},
                            status=status.HTTP_404_NOT_FOUND,
                        )

            for item in [
                img
                for img in current_images
                if img not in [img.uuid for img in sut_images]
            ]:
                img = ImageFile.objects.get(pk=item)
                full_path = img.file.path
                if os.path.exists(full_path):
                    os.remove(full_path)  # Delete from filesystem
                    logger.info(f"Deleted old image: {full_path}")

                img.delete()

            try:
                sut.images.set(sut_images)
                sut.name = sut_data["name"]
                sut.description = sut_data["description"]
                sut.sut_provider_uuid = sut_data["sut_provider"]
                sut.save()
                logger.info(f"Sut {pk} updated")
            except Exception as e:
                return Response({"message": e.__str__()}, status=500)

            serializer = SutGETSerializer(sut)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error updating Sut {pk}")
            return Response({"message": e.__str__()}, status=400)

    def destroy(self, request, pk):
        try:
            sut = Sut.objects.get(pk=pk)
            # Delete associated images (both from database and filesystem)
            for img in sut.images.all():
                if img.file and os.path.exists(img.file.path):
                    os.remove(img.file.path)  # Delete the actual file
                    logger.info(f"Deleted image: {img.file.path}")
                img.delete()  # Remove from database

            sut.delete()
            folder_path = f"{settings.MEDIA_ROOT}/images/sut/{pk}"
            if os.path.exists(folder_path):
                shutil.rmtree(folder_path)
                os.rmdir(folder_path)

            logger.info(f"Sut {pk} deleted")
            return Response(
                status=status.HTTP_204_NO_CONTENT,
            )
        except Sut.DoesNotExist as e:
            return Response({"message": e.__str__()}, status=status.HTTP_404_NOT_FOUND)


class NoteView(viewsets.ViewSet):
    queryset = Note.objects.all()
    serializer_class = NoteSerializer

    def list(self, request):
        serializer = self.serializer_class(self.queryset, many=True)
        logger.info("Note list retrieved")
        return Response(serializer.data)

    def retrieve(self, request, pk):
        try:
            note = Note.objects.get(pk=pk)
            serializer = self.serializer_class(note)
            logger.info(f"Note {pk} retrieved")
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Note.DoesNotExist as e:
            return Response(
                {"message": e.__str__()},
                status=status.HTTP_404_NOT_FOUND,
            )

    @swagger_auto_schema(request_body=NoteSerializer)
    def create(self, request):
        data = request.data
        content_type = ContentType.objects.get(model=data["content_type"])

        try:
            object_id = UUID.UUID(data["object_id"])  # Convert string to UUID
        except ValueError:
            return Response(
                {"message": "Invalid UUID format for object_id"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            content_object = content_type.get_object_for_this_type(pk=object_id)
        except content_type.model_class().DoesNotExist:
            return Response(
                {"message": "The object does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )

        note_data = {
            "content_type": content_type.id,
            "object_id": str(object_id),  # Pass UUID as string for the serializer
            "text": data["text"],
        }

        serializer = self.serializer_class(data=note_data)
        if serializer.is_valid():
            note_instance = serializer.save()
            logger.info(f"Note {note_instance.uuid} created")
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        else:
            logger.error(f"Error creating Note: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(request_body=NoteSerializer)
    def update(self, request, pk):
        try:
            data = request.data
            note = Note.objects.get(pk=pk)
            content_type = ContentType.objects.get(model=data["content_type"])
            object_id = UUID.UUID(data["uuid"])
            content_object = content_type.get_object_for_this_type(pk=object_id)
            note_data = {
                "content_type": content_type,
                "object_id": object_id,
                "note": data["note"],
            }
            serializer = self.serializer_class(data=note_data)
            if serializer.is_valid():
                note_instance = serializer.save()
                logger.info(f"Note {note_instance.uuid} updated")
                return Response(serializer.data, status=status.HTTP_200_OK)
            else:
                logger.error(f"Error updating Note: {serializer.errors}")
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Note.DoesNotExist as e:
            return Response(
                {"message": e.__str__()},
                status=status.HTTP_404_NOT_FOUND,
            )

    def destroy(self, request, pk):
        try:
            note = Note.objects.get(pk=pk)
            note.delete()
            logger.info(f"Note {pk} deleted")
            return Response(
                status=status.HTTP_204_NO_CONTENT,
            )
        except Note.DoesNotExist as e:
            return Response(
                {"message": e.__str__()},
                status=status.HTTP_404_NOT_FOUND,
            )


class VersionView(viewsets.ViewSet):
    """
    A viewset for handling CRUD operations on Version objects.

    - GET: Retrieves a list of all Version objects.
    - GET (with pk): Retrieves a single Version object by its primary key.
    - POST: Creates a new Version object.
    - PUT: Updates an existing Version object by its primary key.
    - DELETE: Deletes a Version object by its primary key.
    """

    queryset = Version.objects.all()
    serializer_class = VersionSerializer
    serializer = VersionSerializer(queryset, many=True)
    parser_classes = (MultiPartParser, FormParser)

    def list(self, request):
        queryset = Version.objects.all()
        serializer = VersionGETSerializer(queryset, many=True)
        logger.info("Version list retrieved")
        return Response(serializer.data)

    def retrieve(self, request, pk):
        try:
            version = (
                Version.objects.select_related("sut")
                .prefetch_related(
                    "images",
                    "objectives",
                    "riskanalysis",
                    "riskanalysis__risks",
                    "flowexecutions",
                )
                .get(pk=pk)
            )
            serializer = VersionGETAllDataSerializer(version)
            logger.info(f"Version {pk} retrieved")
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Version.DoesNotExist as e:
            return Response(
                {"message": e.__str__()},
                status=status.HTTP_404_NOT_FOUND,
            )

    @swagger_auto_schema(request_body=VersionSerializer)
    def create(self, request):
        try:
            data = request.data
            files = request.FILES.getlist("files")
            original_uuid = data["uuid"].strip('"')

            if original_uuid == "":
                version_data = {
                    "name": data["name"].strip('"'),
                    "description": data["description"].strip('"'),
                    "status": data["status"].strip('"'),
                    "notes": data["notes"].strip('"'),
                    "sut": data["sut"].strip('"'),
                }

                version = self.serializer_class(data=version_data)
                version_instance = None
                if version.is_valid():
                    version_instance = version.save()
                else:
                    return Response(version.errors, status=400)

                pk = version_instance.uuid

                path = f"images/version/{pk}/"
                ImageFile.file.field.upload_to = path

                version_images = []
                for img, file in zip(json.loads(data["images"]), files):
                    image = img.copy()
                    image["file"] = file
                    try:
                        imgSerializer = ImageFileSerializer(data=image)

                        if imgSerializer.is_valid():
                            image_instance = imgSerializer.save(uuid=uuid.uuid7())
                            version_images.append(image_instance)

                        else:
                            return Response(imgSerializer.errors, status=500)
                    except Exception as e:
                        return Response({"message": e.__str__()}, status=500)

                version_instance.images.set(version_images)
                serializer = VersionGETSerializer(version_instance)
                logger.info(f"New Version created")
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            else:
                version = (
                    Version.objects.select_related("sut")
                    .prefetch_related(
                        "component",
                        "component__parameters",
                        "component__subcomponents",
                        "component__subcomponents__parameters",
                        "component__ports",
                        "component__ports__parameters",
                    )
                    .filter(uuid=original_uuid)
                    .all()
                )

                clone_data = {}
                clone_data["uuid"] = uuid.uuid7()
                clone_data["name"] = "Copy of " + version[0].name
                clone_data["description"] = version[0].description
                clone_data["status"] = version[0].status
                clone_data["notes"] = version[0].notes
                clone_data["sut"] = version[0].sut.uuid

                # Need to implement later the clone of the relations such as images, objective, scope, etc.
                serializer = VersionSerializer(data=clone_data)
                if serializer.is_valid():
                    version_instance = serializer.save()
                    clone_images = []
                    try:
                        logger.info(f"images/version/{version_instance.uuid}")
                        if not os.path.exists(
                            f"{settings.MEDIA_ROOT}/images/version/{version_instance.uuid}"
                        ):
                            os.makedirs(
                                f"{settings.MEDIA_ROOT}/images/version/{version_instance.uuid}"
                            )
                            logger.info("Created folder for new version")
                        original_version = Version.objects.prefetch_related(
                            "images"
                        ).get(uuid=original_uuid)
                        for img in original_version.images.all():
                            f = img.file.name.split("/")[-1]
                            with open(
                                f"{settings.MEDIA_ROOT}/images/version/{original_uuid}/{f}",
                                "rb",
                            ) as file:
                                ImageFile.file.field.upload_to = (
                                    f"images/version/{version_instance.uuid}/"
                                )
                                image = ImageFile.objects.create(
                                    file=ContentFile(file.read(), name=f),
                                    default=img.default,
                                )

                                clone_images.append(image.uuid)

                        version_instance.images.set(clone_images)
                        version_instance.save()
                    except Exception as e:
                        logger.error(f"Error cloning images: {e}")
                        return Response({"message": e.__str__()}, status=500)
                    logger.info(f"New Version created")

                    for i in range(len(version[0].component.all())):
                        comp = version[0].component.all()[i]
                        comp_id = comp.id
                        component = ComponentSerializer(
                            Component.objects.get(pk=comp_id)
                        ).data.copy()
                        # print("component", component, flush=True)
                        component.pop("id")
                        component["version"] = version_instance.uuid
                        component["name"] = comp.name
                        component["parameters"] = []
                        component["subcomponents"] = []
                        component["ports"] = []
                        component["vulnerabilities"] = []
                        component["interfaces"] = []

                        component_serializer = ComponentSerializer(data=component)
                        if component_serializer.is_valid():
                            component_instance = component_serializer.save()
                            print("component created", flush=True)
                        else:
                            print(
                                "error for component",
                                component_serializer.errors,
                                flush=True,
                            )
                            return Response(component_serializer.errors, status=500)

                        for subcomponent in comp.subcomponents.all():
                            subcomponent_id = subcomponent.id
                            sub_comp = SubComponentSerializer(
                                SubComponent.objects.get(pk=subcomponent_id)
                            ).data.copy()
                            sub_comp.pop("id")
                            sub_comp["component"] = component_instance.id
                            sub_comp["name"] = subcomponent.name
                            sub_comp["version"] = component_instance.version.uuid
                            sub_comp["parameters"] = []
                            sub_comp["vulnerabilities"] = []

                            sub_comp_serializer = SubComponentSerializer(data=sub_comp)
                            if sub_comp_serializer.is_valid():

                                sub_component_instance = sub_comp_serializer.save()
                                print("subcomponent created", flush=True)
                            else:
                                print(
                                    "error for subcomponent",
                                    sub_comp_serializer.errors,
                                    flush=True,
                                )
                                return Response(sub_comp_serializer.errors, status=500)

                            # for param in subcomponent.parameters.all():
                            #     tmp_param = ParameterSerializer(param).data.copy()
                            #     tmp_param["id"] = uuid.uuid7()
                            #     tmp_param["subcomponent"] = sub_comp["id"]
                            #     tmp_param["name"] = param.name
                            #     tmp_param["secret"] = param.secret
                            #     tmp_param["value"] = param.value
                            #     tmp_param["parameter_type"] = param.parameter_type.id

                            #     param_serializer = ParameterSerializer(data=tmp_param)
                            #     if param_serializer.is_valid():

                            #         param_serializer.save()
                            #     else:
                            #         return Response(param_serializer.errors, status=500)

                        for port in comp.ports.all():
                            port_id = port.id
                            tmp_port = PortSerializer(
                                Port.objects.get(pk=port_id)
                            ).data.copy()
                            tmp_port["id"] = uuid.uuid7()
                            tmp_port["component"] = component_instance.id
                            tmp_port["name"] = port.name
                            tmp_port["version"] = component_instance.version.uuid
                            tmp_port["parameters"] = []
                            tmp_port["vulnerabilities"] = []

                            port_serializer = PortSerializer(data=tmp_port)
                            if port_serializer.is_valid():

                                port_instance = port_serializer.save()
                            else:
                                print(
                                    "error for port", port_serializer.errors, flush=True
                                )
                                return Response(port_serializer.errors, status=500)

                            # for param in port.parameters.all():
                            #     tmp_param = ParameterSerializer(param).data.copy()
                            #     tmp_param["id"] = uuid.uuid7()
                            #     tmp_param["port"] = tmp_port["id"]
                            #     tmp_param["name"] = param.name
                            #     tmp_param["secret"] = param.secret
                            #     tmp_param["value"] = param.value
                            #     tmp_param["parameter_type"] = param.parameter_type.id

                            #     param_serializer = ParameterSerializer(data=tmp_param)
                            #     if param_serializer.is_valid():

                            #         param_serializer.save()
                            #     else:
                            #         return Response(param_serializer.errors, status=500)

                    return Response(serializer.data, status=status.HTTP_201_CREATED)
                else:
                    return Response(serializer.errors, status=400)
        except Exception as e:
            print(e.__str__(), flush=True)
            logger.error(f"Error creating Version")
            return Response({"message": e.__str__()}, status=500)

    @swagger_auto_schema(request_body=VersionSerializer)
    def update(self, request, pk):
        try:
            data = request.data
            files = request.FILES.getlist("files")

            version = Version.objects.get(pk=pk)
            onlyJson = data.get("onlyJson", None)
            if onlyJson == "1":
                version.diagram_json = data["diagram_json"]
                version.save(update_fields=["diagram_json"])
            else:
                version_data = {
                    "name": data["name"].strip('"'),
                    "description": data["description"].strip('"'),
                    "status": data["status"].strip('"'),
                    "notes": data["notes"].strip('"'),
                    "sut": Sut.objects.get(pk=data["sut"].strip('"')),
                    "onlyNotes": data["onlyNotes"].strip('"'),
                }

                if version_data["onlyNotes"] == "1":
                    version.notes = version_data["notes"]
                    version.save(update_fields=["notes"])
                else:
                    path = f"images/version/{pk}/"
                    ImageFile.file.field.upload_to = path
                    if not os.path.exists(path):
                        os.makedirs(path)
                    version_images = []
                    current_images = [img.uuid for img in version.images.all()]
                    inc = 0
                    for img in json.loads(data["images"]):
                        image = img.copy()
                        if image["uuid"] == "":
                            file = files[inc]
                            image["file"] = file
                            imgSerializer = ImageFileSerializer(data=image)

                            if imgSerializer.is_valid():
                                image_instance = imgSerializer.save(uuid=uuid.uuid7())
                                version_images.append(image_instance)
                            else:
                                return Response(imgSerializer.errors, status=500)
                            inc += 1
                        else:
                            try:
                                img_update = ImageFile.objects.get(pk=image["uuid"])
                                version_images.append(img_update)
                                try:
                                    img_update.default = image["default"]
                                    img_update.save(update_fields=["default"])
                                except Exception as e:
                                    return Response(
                                        {"message": e.__str__()}, status=500
                                    )
                            except ImageFile.DoesNotExist as e:
                                return Response(
                                    {"message": e.__str__()},
                                    status=status.HTTP_404_NOT_FOUND,
                                )

                    for item in [
                        img
                        for img in current_images
                        if img not in [img.uuid for img in version_images]
                    ]:
                        img = ImageFile.objects.get(pk=item)
                        full_path = img.file.path
                        if os.path.exists(full_path):
                            os.remove(full_path)  # Delete from filesystem
                            logger.info(f"Deleted old image: {full_path}")
                        img.delete()

                    try:
                        version.images.set(version_images)
                        version.name = version_data["name"]
                        version.description = version_data["description"]
                        version.status = version_data["status"]
                        version.notes = version_data["notes"]
                        version.sut_uuid = version_data["sut"]
                        version.save()
                        logger.info(f"Version {pk} updated")
                    except Exception as e:
                        return Response({"message": e.__str__()}, status=500)

            serializer = self.serializer_class(version)

            return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error updating Version {pk}")
            return Response({"message": e.__str__()}, status=500)

    def destroy(self, request, pk):
        try:
            version = Version.objects.get(pk=pk)
            for img in version.images.all():
                if img.file and os.path.exists(img.file.path):
                    os.remove(img.file.path)  # Delete the actual file
                    logger.info(f"Deleted image: {img.file.path}")
                img.delete()

            version.delete()
            folder_path = f"{settings.MEDIA_ROOT}/images/version/{pk}"
            if os.path.exists(folder_path):
                shutil.rmtree(folder_path)
                os.rmdir(folder_path)
            logger.info(f"Version {pk} deleted")
            return Response(
                status=status.HTTP_204_NO_CONTENT,
            )
        except Version.DoesNotExist as e:
            return Response({"message": e.__str__()}, status=status.HTTP_404_NOT_FOUND)


class ObjectivesAndScopeView(viewsets.ViewSet):
    """
    A viewset for handling CRUD operations on ObjectivesAndScope objects.

    - GET: Retrieves a list of all ObjectivesAndScope objects.
    - GET (with pk): Retrieves a single ObjectivesAndScope object by its primary key.
    - POST: Creates a new ObjectivesAndScope object.
    - PUT: Updates an existing ObjectivesAndScope object by its primary key.
    - DELETE: Deletes an ObjectivesAndScope object by its primary key.
    """

    queryset = ObjectivesAndScope.objects.all()
    serializer_class = ObjectivesAndScopeSerializer
    serializer = ObjectivesAndScopeSerializer(queryset, many=True)

    def list(self, request):
        queryset = ObjectivesAndScope.objects.all()
        serializer = ObjectivesAndScopeGETSerializer(queryset, many=True)
        logger.info("ObjectivesAndScope list retrieved")
        return Response(serializer.data)

    def retrieve(self, request, pk):
        try:
            objectives_and_scope = ObjectivesAndScope.objects.get(pk=pk)
            serializer = ObjectivesAndScopeGETSerializer(objectives_and_scope)
            logger.info(f"ObjectivesAndScope {pk} retrieved")
            return Response(serializer.data, status=status.HTTP_200_OK)
        except ObjectivesAndScope.DoesNotExist as e:
            return Response(
                {"message": e.__str__()},
                status=status.HTTP_404_NOT_FOUND,
            )

    @swagger_auto_schema(request_body=ObjectivesAndScopeSerializer)
    def create(self, request):
        data = request.data
        onlyScope = data["onlyScope"].strip('"')
        if onlyScope == "1":
            serializer = self.serializer_class(
                data={
                    "scope": data["scope"].strip('"'),
                    "sut_version": data["sut_version"].strip('"'),
                }
            )
        else:
            serializer = self.serializer_class(
                data={
                    "objectives": data["objectives"].strip('"'),
                    "description": data["description"].strip('"'),
                    "sut_version": data["sut_version"].strip('"'),
                }
            )

        if serializer.is_valid():
            objectives_and_scope_instance = serializer.save()
            logger.info(
                f"ObjectiveAndScope {objectives_and_scope_instance.uuid} created"
            )
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        logger.error(f"Error creating new ObjectivesAndScope")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(request_body=ObjectivesAndScopeSerializer)
    def update(self, request, pk):
        data = request.data
        objectives_and_scope = ObjectivesAndScope.objects.get(pk=pk)

        onlyScope = data["onlyScope"].strip('"')
        if onlyScope == "1":
            serializer = self.serializer_class(
                objectives_and_scope,
                data={
                    "scope": data["scope"].strip('"'),
                    "sut_version": data["sut_version"].strip('"'),
                },
            )
        else:
            serializer = self.serializer_class(
                objectives_and_scope,
                data={
                    "objectives": data["objectives"].strip('"'),
                    "description": data["description"].strip('"'),
                    "sut_version": data["sut_version"].strip('"'),
                },
            )

        if serializer.is_valid():
            serializer.save()
            logger.info(f"ObjectivesAndScope {pk} updated")
            return Response(serializer.data, status=status.HTTP_200_OK)
        logger.error(f"Error updating ObjectivesAndScope {pk}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, pk):
        try:
            objective_and_scope = ObjectivesAndScope.objects.get(pk=pk)
            objective_and_scope.delete()
            logger.info(f"ObjectivesAndScope {pk} deleted")
            return Response(
                status=status.HTTP_204_NO_CONTENT,
            )
        except Exception as e:
            logger.error(f"Error deleting ObjectivesAndScope {pk}")
            return Response(
                {"message": e.__str__()},
                status=status.HTTP_404_NOT_FOUND,
            )


class RiskAnalysisView(viewsets.ViewSet):
    """
    A viewset for handling CRUD operations on RiskAnalysis objects.

    - GET: Retrieves a list of all RiskAnalysis objects.
    - GET (with pk): Retrieves a single RiskAnalysis object by its primary key.
    - POST: Creates a new RiskAnalysis object.
    - PUT: Updates an existing RiskAnalysis object by its primary key.
    - DELETE: Deletes a RiskAnalysis object by its primary key.
    """

    queryset = RiskAnalysis.objects.all()
    serializer_class = RiskAnalysisSerializer
    serializer = RiskAnalysisSerializer(queryset, many=True)

    def list(self, request):
        queryset = RiskAnalysis.objects.all()
        serializer = RiskAnalysisGETSerializer(queryset, many=True)
        return Response(serializer.data)

    def retrieve(self, request, pk):
        try:
            item = RiskAnalysis.objects.get(pk=pk)
            serializer = RiskAnalysisGETSerializer(item)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except:
            return Response(
                {"message": "The object does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @swagger_auto_schema(request_body=RiskAnalysisSerializer)
    def create(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(request_body=RiskAnalysisSerializer)
    def update(self, request, pk):
        item = RiskAnalysis.objects.get(pk=pk)
        serializer = self.serializer_class(item, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, pk):
        item = RiskAnalysis.objects.get(pk=pk)
        item.delete()
        return Response(
            status=status.HTTP_204_NO_CONTENT,
        )


class RiskView(viewsets.ViewSet):
    """
    A viewset for handling CRUD operations on Risk objects.

    - GET: Retrieves a list of all Risk objects.
    - GET (with pk): Retrieves a single Risk object by its primary key.
    - POST: Creates a new Risk object.
    - PUT: Updates an existing Risk object by its primary key.
    - DELETE: Deletes a Risk object by its primary key.
    """

    queryset = Risk.objects.all()
    serializer_class = RiskSerializer
    serializer = RiskSerializer(queryset, many=True)

    def list(self, request):
        queryset = Risk.objects.all()
        serializer = RiskGETSerializer(queryset, many=True)
        return Response(serializer.data)

    def retrieve(self, request, pk):
        try:
            item = Risk.objects.get(pk=pk)
            serializer = RiskGETSerializer(item)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except:
            return Response(
                {"message": "The object does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @swagger_auto_schema(request_body=RiskSerializer)
    def create(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(request_body=RiskSerializer)
    def update(self, request, pk):
        item = Risk.objects.get(pk=pk)
        serializer = self.serializer_class(item, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, pk):
        item = Risk.objects.get(pk=pk)
        item.delete()
        return Response(
            status=status.HTTP_204_NO_CONTENT,
        )


class RequirementView(viewsets.ViewSet):
    """
    A viewset for handling CRUD operations on Requirement objects.

    - GET: Retrieves a list of all Requirement objects.
    - GET (with pk): Retrieves a single Requirement object by its primary key.
    - POST: Creates a new Requirement object.
    - PUT: Updates an existing Requirement object by its primary key.
    - DELETE: Deletes a Requirement object by its primary key.
    """

    queryset = Requirement.objects.all()
    serializer_class = RequirementSerializer
    serializer = serializer_class(queryset, many=True)

    def list(self, request):
        queryset = Requirement.objects.all()
        serializer_class = RequirementGETSerializer
        serializer = serializer_class(queryset, many=True)
        try:
            return Response(serializer.data, status=200)
        except:
            return Response({"message": "Internal server error"}, status=500)

    def retrieve(self, request, pk):
        try:
            item = Requirement.objects.get(pk=pk)
            serializer = RequirementGETSerializer(item)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except:
            return Response(
                {"message": "The object does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @swagger_auto_schema(request_body=RequirementSerializer)
    def create(self, request):
        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=201)
        else:
            return Response(serializer.errors, status=400)

    def update(self, request, pk):
        item = Requirement.objects.get(pk=pk)
        serializer = self.serializer_class(item, data=request.data)

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=200)
        else:
            return Response(serializer.errors, status=400)

    def destroy(self, request, pk):
        try:
            item = Requirement.objects.get(pk=pk)
            item.delete()
            return Response({"message": "The object has been deleted"}, status=204)
        except:
            return Response({"message": "The object does not exist"}, status=404)


class FlowView(viewsets.ViewSet):
    """
    A viewset for handling CRUD operations on Flow objects.

    - GET: Retrieves a list of all Flow objects.
    - GET (with pk): Retrieves a single Flow object by its primary key.
    - POST: Creates a new Flow object.
    - PUT: Updates an existing Flow object by its primary key.
    - DELETE: Deletes a Flow object by its primary key.
    """

    queryset = Flow.objects.all()
    serializer_class = FlowSerializer
    serializer = FlowSerializer(queryset, many=True)

    def list(self, request):
        queryset = Flow.objects.prefetch_related("parameter_type").all()
        serializer = FlowGETSerializer(queryset, many=True)
        logger.info("Flow list retrieved")
        return Response(serializer.data)

    def retrieve(self, request, pk):
        try:
            item = Flow.objects.get(pk=pk)
            serializer = FlowGETSerializer(item)
            logger.info(f"Flow {pk} retrieved")
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {"message": e.__str__()},
                status=status.HTTP_404_NOT_FOUND,
            )

    @swagger_auto_schema(request_body=FlowSerializer)
    def create(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            flow_instance = serializer.save()
            logger.info(f"New Flow {flow_instance.uuid} created")
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(request_body=FlowSerializer)
    def update(self, request, pk):
        flow = Flow.objects.get(pk=pk)
        serializer = self.serializer_class(flow, data=request.data)
        if serializer.is_valid():
            serializer.save()
            logger.info(f"Flow {pk} updated")
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, pk):
        try:
            flow = Flow.objects.get(pk=pk)
            flow.delete()
            return Response(
                status=status.HTTP_204_NO_CONTENT,
            )
        except Exception as e:
            return Response(
                {"message": e.__str__()},
                status=status.HTTP_404_NOT_FOUND,
            )


class FlowExecutionView(viewsets.ViewSet):
    """
    A viewset for handling CRUD operations on FlowExecution objects.

    - GET: Retrieves a list of all FlowExecution objects.
    - GET (with pk): Retrieves a single FlowExecution object by its primary key.
    - POST: Creates a new FlowExecution object.
    - PUT: Updates an existing FlowExecution object by its primary key.
    - DELETE: Deletes a FlowExecution object by its primary key.
    """

    queryset = FlowExecution.objects.all()
    serializer_class = FlowExecutionSerializer
    serializer = FlowExecutionSerializer(queryset, many=True)
    parser_classes = (MultiPartParser, FormParser)

    def list(self, request):
        queryset = FlowExecution.objects.select_related(
            "sut_version",
            "flow",
            "port",
            "interface",
            "component",
            "subcomponent",
            # "sut_version__sut__sut_provider",
            # "sut_version__sut",
        ).all()
        serializer = FlowExecutionGETSerializer(queryset, many=True)
        logger.info("FlowExecution list retrieved")
        # logger.info(serializer.data)

        return Response(serializer.data)

    @csrf_exempt
    def retrieve(self, request, pk):
        try:
            flow_execution = FlowExecution.objects.get(pk=pk)
            serializer = FlowExecutionGETSerializer(flow_execution)
            logger.info(f"FlowExecution {pk} retrieved")
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {"message": e.__str__()},
                status=status.HTTP_404_NOT_FOUND,
            )

    @csrf_exempt
    @swagger_auto_schema(request_body=FlowExecutionSerializer)
    def create(self, request):

        serializer = self.serializer_class(data=request.data)
        id = uuid.uuid7()
        path = f"flow_reports/{id}/"

        if serializer.is_valid():
            FlowExecution.report.field.upload_to = path
            serializer.save(uuid=id)
            logger.info(f"FlowExecution {id} created")
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(request_body=FlowExecutionSerializer)
    def update(self, request, pk):

        try:
            flow_execution = FlowExecution.objects.get(pk=pk)
            serializer = self.serializer_class(flow_execution, data=request.data)

            if serializer.is_valid():
                serializer.save()
                logger.info(f"FlowExecution {pk} updated")
                return Response(serializer.data, status=status.HTTP_201_CREATED)

            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                {"message": e.__str__()},
                status=status.HTTP_404_NOT_FOUND,
            )

    def destroy(self, request, pk):
        try:
            flow_execution = FlowExecution.objects.get(pk=pk)
            shutil.rmtree(f"{settings.MEDIA_ROOT}/flow_reports/{pk}")
            flow_execution.delete()
            logger.info(f"FlowExecution {pk} deleted")
            return Response(
                status=status.HTTP_204_NO_CONTENT,
            )
        except Exception as e:
            return Response(
                {"message": e.__str__()},
                status=status.HTTP_404_NOT_FOUND,
            )


@swagger_auto_schema(method="get")
class FlowReportView(viewsets.ViewSet):

    def retrieve(self, request, pk=None):
        try:
            flow_execution = FlowExecution.objects.get(pk=pk)
            path = flow_execution.report.path
            filename = flow_execution.report.name
            logger.info(f"FlowExecution {pk} report retrieved")
            return FileResponse(open(path, "rb"), as_attachment=True, filename=filename)
        except Exception as e:
            return Response(
                {"message": e.__str__()},
                status=status.HTTP_404_NOT_FOUND,
            )


class TestPlanView(viewsets.ViewSet):
    """
    A viewset for handling CRUD operations on TestPlan objects.

    - GET: Retrieves a list of all TestPlan objects.
    - GET (with pk): Retrieves a single TestPlan object by its primary key.
    - POST: Creates a new TestPlan object.
    - PUT: Updates an existing TestPlan object by its primary key.
    - DELETE: Deletes a TestPlan object by its primary key.
    """

    queryset = TestPlan.objects.all()
    serializer_class = TestPlanSerializer

    def list(self, request):
        queryset = TestPlan.objects.all()
        serializer = TestPlanGETSerializer(queryset, many=True)
        return Response(serializer.data)

    def retrieve(self, request, pk=None):
        try:
            item = TestPlan.objects.get(pk=pk)
            serializer = TestPlanGETSerializer(item)
            return Response(serializer.data, status=200)
        except:
            return Response({"message": "the object does not exist"})

    @csrf_exempt
    @swagger_auto_schema(request_body=TestPlanSerializer)
    def create(self, request):

        serializer = self.serializer_class(data=request.data)
        id = uuid.uuid7()

        if serializer.is_valid():
            serializer.save(uuid=id)
            logger.info(f"TestPlan {id} created")
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(request_body=TestPlanSerializer)
    def update(self, request, pk):
        try:
            test_plan = TestPlan.objects.get(pk=pk)
            serializer = self.serializer_class(test_plan, data=request.data)

            if serializer.is_valid():
                serializer.save()
                logger.info(f"TestPlan {pk} updated")
                return Response(serializer.data, status=status.HTTP_201_CREATED)

            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                {"message": e.__str__()},
                status=status.HTTP_404_NOT_FOUND,
            )

    def destroy(self, request, pk=None):
        try:
            item = TestPlan.objects.get(pk=pk)
            item.delete()
            return Response(
                status=status.HTTP_204_NO_CONTENT,
            )
        except:
            return Response({"message": "the object does not exist"}, status=404)


class TestRunView(viewsets.ViewSet):
    """
    A viewset for handling CRUD operations on TestRun objects.

    - GET: Retrieves a list of all TestRun objects.
    - GET (with pk): Retrieves a single TestRun object by its primary key.
    - POST: Creates a new TestRun object.
    - PUT: Updates an existing TestRun object by its primary key.
    - DELETE: Deletes a TestRun object by its primary key (not reflected on Kiwi).

    Note: To avoid non-existing object errors, send a GET request to /testplan to ensure you have the latest version of the test plans list.
    """

    queryset = TestRun.objects.all()
    serializer_class = TestRunSerializer
    serializer = TestRunSerializer(queryset, many=True)

    def list(self, request):
        queryset = TestRun.objects.all()
        serializer = TestRunGETSerializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def retrieve(self, request, pk):
        try:
            item = TestRun.objects.get(pk=pk)
            serializer = TestRunGETSerializer(item)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except:
            return Response(
                {"message": "The object does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @swagger_auto_schema(request_body=TestRunSerializer)
    def create(self, request):
        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():
            try:
                serializer.save()
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            except Exception as e:
                return Response(
                    {"message": e.__str__()},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(request_body=TestRunSerializer)
    def update(self, request, pk):
        item = TestRun.objects.get(pk=pk)
        serializer = self.serializer_class(item, data=request.data)
        if serializer.is_valid():

            # TODO: handle attachements
            # try:
            #     path = (
            #         f"{settings.ATTACHMENTS}testruns_testrun/{item.tcms_external_id}/"
            #     )

            #     TestCase.file.field.upload_to = f"{path}"
            #     attachments = item.attachments["files"].copy()
            #     if request.FILES.get("file") != None:
            #         filename = request.FILES.get("file").name
            #         attachments.append(path + filename)
            #         serializer.save(attachments={"files": attachments})
            #     else:
            #         serializer.save()
            # except:
            #     serializer.save()
            serializer.save()
            
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, pk):
        item = TestRun.objects.get(pk=pk)

        try:
            # TODO: handle attachements
            item.delete()
            return Response(
                status=status.HTTP_204_NO_CONTENT,
            )
        except:
            return Response(
                {"message": "The object does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )


class VulnerabilityView(viewsets.ViewSet):
    """
    A viewset for handling CRUD operations on Vulnerability objects.

    - GET: Retrieves a list of all Vulnerability objects.
    - GET (with pk): Retrieves a single Vulnerability object by its primary key.
    - POST: Creates a new Vulnerability object.
    - PUT: Updates an existing Vulnerability object by its primary key.
    - DELETE: Deletes a Vulnerability object by its primary key.
    """

    queryset = Vulnerability.objects.all()
    serializer_class = VulnerabilitySerializer
    serializer = VulnerabilitySerializer(queryset, many=True)

    def list(self, request):
        queryset = Vulnerability.objects.all()
        serializer = VulnerabilityGETSerializer(queryset, many=True)
        return Response(serializer.data)

    def retrieve(self, request, pk):
        try:
            item = Vulnerability.objects.get(pk=pk)
            serializer = VulnerabilityGETSerializer(item)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except:
            return Response(
                {"message": "The object does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @swagger_auto_schema(request_body=VulnerabilitySerializer)
    def create(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(request_body=VulnerabilitySerializer)
    def update(self, request, pk):
        item = Vulnerability.objects.get(pk=pk)
        serializer = self.serializer_class(item, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, pk):
        item = Vulnerability.objects.get(pk=pk)
        item.delete()
        return Response(
            status=status.HTTP_204_NO_CONTENT,
        )


class TestCaseView(viewsets.ViewSet):
    """
    A viewset for handling CRUD operations on TestCase objects.

    - GET: Retrieves a list of all TestCase objects.
    - GET (with pk): Retrieves a single TestCase object by its primary key.
    - POST: Creates a new TestCase object.
    - PUT: Updates an existing TestCase object by its primary key.
    - DELETE: Deletes a TestCase object by its primary key.

    Note: A test case can be linked either to a vulnerability or a requirement or none of them but not to both of them at the same time. Manage it on the frontend.
    You can add an attachment to a test case from this API. Multiple files upload at once is not supported. If you need to add more than one attachment, use the update request and add a file again.
    """

    queryset = TestCase.objects.all()
    serializer_class = TestCaseSerializer
    serializer = TestCaseSerializer(queryset, many=True)

    def list(self, request):
        # self.files_sync()
        queryset = TestCase.objects.all()
        serializer = TestCaseGETSerializer(queryset, many=True)
        return Response(serializer.data, status=200)

    def retrieve(self, request, pk):
        try:
            item = TestCase.objects.get(pk=pk)
            serializer = TestCaseGETSerializer(item)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except:
            return Response(
                {"message": "The object does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @swagger_auto_schema(request_body=TestCaseSerializer)
    def create(self, request):
        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():

            try:
                # TODO: handle attachements
                # path = f"{settings.ATTACHMENTS}testcases_testcase/{kiwi['id']}/"
                # TestCase.file.field.upload_to = f"{path}"
                # if not os.path.exists(path):
                #     os.makedirs(path)
                # if request.FILES.get("file") != None:
                #     filename = request.FILES.get("file").name
                #     serializer.save(attachments={"files": [path + filename]},)
                # else:
                #     serializer.save()
                serializer.save()
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            except Exception as e:
                return Response(
                    {"message": e.__str__()},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(request_body=TestCaseSerializer)
    def update(self, request, pk):
        item = TestCase.objects.get(pk=pk)
        serializer = self.serializer_class(item, data=request.data)
        if serializer.is_valid():
            # TODO: handle attachements
            # try:
            #     path = (
            #         f"{settings.ATTACHMENTS}testcases_testcase/{item.tcms_external_id}/"
            #     )

            #     TestCase.file.field.upload_to = f"{path}"
            #     attachments = item.attachments["files"].copy()
            #     if request.FILES.get("file") != None:
            #         filename = request.FILES.get("file").name
            #         attachments.append(path + filename)
            #         serializer.save(attachments={"files": attachments})
            #     else:
            #         serializer.save()
            # except:
            #     serializer.save()
            # return Response(serializer.data, status=status.HTTP_200_OK)
            serializer.save()
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, pk):
        item = TestCase.objects.get(pk=pk)
        try:

            item.delete()
            # TODO: handle attachements
            # path = f"{settings.ATTACHMENTS}testcases_testcase/{item.tcms_external_id}"
            # shutil.rmtree(path)
            return Response(
                status=status.HTTP_204_NO_CONTENT,
            )

        except Exception as e:
            return Response(
                {"message": e.__str__()},
                status=status.HTTP_404_NOT_FOUND,
            )


class TestExecutionView(viewsets.ViewSet):
    """
    A class to query TestExecution objects. it supports get, post, put and delete requests
    """

    queryset = TestExecution.objects.all()
    serializer_class = TestExecutionSerializer
    serializer = TestExecutionSerializer(queryset, many=True)

    def list(self, request):
        queryset = TestExecution.objects.all()
        serializer = TestExecutionSerializer(queryset, many=True)
        return Response(serializer.data)

    def retrieve(self, request, pk):
        try:
            item = TestExecution.objects.get(pk=pk)
            serializer = self.serializer_class(item)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except:
            return Response(
                {"message": "The object does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @swagger_auto_schema(request_body=TestExecutionSerializer)
    def create(self, request):
        """create a new test execution object.
        """
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(request_body=TestExecutionSerializer)
    def update(self, request, pk):
        """This endpoint update the test execution object with the given id
        """
        item = TestExecution.objects.get(pk=pk)
        serializer = self.serializer_class(item, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, pk):
        item = TestExecution.objects.get(pk=pk)
        # try:
        #     kiwi = json.loads(
        #         requests.delete(
        #             f"{settings.KIWI_BROKER}api/testexecution/{item.tcms_external_id}"
        #         )
        #     )
        # except:
        #     pass
        try:
            item.delete()
            return Response(
                status=status.HTTP_204_NO_CONTENT,
            )
        except:
            return Response(
                {"message": "The object does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )


class ScanView(viewsets.ViewSet):

    queryset = Vulnerability.objects.all()

    def list(self, request):

        try:
            r = requests.get(f"{settings.OPENVAS_BROKER}tasks/")
            response = json.loads(r.content)
            return Response(response)
        except Exception as e:
            return Response(
                {
                    "error": "The remote Openvas server can not be found",
                    "message": f"{e.message}",
                },
                status=status.HTTP_404_NOT_FOUND,
            )


class RetrieveVulnsView(viewsets.ViewSet):
    """
    This view allows you to retrieve vulnerabilities from the Openvas server. The request should be a post request with the following data:
    data = {
        "report_id": "the id of the report",
        "asset_id": "the id of the asset"
        }
    """

    queryset = RetrieveVulns.objects.all()
    serializer_class = VulnerabilitySerializer

    @swagger_auto_schema()
    def create(self, request):

        try:
            # Decode and parse JSON
            data = json.loads(request.body.decode("utf-8"))
        except json.JSONDecodeError as e:
            return Response(
                {"message": "Invalid JSON payload"},
                status=400,
            )

        # Check if data is empty or invalid
        if len(data["vulnerabilities"]) == 0:
            return Response(
                {
                    "message": "Request processed successfully but no vulnerability written to the database"
                },
                status=200,
            )

        try:
            # Fetch asset and process vulnerabilities
            element_uuid = data["element_uuid"]
            element_type = data["element_type"]
            for vuln in data["vulnerabilities"]:
                vuln[element_type] = element_uuid
                serializer = self.serializer_class(data=vuln)

                if serializer.is_valid():

                    serializer.save()

                else:
                    return Response(serializer.errors, status=500)
            return Response(
                {"message": "The vulnerabilities have been retrieved successfully"},
                status=201,
            )

        except Exception as e:
            return Response(
                {"message": e.__str__()},
                status=500,
            )

        except KeyError as e:
            return Response(
                {"message": f"Missing key in request data: {e}"},
                status=400,
            )

        except Exception as e:
            return Response(
                {"message": "An unexpected error occurred"},
                status=500,
            )

class MenuView(viewsets.ViewSet):
    """
    A viewset for handling the home page.
    """

    def list(self, request):
        response = []
        queryset = Sut.objects.prefetch_related(
            "versions",
            "versions__component",
            "versions__component__subcomponents",
            "versions__component__ports",
        ).all()

        for sut in queryset:
            versions = []
            for version in sut.versions.all():
                components = []
                for component in version.component.all():
                    subcomponents = []
                    for subcomponent in component.subcomponents.all():
                        subcomponents.append(
                            {
                                "uuid": str(
                                    subcomponent.id
                                ),  # Ensure UUIDs are converted to strings
                                "name": subcomponent.name,
                                "type": "subcomponent",
                                "route": f"/sut/{sut.uuid}/version/{version.uuid}/component/{component.id}/subcomponent/{subcomponent.id}",  # Use f-strings for better readability
                            }
                        )

                    ports = []
                    for port in component.ports.all():
                        ports.append(
                            {
                                "uuid": str(
                                    port.id
                                ),  # Ensure UUIDs are converted to strings
                                "name": port.name,
                                "type": "port",
                                "route": f"/sut/{sut.uuid}/version/{version.uuid}/component/{component.id}/port/{port.id}",  # Use f-strings for better readability
                            }
                        )
                    components.append(
                        {
                            "uuid": str(
                                component.id
                            ),  # Ensure UUIDs are converted to strings
                            "name": component.name,
                            "type": "component",
                            "route": f"/sut/{sut.uuid}/version/{version.uuid}/component/{component.id}",  # Use f-strings for better readability
                            "children": subcomponents + ports,
                        }
                    )

                versions.append(
                    {
                        "uuid": str(
                            version.uuid
                        ),  # Ensure UUIDs are converted to strings
                        "name": version.name,
                        "type": "version",
                        "route": f"/sut/{sut.uuid}/version/{version.uuid}",  # Use f-strings for better readability
                        "children": components,
                    }
                )

            response.append(
                {  # Append to response, not `sut`
                    "uuid": str(sut.uuid),
                    "name": sut.name,
                    "route": f"/sut/{sut.uuid}",
                    "children": versions,
                }
            )

        return Response(response, status=200)


class ElementView(viewsets.ViewSet):
    """
    A viewset for handling the home page.
    """

    def list(self, request):
        response = []
        queryset = Sut.objects.prefetch_related(
            "versions",
            "versions__component",
            "versions__component__subcomponents",
            "versions__component__ports",
        ).all()

        for sut in queryset:
            versions = []
            for version in sut.versions.all():
                elements = []
                for component in version.component.all():
                    elements.append(
                        {
                            "uuid": str(
                                component.id
                            ),  # Ensure UUIDs are converted to strings
                            "name": component.name,
                            "type": "component",
                        }
                    )
                    for subcomponent in component.subcomponents.all():
                        elements.append(
                            {
                                "uuid": str(
                                    subcomponent.id
                                ),  # Ensure UUIDs are converted to strings
                                "name": subcomponent.name,
                                "type": "subcomponent",
                            }
                        )

                    for port in component.ports.all():
                        elements.append(
                            {
                                "uuid": str(
                                    port.id
                                ),  # Ensure UUIDs are converted to strings
                                "name": port.name,
                                "type": "port",
                            }
                        )

                versions.append(
                    {
                        "uuid": str(
                            version.uuid
                        ),  # Ensure UUIDs are converted to strings
                        "name": version.name,
                        "type": "version",
                        "elements": elements,
                    }
                )

            response.append(
                {  # Append to response, not `sut`
                    "uuid": str(sut.uuid),
                    "name": sut.name,
                    "versions": versions,
                }
            )

        return Response(response, status=200)


class DashboardView(viewsets.ViewSet):
    """
    A viewset for handling the dashboard page. Collect the information about the vulnerabilities and tests
    """

    def list(self, request):
        vulnerabilities = (
            Vulnerability.objects.values(
                "status",
                "port__version__name",
                "port__version__sut__name",
                "port__version__uuid",
                "port__version__sut__uuid",
            )
            .annotate(count=Count("uuid"))
            .union(
                Vulnerability.objects.values(
                    "status",
                    "component__version__name",
                    "component__version__sut__name",
                    "component__version__uuid",
                    "component__version__sut__uuid",
                ).annotate(count=Count("uuid"))
            )
            .union(
                Vulnerability.objects.values(
                    "status",
                    "subcomponent__version__name",
                    "subcomponent__version__sut__name",
                    "subcomponent__version__uuid",
                    "subcomponent__version__sut__uuid",
                ).annotate(count=Count("uuid"))
            )
            .union(
                Vulnerability.objects.values(
                    "status",
                    "interface__version__name",
                    "interface__version__sut__name",
                    "interface__version__uuid",
                    "interface__version__sut__uuid",
                ).annotate(count=Count("uuid"))
            )
            .order_by(
                "-port__version__sut__uuid",
                "-port__version__uuid",
                "port__version__sut__name",
                "port__version__name",
                "status",
            )
        )

        data = {}
        for v in vulnerabilities:
            sut_name = (
                v.get("port__version__sut__name")
                or v.get("component__version__sut__name")
                or v.get("subcomponent__version__sut__name")
                or v.get("interface__version__sut__name")
            )
            version_name = (
                v.get("port__version__name")
                or v.get("component__version__name")
                or v.get("subcomponent__version__name")
                or v.get("interface__version__name")
            )

            # if not sut_name or not version_name or v["count"] == 0:
            if not sut_name or not version_name:
                continue

            key = f"{sut_name} - {version_name}"
            if key not in data:
                data[key] = []
            data[key].append({"name": v["status"], "value": v["count"]})
        # Need to add code later of how to get test data
        final_response = {
            "vulnerabilityData": [{"name": k, "series": v} for k, v in data.items()][
                :5
            ],
            "testData": [],
        }
        return Response(final_response, status=200)


class HomeView(viewsets.ViewSet):
    """
    A viewset for handling the home page.
    """

    queryset = Version.objects.all()
    serializer_class = VersionGETSerializer

    def list(self, request):

        versions = self.serializer_class(self.queryset, many=True)
        suts = SutGETSerializer(Sut.objects.all(), many=True)
        sut_provider = SutProviderGETSerializer(SutProvider.objects.all(), many=True)
        response = {
            "home": {
                "versions": versions.data,
                "suts": suts.data,
            }
        }

        return Response(response, status=200)


def robots_txt(request):
    if settings.DEBUG:
        # Disallow all in development
        lines = ["User-Agent: *", "Disallow: /"]
    else:
        # Production rules
        lines = [
            "User-Agent: *",
            "Disallow: /admin/",
            "Allow: /",
        ]
    return HttpResponse("\n".join(lines), content_type="text/plain")


class NotFoundView(View):
    def dispatch(self, request):
        response = Response(
            {"message": "The requested resource can not be found"},
            status=status.HTTP_404_NOT_FOUND,
        )
        response.accepted_renderer = JSONRenderer()
        response.accepted_media_type = "application/json"
        response.renderer_context = {}
        return response


@api_view(["GET"])
@renderer_classes([JSONRenderer])
def error_404(request, exception):
    message = "The requested resource can not be found"
    return Response(data={"message": message}, status=status.HTTP_404_NOT_FOUND)


@api_view(["GET"])
@renderer_classes([JSONRenderer])
def error_401(request, exception):
    message = ("The resource can not be retrieved because you are not authenticated",)
    return Response({"message": message}, status=status.HTTP_401_UNAUTHORIZED)


@api_view(["GET"])
@renderer_classes([JSONRenderer])
def error_500(request):
    return Response(
        {"message": "Unable to reach the server"},
        status=status.HTTP_500_INTERNAL_SERVER_ERROR,
    )
