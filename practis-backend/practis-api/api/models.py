import os
import uuid6 as uuid
from django.db import models
from django.utils import timezone as tz
from django.conf import settings
from django.core.exceptions import ValidationError
from django.contrib.postgres.fields import ArrayField
from django.contrib.contenttypes.fields import <PERSON>ricForeign<PERSON>ey
from django.contrib.contenttypes.models import ContentType
from .validators import validate_customer_address, validate_customer_contact
from django.apps import apps

class ImageFile(models.Model):
    """
    images for a SUT and its environment
    """

    uuid = models.UUIDField(
        default=uuid.uuid7, editable=False, unique=True, primary_key=True
    )

    file = models.FileField(upload_to="images/global", null=True, blank=True)
    default = models.BooleanField(default=False)

    class Meta:
        db_table = "image_file"
        ordering = ["-default"]

    def __str__(self):
        return "/".join(self.file.name.split("/")[-3:])


class SutProvider(models.Model):
    """
    A sut_provider is a company that uses Practis for their tests.
    This table has a unique record for privacy purposes.
    The sut_provider address must have the following schema:
    {
        "street": "string",
        "city": "string",
        "zip": "string",
        "state": "string"
    }
    """

    uuid = models.UUIDField(
        default=uuid.uuid7, editable=False, unique=True, primary_key=True
    )
    name = models.CharField(max_length=250)
    description = models.TextField(blank=True)
    images = models.ManyToManyField(ImageFile, blank=True)
    address = models.JSONField(validators=[validate_customer_address], default={})
    contacts = ArrayField(
        models.JSONField(default=dict),
        validators=[validate_customer_contact],
        default=list,
    )

    class Meta:
        db_table = "sut_provider"
        ordering = ["-uuid"]

    def __str__(self):
        return self.name


class Sut(models.Model):
    """
    A System under test
    An instance of SutProvider must exist to create a sut
    A Sut can be created before adding elements later. It is set like that because a SUT can change with time ie an asset can be added (removed)
    """

    uuid = models.UUIDField(
        default=uuid.uuid7, editable=False, unique=True, primary_key=True
    )
    name = models.CharField(max_length=250)
    description = models.TextField(blank=True)
    images = models.ManyToManyField(ImageFile, blank=True)
    sut_provider = models.ForeignKey(
        SutProvider, models.CASCADE, blank=False, null=False
    )

    class Meta:
        db_table = "sut"
        ordering = ["-uuid"]

    def __str__(self):
        return self.name


class Note(models.Model):
    """
    Notes for elements and versions.
    """

    uuid = models.UUIDField(
        default=uuid.uuid7, editable=False, unique=True, primary_key=True
    )
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.UUIDField()
    element = GenericForeignKey("content_type", "object_id")
    text = models.TextField()

    def __str__(self):
        return f"Note for {self.element}"

    class Meta:
        db_table = "note"


class Version(models.Model):
    """
    This table aims to track version for a System under test.
    You need at least an existing SUT to create a version
    the default_image field is the id of one of the images in the images field
    """

    STATUS_TYPES = [
        ("in development", "in development"),
        ("in production", "in production"),
    ]

    uuid = models.UUIDField(
        default=uuid.uuid7, editable=False, unique=True, primary_key=True
    )
    name = models.CharField(max_length=250)
    description = models.TextField(blank=True, default="")
    status = models.CharField(
        max_length=50, choices=STATUS_TYPES, default=STATUS_TYPES[0][0]
    )
    sut = models.ForeignKey(
        Sut, on_delete=models.CASCADE, blank=False, null=False, related_name="versions"
    )
    images = models.ManyToManyField(ImageFile, blank=True)
    notes = models.TextField(blank=True, null=True, default="")
    diagram_json = models.TextField(blank=True, null=True, default="")

    class Meta:
        db_table = "sut_version"
        ordering = ["-uuid"]


class ObjectivesAndScope(models.Model):
    """
    The tests objectives and scope defined by the sut_provider.
    Ojectives and scope ar related to a SUT version
    """

    OBJECTIVE_TYPES = [
        ("evaluating security", "evaluating security"),
        ("validating risk analysis", "validating risk analysis"),
        ("verifying corrected vulnerabilities", "verifying corrected vulnerabilities"),
    ]

    uuid = models.UUIDField(
        default=uuid.uuid7, editable=False, unique=True, primary_key=True
    )
    objectives = models.CharField(
        max_length=100, choices=OBJECTIVE_TYPES, default=OBJECTIVE_TYPES[0][0]
    )
    scope = models.TextField(blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    sut_version = models.ForeignKey(
        Version,
        on_delete=models.CASCADE,
        blank=False,
        null=False,
        related_name="objectives",
    )

    class Meta:
        db_table = "objectives_and_scope"


class Flow(models.Model):
    """
    Acknowledgement flow on nodered, that executes some gathering information operations on a System under Test an its environment
    """

    uuid = models.UUIDField(
        default=uuid.uuid7, editable=False, unique=True, primary_key=True
    )
    name = models.CharField(max_length=250)
    description = models.TextField(blank=True)
    address = models.URLField(default="http://localhost:8000")
    api_url = models.URLField(default="http://localhost:8000")
    parameter_type = models.ManyToManyField(
        "diagram.ParameterType", null=True, blank=True, related_name="flows"
    )

    class Meta:
        db_table = "flow"
        ordering = ["-uuid"]

    def __str__(self):
        return self.name


class FlowExecution(models.Model):
    """
    An execution of a flow with its data. An execution concerns a single SUT version and a single flow. So, both of them must exist!
    Result and report fields are populated once the execution is done.
    """

    RESULT_TYPES = [("pass", "pass"), ("fail", "fail"), ("n/a", "n/a")]

    uuid = models.UUIDField(
        default=uuid.uuid7, editable=False, unique=True, primary_key=True
    )
    date = models.DateTimeField(default=tz.now)
    result = models.CharField(
        max_length=50, choices=RESULT_TYPES, default=RESULT_TYPES[-1][0]
    )
    report = models.FileField(upload_to=f"media/flow_reports/", null=True, blank=True)
    sut_version = models.ForeignKey(
        Version, models.CASCADE, blank=False, null=False, related_name="flowexecutions"
    )
    flow = models.ForeignKey(
        Flow,
        models.CASCADE,
        blank=False,
        null=False,
        related_name="flowexecutions",
    )
    port = models.ForeignKey(
        "diagram.Port",
        models.SET_NULL,
        blank=True,
        null=True,
        related_name="flowexecutions",
    )
    interface = models.ForeignKey(
        "diagram.Interface",
        models.SET_NULL,
        blank=True,
        null=True,
        related_name="flowexecutions",
    )
    component = models.ForeignKey(
        "diagram.Component",
        models.SET_NULL,
        blank=True,
        null=True,
        related_name="flowexecutions",
    )
    subcomponent = models.ForeignKey(
        "diagram.SubComponent",
        models.SET_NULL,
        blank=True,
        null=True,
        related_name="flowexecutions",
    )

    class Meta:
        db_table = "flow_execution"
        ordering = ["-flow__uuid", "-date"]

    def __str__(self):
        return f"execution for flow {self.flow}"


class TestPlan(models.Model):
    """
    A plan is a set of test cases to execute. A plan is needed in order to create a test run. we don't manage test plans here.
    This table only aims to store plans ids to use it further in the test run.
    """

    uuid = models.UUIDField(
        default=uuid.uuid7, editable=False, unique=True, primary_key=True
    )
    name = models.CharField(max_length=250)
    description = models.TextField(blank=True, default="")
    attachments = models.JSONField(default={"files": []})
    version = models.ForeignKey(Version, on_delete=models.CASCADE)

    test_cases = models.ManyToManyField("TestCase", blank=True)
    class Meta:
        db_table = "test_plan"

    def __str__(self):
        return self.name


class TestRun(models.Model):
    """
    A session of tests. It could be one test or a group of tests to execute.
    A session concerns a single SUT. So a SUT must exist to create a session
    """

    uuid = models.UUIDField(
        default=uuid.uuid7, editable=False, unique=True, primary_key=True
    )
    name = models.CharField(max_length=250)
    description = models.TextField(blank=True)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    version = models.ForeignKey(Version, on_delete=models.CASCADE)

    plan = models.ForeignKey(TestPlan, on_delete=models.SET_NULL, blank=True, null=True)
    attachments = models.JSONField(default={"files": []})

    class Meta:
        db_table = "test_run"

    def __str__(self):
        return str(self.name)

    def clean(self):
        """
        Custom validation to ensure start_date is before end_date
        """
        super().clean()
        
        if self.start_date and self.end_date:
            if self.start_date > self.end_date:
                raise ValidationError({'end_date': 'End date cannot be before start date'})

class RiskAnalysis(models.Model):
    """
    Analysis for the risks
    """

    uuid = models.UUIDField(
        default=uuid.uuid7, editable=False, unique=True, primary_key=True
    )
    name = models.CharField(max_length=200, blank=False)
    docs = models.URLField()
    # sut_version = models.UUIDField(blank=False, null=True)
    sut_version = models.ForeignKey(
        Version,
        on_delete=models.CASCADE,
        blank=False,
        null=False,
        related_name="riskanalysis",
    )

    class Meta:
        db_table = "risk_analysis"
        ordering = ["-uuid"]

    def __str__(self):
        return self.name


class Risk(models.Model):
    """
    A potential risk. As risk must have a risk analysis. An instance of risk analysis must exist in order to create a Risk.
    """

    STATUS_TYPES = [
        ("new", "new"),
        ("confirmed", "confirmed"),
        ("unconfirmed", "unconfirmed"),
        ("treated", "treated"),
    ]
    VALUE_CHOICES = [
        ("Low", "Low"),
        ("Medium", "Medium"),
        ("High", "High"),
    ]

    uuid = models.UUIDField(
        default=uuid.uuid7, editable=False, unique=True, primary_key=True
    )
    name = models.CharField(max_length=250)
    description = models.TextField(blank=True)
    value = models.CharField(
        max_length=50, choices=VALUE_CHOICES, default=VALUE_CHOICES[0][0]
    )
    level = models.CharField(max_length=250)
    status = models.CharField(
        max_length=50, choices=STATUS_TYPES, default=STATUS_TYPES[0][0]
    )
    # risk_analysis = models.UUIDField(null=True, blank=False)
    risk_analysis = models.ForeignKey(
        RiskAnalysis, models.SET_NULL, null=True, blank=True, related_name="risks"
    )

    class Meta:
        db_table = "risk"

    def __str__(self):
        return self.name


class Vulnerability(models.Model):
    """
    Vulnerabilities from risks
    """

    STATUS_TYPE = [
        ("new", "new"),
        ("confirmed", "confirmed"),
        ("unconfirmed", "unconfirmed"),
        ("resolved", "resolved"),
        ("confirmed resolved", "confirmed resolved"),
    ]
    uuid = models.UUIDField(
        default=uuid.uuid7, editable=False, unique=True, primary_key=True
    )
    gvm_uuid = models.CharField(max_length=100, blank=True, null=True)
    name = models.CharField(max_length=250)
    description = models.TextField(blank=True)
    cve_cwe = models.TextField(blank=True)
    attack_path_or_vector = models.CharField(
        max_length=300, blank=True, null=True, default=""
    )
    status = models.CharField(
        max_length=250, choices=STATUS_TYPE, default=STATUS_TYPE[0][0]
    )
    recommendations = models.TextField(blank=True, null=True, default="")
    risks = models.ManyToManyField(
        Risk, null=True, blank=True, related_name="vulnerabilities"
    )
    port = models.ForeignKey(
        "diagram.Port",
        models.SET_NULL,
        blank=True,
        null=True,
        related_name="vulnerabilities",
    )
    interface = models.ForeignKey(
        "diagram.Interface",
        models.SET_NULL,
        blank=True,
        null=True,
        related_name="vulnearbilities",
    )
    component = models.ForeignKey(
        "diagram.Component",
        models.SET_NULL,
        blank=True,
        null=True,
        related_name="vulnerabilities",
    )
    subcomponent = models.ForeignKey(
        "diagram.SubComponent",
        models.SET_NULL,
        blank=True,
        null=True,
        related_name="vulnerabilities",
    )

    class Meta:
        db_table = "vulnerability"
        ordering = ["-uuid"]

    def __str__(self):
        return self.name


class Requirement(models.Model):
    PRIORITY_CHOICES = [
        ("Low", "Low"),
        ("Medium", "Medium"),
        ("High", "High"),
        ("Critical", "Critical"),
    ]

    STATUS_CHOICES = [
        ("Pending", "Pending"),
        ("In Progress", "In Progress"),
        ("Completed", "Completed"),
        ("Cancelled", "Cancelled"),
    ]
    uuid = models.UUIDField(
        default=uuid.uuid7, editable=False, unique=True, primary_key=True
    )
    name = models.CharField(max_length=250)
    description = models.TextField(blank=False, null=True)
    priority = models.CharField(
        max_length=50, choices=PRIORITY_CHOICES, default=PRIORITY_CHOICES[0][0]
    )
    status = models.CharField(
        max_length=50, choices=STATUS_CHOICES, default=STATUS_CHOICES[0][0]
    )
    # sut_version = models.UUIDField(null=True)
    sut_version = models.ForeignKey(
        Version, null=True, on_delete=models.SET_NULL, related_name="requirements"
    )


class TestCase(models.Model):
    """
    A test case for a vulnerability. There could be several tests scripts in a test session. An instance of vulnerability must exist
    for category, you can use the default value "--default--"
    """

    STATUS = [
        ("PROPOSED", "PROPOSED"),
        ("CONFIRMED", "CONFIRMED"),
        ("DISABLED", "DISABLED"),
        ("NEED UPDATE", "NEED UPDATE"),
    ]
    PRIORITY = [
        ("1", "P1"),
        ("2", "P2"),
        ("3", "P3"),
        ("4", "P4"),
        ("5", "P5"),
    ]
    uuid = models.UUIDField(
        default=uuid.uuid7, editable=False, unique=True, primary_key=True
    )
    name = models.CharField(max_length=250)
    description = models.TextField(blank=True)
    attack_technique = models.CharField(max_length=250, blank=True, default="")
    # vulnerability = models.UUIDField(blank=False, null=True)
    vulnerability = models.ForeignKey(
        Vulnerability, models.SET_NULL, blank=False, null=True
    )
    # requirement = models.UUIDField(blank=False, null=True)
    requirement = models.ForeignKey(
        Requirement, on_delete=models.SET_NULL, blank=False, null=True
    )
    status = models.CharField(max_length=250, choices=STATUS, default=STATUS[0][0])
    priority = models.CharField(
        max_length=250, choices=PRIORITY, default=PRIORITY[0][0]
    )
    category = models.CharField(max_length=250, default="--default--", blank=True)
    file = models.FileField(upload_to="", blank=True, null=True)
    attachments = models.JSONField(default={"files": []})
    recommendations = models.TextField(blank=True, null=True, default="")

    class Meta:
        db_table = "test_case"

    def __str__(self):
        return self.name


class TestExecution(models.Model):
    """
    Execution of a test case with its data. A test script execution is include in a test session. So, A test session must have been created before any script execution.
    """

    RESULT_TYPES = [
        ("PASSED", "PASSED"),
        ("WAVED", "WAVED"),
        ("PAUSED", "PAUSED"),
        ("FAILED", "FAILED"),
        ("RUNNING", "RUNNING"),
        ("IDLE", "IDLE"),
        ("BLOCKED", "BLOCKED"),
        ("ERROR", "ERROR"),
    ]
    uuid = models.UUIDField(
        default=uuid.uuid7, editable=False, unique=True, primary_key=True
    )
    date = models.DateTimeField(null=True, blank=True)
    status = models.CharField(
        max_length=50, choices=RESULT_TYPES, default="IDLE"
    )
    report = models.TextField(null=True, blank=True)
    test_case = models.ForeignKey(TestCase, null=False, on_delete=models.CASCADE)
    test_run = models.ForeignKey(TestRun, null=False, on_delete=models.CASCADE)

    automated = models.BooleanField(default=False)
    source = models.URLField(null=True, blank=True)
    execution_path = models.CharField(max_length=250, null=True, blank=True)
    
    class Meta:
        db_table = "test_execution"


# class TestRunCase(models.Model):

#     ACTIONS = [("add", "add"), ("remove", "remove")]
#     test_run_id = models.ForeignKey(TestRun, on_delete=models.CASCADE)
#     test_cases = models.ManyToManyField(TestCase, blank=True)
#     action = models.CharField(max_length=250, choices=ACTIONS, default=ACTIONS[0][0])

#     class Meta:
#         managed = False


class RetrieveVulns(models.Model):

    uuid = models.UUIDField(
        default=uuid.uuid7, editable=False, unique=True, primary_key=True
    )
    report = models.CharField(max_length=200, default="")

    class Meta:
        db_table = "retrieval_vulns"

    def __str__(self) -> str:
        return self.report


class RetrieveTestExecution(models.Model):

    uuid = models.UUIDField(
        default=uuid.uuid7, editable=False, unique=True, primary_key=True
    )

    class Meta:
        db_table = "retrieval_test_execution"
