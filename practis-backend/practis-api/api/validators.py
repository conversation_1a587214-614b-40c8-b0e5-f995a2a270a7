import re
from django.core.exceptions import ValidationError


def validate_customer_address(value):
    required_keys = ["street", "city", "zip", "state"]

    if not isinstance(value, dict):
        raise ValidationError("Address must be a valid JSON object.")

    if value and set(required_keys) != set(value.keys()):
        raise ValidationError(
            f"Invalid dict format. Ensure your json keys are : {', '.join(required_keys)}"
        )


def validate_customer_contact(value):
    required_keys = ["mail", "phone", "name", "description"]
    email_pattern = r"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$"

    if not isinstance(value, list):
        raise ValidationError("Contacts must be a valid list of Json objects.")

    for _ in value:
        if not isinstance(_, dict):
            raise ValidationError("A contact must be a valid Json object.")

        if set(required_keys) != set(_.keys()):
            raise ValidationError(
                f"Invalid dict format. Ensure your json keys are : {', '.join(required_keys)}"
            )

        if not re.match(email_pattern, _["mail"]):
            raise ValidationError(
                "Invalid email format. Check all your contacts's emails."
            )
