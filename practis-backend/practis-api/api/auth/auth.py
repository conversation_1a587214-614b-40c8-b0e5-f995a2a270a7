import logging
from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed
from django.contrib.auth.models import User
from .keycloak_service import KeycloakService

logger = logging.getLogger("keycloak")


class KeycloakAuthentication(BaseAuthentication):

    def authenticate(self, request):
        # Extract the token from the Authorization header
        token = request.headers.get("Authorization")
        if not token:
            logger.warning("Token not found in request headers")
            return None

        token = token.split(" ")[-1]  # Remove 'Bearer' prefix if present
        keycloak_service = KeycloakService()
        user_info = keycloak_service.verify_token(token)

        if not user_info:
            logger.error("Invalid token")
            raise AuthenticationFailed("Invalid token")

        # Get or create the Django user based on Keycloak user info
        user, created = User.objects.get_or_create(
            username=user_info.get("username"),
            defaults={
                "email": user_info.get("email"),
                "first_name": user_info.get("first_name"),
                "last_name": user_info.get("last_name"),
            },
        )

        # Optionally, update user info each time they log in
        if not created:
            logger.info(f"Retrieved existing user: {user.username}")
            user.email = user_info.get("email")
            user.first_name = user_info.get("first_name")
            user.last_name = user_info.get("last_name")
            user.save()
        else:
            logger.info(f"Created new user: {user.username}")

        return (user, token)
