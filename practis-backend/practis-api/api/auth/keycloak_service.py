import requests
from keycloak import KeycloakOpenID
from django.conf import settings


class KeycloakService:
    def __init__(self):
        self.keycloak_openid = KeycloakOpenID(
            server_url=settings.KEYCLOAK_SERVER_URL,
            client_id=settings.<PERSON><PERSON><PERSON><PERSON><PERSON>K_CLIENT_ID,
            realm_name=settings.<PERSON><PERSON><PERSON><PERSON>OAK_REALM_NAME,
            client_secret_key=settings.K<PERSON><PERSON>CLOAK_CLIENT_SECRET,
        )

    def verify_token(self, token):

        introspect_url = f"{settings.KEYCLOAK_SERVER_URL}realms/{settings.KEYCLOAK_REALM_NAME}/protocol/openid-connect/token/introspect"
        introspect_data = {
            "client_id": settings.<PERSON><PERSON><PERSON><PERSON>OAK_CLIENT_ID,
            "client_secret": settings.KEYCLOAK_CLIENT_SECRET,
            "token": token,
        }
        try:
            response = requests.post(introspect_url, data=introspect_data)
            if response.json().get("active"):

                user_info = {
                    "first_name": response.json().get("given_name"),
                    "last_name": response.json().get("family_name"),
                    "email": response.json().get("email"),
                    "username": response.json().get("preferred_username"),
                }

                return user_info
            else:
                return None
        except Exception as e:
            return None
