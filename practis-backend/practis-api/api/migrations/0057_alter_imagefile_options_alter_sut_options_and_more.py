# Generated by Django 4.2.7 on 2025-02-28 14:00

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0056_sut_images_alter_flow_uuid_alter_flowexecution_uuid_and_more"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="imagefile",
            options={"ordering": ["-default"]},
        ),
        migrations.AlterModelOptions(
            name="sut",
            options={"ordering": ["-uuid"]},
        ),
        migrations.AlterModelOptions(
            name="sutprovider",
            options={"ordering": ["-uuid"]},
        ),
        migrations.AlterField(
            model_name="flow",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954cdb-c58e-708b-9eb7-bf808a7a7092"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="flowexecution",
            name="report",
            field=models.FileField(
                blank=True, null=True, upload_to="media/flow_reports/"
            ),
        ),
        migrations.AlterField(
            model_name="flowexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954cdb-c58f-7e0b-9d94-d042e31fdc0c"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="imagefile",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954cdb-c589-73f8-be6a-5013d632243d"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="objectivesandscope",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954cdb-c58d-78d9-beaa-ff85941d31c2"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="requirement",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954cdb-c595-7792-ad4b-dcf65f8576ae"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="retrievetestexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954cdb-c599-7084-97fb-d5b8217d4f8e"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="retrievevulns",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954cdb-c598-7d37-9c74-ee42cd5055d6"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="risk",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954cdb-c594-75c2-bb67-74ae163661fd"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="riskanalysis",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954cdb-c592-7ef1-a354-e6e6099aaf68"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="sut",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954cdb-c58b-70a1-b369-8ead738e0bc2"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="sutprovider",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954cdb-c58a-775c-8db7-46aa59fd0a94"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testcase",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954cdb-c596-78e9-a4e1-81a7622c3eec"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954cdb-c597-7498-8bc7-ecbc3405ff22"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testplan",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954cdb-c590-781f-9af2-bade26ab463f"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testrun",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954cdb-c591-7e5c-b71a-3dda835c63ba"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="version",
            name="sut",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="versions",
                to="api.sut",
            ),
        ),
        migrations.AlterField(
            model_name="version",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954cdb-c58c-72e0-845b-ee5b2229f585"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="vulnerability",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954cdb-c593-7adb-a48b-1d05620cbb72"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
    ]
