# Generated by Django 4.2.7 on 2024-06-09 22:17

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0016_remove_vulnerability_scan"),
    ]

    operations = [
        migrations.CreateModel(
            name="RetrieveTestScriptExecution",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("product", models.IntegerField()),
            ],
            options={
                "db_table": "retrieval_test_execution",
            },
        ),
        migrations.AddField(
            model_name="sut",
            name="id_kiwi",
            field=models.CharField(blank=True, max_length=250, null=True),
        ),
        migrations.AlterModelTable(
            name="retrievevulns",
            table="retrieval_vulns",
        ),
    ]
