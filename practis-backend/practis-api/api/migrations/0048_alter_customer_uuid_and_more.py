# Generated by Django 4.2.7 on 2025-02-24 00:05

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0047_customerobjectivesandscope_sut_version_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="customer",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953544-7b4a-727e-a1be-6eda97f9dadb"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="customerobjectivesandscope",
            name="sut_version",
            field=models.ForeignKey(
                default="",
                on_delete=django.db.models.deletion.CASCADE,
                to="api.version",
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="customerobjectivesandscope",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953544-7b4d-7678-b441-1b11e98a3894"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="flow",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953544-7b4e-7bc4-b804-b6ec3bab035c"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="flowexecution",
            name="flow",
            field=models.ForeignKey(
                db_column="flow",
                default="",
                on_delete=django.db.models.deletion.CASCADE,
                to="api.flow",
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="flowexecution",
            name="sut_version",
            field=models.ForeignKey(
                default="", on_delete=django.db.models.deletion.CASCADE, to="api.sut"
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="flowexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953544-7b4f-7403-ae7e-ace260253cad"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="imagefile",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953544-7b49-779b-9349-afac6879841d"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="requirement",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953544-7b55-7a43-b656-a37695954387"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="retrievetestexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953544-7b59-74fe-9eab-6edf15a38dc4"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="retrievevulns",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953544-7b58-7802-b86d-190958d68361"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="risk",
            name="risk_analysis",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="api.riskanalysis",
            ),
        ),
        migrations.AlterField(
            model_name="risk",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953544-7b54-79eb-9fbe-aa41ca933d90"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="riskanalysis",
            name="sut_version",
            field=models.ForeignKey(
                default="",
                on_delete=django.db.models.deletion.CASCADE,
                to="api.version",
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="riskanalysis",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953544-7b52-7398-ae87-8007d90fddcb"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="sut",
            name="customer",
            field=models.ForeignKey(
                default="",
                on_delete=django.db.models.deletion.CASCADE,
                to="api.customer",
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="sut",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953544-7b4b-7684-8a23-c6a7b9b58cd6"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testcase",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953544-7b56-7c1a-974a-f97ca289ebe7"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953544-7b57-7d88-9695-5f50ba011c93"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testplan",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953544-7b50-7e7a-87e5-6a50dcd363c0"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testrun",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953544-7b51-7dc0-89b3-dd926415f16c"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="version",
            name="default_image",
            field=models.CharField(blank=True, default="", max_length=100),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="version",
            name="sut",
            field=models.ForeignKey(
                default="", on_delete=django.db.models.deletion.CASCADE, to="api.sut"
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="version",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953544-7b4c-76df-8a5b-df705586b39a"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="vulnerability",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953544-7b53-744e-b599-032490145e2c"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
    ]
