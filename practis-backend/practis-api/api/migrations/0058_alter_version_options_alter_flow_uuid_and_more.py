# Generated by Django 4.2.7 on 2025-02-28 19:10

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0057_alter_imagefile_options_alter_sut_options_and_more"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="version",
            options={"ordering": ["-uuid"]},
        ),
        migrations.AlterField(
            model_name="flow",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954df7-7fce-747a-a935-76161945437b"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="flowexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954df7-7fcf-7618-b1c1-b069f497bbf7"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="imagefile",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954df7-7fc9-77e9-97b2-37b58c7662fa"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="objectivesandscope",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954df7-7fcd-7bac-9383-e37a77515083"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="requirement",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954df7-7fd5-7736-aea0-9350625b9044"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="retrievetestexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954df7-7fd9-7bcf-9706-99766953f652"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="retrievevulns",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954df7-7fd8-7896-9d45-d607fada107e"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="risk",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954df7-7fd4-728f-8e97-fac6d45ec813"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="riskanalysis",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954df7-7fd2-7678-b9c4-e07dfea71966"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="sut",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954df7-7fcb-7673-b110-a416c0686ff3"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="sutprovider",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954df7-7fca-704d-9e7e-5cd0f69db216"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testcase",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954df7-7fd6-71a6-a0ff-629c5c17f029"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954df7-7fd7-7602-bffe-920353cd3e3e"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testplan",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954df7-7fd0-719d-b91b-3a8915f2e449"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testrun",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954df7-7fd1-7fde-8c52-54fe4647e740"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="version",
            name="images",
            field=models.ManyToManyField(blank=True, to="api.imagefile"),
        ),
        migrations.AlterField(
            model_name="version",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954df7-7fcc-739a-b5cb-fb835182abb5"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="vulnerability",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01954df7-7fd3-7080-b4c0-0d759a8cecfc"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
    ]
