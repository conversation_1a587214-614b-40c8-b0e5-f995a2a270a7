# Generated by Django 4.2.7 on 2025-03-26 21:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    replaces = [
        ("api", "0074_remove_testcase_tcms_external_id"),
        ("api", "0075_remove_testexecution_tcms_external_id_and_more"),
        ("api", "0076_testcase_plan"),
        ("api", "0077_testplan_version"),
    ]

    dependencies = [
        ("api", "0073_version_diagram_json"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="testcase",
            name="tcms_external_id",
        ),
        migrations.RemoveField(
            model_name="testexecution",
            name="tcms_external_id",
        ),
        migrations.RemoveField(
            model_name="testplan",
            name="tcms_external_id",
        ),
        migrations.RemoveField(
            model_name="testrun",
            name="tcms_external_id",
        ),
        migrations.RemoveField(
            model_name="version",
            name="tcms_external_id",
        ),
        migrations.AddField(
            model_name="testcase",
            name="plan",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="api.testplan",
            ),
        ),
        migrations.AddField(
            model_name="testplan",
            name="version",
            field=models.ForeignKey(
                default=2, on_delete=django.db.models.deletion.CASCADE, to="api.version"
            ),
            preserve_default=False,
        ),
    ]
