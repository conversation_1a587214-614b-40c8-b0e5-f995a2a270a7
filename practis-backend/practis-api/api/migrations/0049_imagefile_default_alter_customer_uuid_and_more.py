# Generated by Django 4.2.7 on 2025-02-24 10:29

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0048_alter_customer_uuid_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="imagefile",
            name="default",
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name="customer",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953781-ab37-746d-8bf8-36c3016b4a77"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="customerobjectivesandscope",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953781-ab3a-7280-8478-6c7b03c43a61"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="flow",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953781-ab3b-7676-ac08-952ba74208ef"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="flowexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953781-ab3c-7f9b-81e2-8fd5c3f0d53f"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="imagefile",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953781-ab36-7775-a66d-e5b861a529ff"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="requirement",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953781-ab42-766c-9f7f-2987ca7a6237"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="retrievetestexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953781-ab46-7f8d-9385-29961606ef16"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="retrievevulns",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953781-ab45-7796-a4c4-fece9cb4f3ce"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="risk",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953781-ab41-7544-bd65-7d3a05d0f12d"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="riskanalysis",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953781-ab3f-7a86-ac02-3c3045de5517"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="sut",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953781-ab38-7338-90ce-00b54e84bbb4"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testcase",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953781-ab43-7255-9070-9334a36e4e4f"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953781-ab44-79f5-bdd9-4373086aef8d"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testplan",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953781-ab3d-7d24-b7b5-5853ed9e2d6b"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testrun",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953781-ab3e-7b22-be83-133c0c86eec2"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="version",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953781-ab39-700b-bf02-f1f52cbe3161"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="vulnerability",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953781-ab40-7835-9a87-78a089c89559"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
    ]
