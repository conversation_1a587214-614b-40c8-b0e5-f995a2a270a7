# Generated by Django 4.2.7 on 2025-02-21 14:13

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0046_flow_imagefile_delete_attackpathorvector_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="customerobjectivesandscope",
            name="sut_version",
            field=models.ForeignKey(
                null=True, on_delete=django.db.models.deletion.CASCADE, to="api.version"
            ),
        ),
        migrations.AddField(
            model_name="flowexecution",
            name="flow",
            field=models.ForeignKey(
                db_column="flow",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="api.flow",
            ),
        ),
        migrations.AddField(
            model_name="flowexecution",
            name="sut_version",
            field=models.ForeignKey(
                null=True, on_delete=django.db.models.deletion.CASCADE, to="api.sut"
            ),
        ),
        migrations.AddField(
            model_name="requirement",
            name="sut_version",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="api.version",
            ),
        ),
        migrations.AddField(
            model_name="risk",
            name="risk_analysis",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="api.riskanalysis",
            ),
        ),
        migrations.AddField(
            model_name="risk",
            name="vulnerability",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="api.vulnerability",
            ),
        ),
        migrations.AddField(
            model_name="riskanalysis",
            name="sut_version",
            field=models.ForeignKey(
                null=True, on_delete=django.db.models.deletion.CASCADE, to="api.version"
            ),
        ),
        migrations.AddField(
            model_name="sut",
            name="customer",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="api.customer",
            ),
        ),
        migrations.AddField(
            model_name="testcase",
            name="requirement",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="api.requirement",
            ),
        ),
        migrations.AddField(
            model_name="testcase",
            name="vulnerability",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="api.vulnerability",
            ),
        ),
        migrations.AddField(
            model_name="testrun",
            name="plan",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="api.testplan",
            ),
        ),
        migrations.AddField(
            model_name="testrun",
            name="sut",
            field=models.ForeignKey(
                null=True, on_delete=django.db.models.deletion.SET_NULL, to="api.sut"
            ),
        ),
        migrations.AddField(
            model_name="version",
            name="sut",
            field=models.ForeignKey(
                null=True, on_delete=django.db.models.deletion.CASCADE, to="api.sut"
            ),
        ),
        migrations.AlterField(
            model_name="customer",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528da-eabc-77e7-b0df-0d24d3e0b615"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="customerobjectivesandscope",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528da-eabf-7da6-9b83-02f2f82ad286"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="flow",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528da-eac0-71b8-a47c-c095d648f838"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="flowexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528da-eac1-7278-9a72-3d707347f94d"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="imagefile",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528da-eabb-7e8e-9f9a-e27781d74d23"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="requirement",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528da-eac7-70f6-865b-eae3f61dab4b"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="retrievetestexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528da-eacb-76d8-80a4-4c2f43fb0e95"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="retrievevulns",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528da-eaca-75bd-98b9-ae7273f6b814"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="risk",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528da-eac6-7c66-b0a5-d2cd014ee8d3"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="riskanalysis",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528da-eac4-7551-b15e-87539bbf3898"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="sut",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528da-eabd-7bbe-aa85-4e824c570d49"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testcase",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528da-eac8-7f79-9088-8a1109d33260"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528da-eac9-728a-967a-1367282d926e"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testplan",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528da-eac2-73cd-8a9b-8bc3baa6be38"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testrun",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528da-eac3-75e5-85da-7eaa37c94f02"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="version",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528da-eabe-7b3e-882a-5ef18f10abba"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="vulnerability",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528da-eac5-7434-9bfb-584ee5e7f80b"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
    ]
