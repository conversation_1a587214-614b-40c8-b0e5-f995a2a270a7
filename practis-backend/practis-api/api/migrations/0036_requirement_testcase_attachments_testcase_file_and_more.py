# Generated by Django 4.2.7 on 2024-08-29 10:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0035_remove_testcase_attack_technic_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Requirement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=250)),
                ("description", models.TextField()),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("Low", "Low"),
                            ("Medium", "Medium"),
                            ("High", "High"),
                            ("Critical", "Critical"),
                        ],
                        default="Low",
                        max_length=50,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("Pending", "Pending"),
                            ("In Progress", "In Progress"),
                            ("Completed", "Completed"),
                            ("Cancelled", "Cancelled"),
                        ],
                        default="Pending",
                        max_length=50,
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="testcase",
            name="attachments",
            field=models.JSONField(default={"files": []}),
        ),
        migrations.AddField(
            model_name="testcase",
            name="file",
            field=models.FileField(blank=True, null=True, upload_to=""),
        ),
        migrations.AddField(
            model_name="testplan",
            name="attachments",
            field=models.JSONField(default={"files": []}),
        ),
        migrations.AddField(
            model_name="testrun",
            name="attachments",
            field=models.JSONField(default={"files": []}),
        ),
        migrations.AddField(
            model_name="testrun",
            name="file",
            field=models.FileField(blank=True, null=True, upload_to=""),
        ),
        migrations.AlterField(
            model_name="testexecution",
            name="test_case",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="api.testcase"
            ),
        ),
        migrations.AlterField(
            model_name="testexecution",
            name="test_run",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="api.testrun"
            ),
        ),
        migrations.AddField(
            model_name="testcase",
            name="requirement",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="api.requirement",
            ),
        ),
    ]
