# Generated by Django 4.2.7 on 2024-03-14 13:22

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0001_initial"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="noderedflow",
            name="nodered_flow",
        ),
        migrations.RemoveField(
            model_name="riskanalysis",
            name="metrics",
        ),
        migrations.AddField(
            model_name="noderedflow",
            name="address",
            field=models.URLField(default="http://localhost:8000"),
        ),
        migrations.AddField(
            model_name="noderedflow",
            name="api_url",
            field=models.URLField(default="http://localhost:8000"),
        ),
        migrations.AddField(
            model_name="noderedflow",
            name="parameter",
            field=models.CharField(default="parameter", max_length=250),
        ),
        migrations.AddField(
            model_name="riskanalysis",
            name="sut_version",
            field=models.ForeignKey(
                default=1, on_delete=django.db.models.deletion.CASCADE, to="api.version"
            ),
        ),
    ]
