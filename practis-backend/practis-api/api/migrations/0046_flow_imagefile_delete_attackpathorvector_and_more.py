# Generated by Django 4.2.7 on 2025-02-21 13:50

import api.validators
import django.contrib.postgres.fields
from django.db import migrations, models
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0045_remove_testexecution_comments_remove_testrun_file_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Flow",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.UUID("019528c6-27ec-7b53-8fa0-1811530a1c48"),
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("name", models.CharField(max_length=250)),
                ("description", models.TextField(blank=True)),
                ("address", models.URLField(default="http://localhost:8000")),
                ("api_url", models.URLField(default="http://localhost:8000")),
            ],
            options={
                "db_table": "flow",
            },
        ),
        migrations.CreateModel(
            name="ImageFile",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.UUID("019528c6-27e7-7320-aa4a-8cfeae261b84"),
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("path", models.CharField(max_length=250)),
                (
                    "file",
                    models.FileField(
                        blank=True, null=True, serialize=False, upload_to="media/images"
                    ),
                ),
            ],
            options={
                "db_table": "image_file",
            },
        ),
        migrations.DeleteModel(
            name="AttackPathOrVector",
        ),
        migrations.RemoveField(
            model_name="noderedflow",
            name="asset",
        ),
        migrations.RenameField(
            model_name="version",
            old_name="version",
            new_name="name",
        ),
        migrations.RemoveField(
            model_name="customer",
            name="id",
        ),
        migrations.RemoveField(
            model_name="customerobjectivesandscope",
            name="assurance_level",
        ),
        migrations.RemoveField(
            model_name="customerobjectivesandscope",
            name="customer",
        ),
        migrations.RemoveField(
            model_name="customerobjectivesandscope",
            name="id",
        ),
        migrations.RemoveField(
            model_name="customerobjectivesandscope",
            name="risk_level_objective",
        ),
        migrations.RemoveField(
            model_name="customerobjectivesandscope",
            name="test_coverage_objective",
        ),
        migrations.RemoveField(
            model_name="flowexecution",
            name="id",
        ),
        migrations.RemoveField(
            model_name="flowexecution",
            name="nodered_flow",
        ),
        migrations.RemoveField(
            model_name="flowexecution",
            name="sut",
        ),
        migrations.RemoveField(
            model_name="requirement",
            name="id",
        ),
        migrations.RemoveField(
            model_name="requirement",
            name="sut",
        ),
        migrations.RemoveField(
            model_name="retrievetestexecution",
            name="id",
        ),
        migrations.RemoveField(
            model_name="retrievetestexecution",
            name="sut_id",
        ),
        migrations.RemoveField(
            model_name="retrievevulns",
            name="id",
        ),
        migrations.RemoveField(
            model_name="risk",
            name="id",
        ),
        migrations.RemoveField(
            model_name="risk",
            name="risk_analysis",
        ),
        migrations.RemoveField(
            model_name="riskanalysis",
            name="id",
        ),
        migrations.RemoveField(
            model_name="riskanalysis",
            name="sut_version",
        ),
        migrations.RemoveField(
            model_name="sut",
            name="assets",
        ),
        migrations.RemoveField(
            model_name="sut",
            name="customer",
        ),
        migrations.RemoveField(
            model_name="sut",
            name="id",
        ),
        migrations.RemoveField(
            model_name="testcase",
            name="id",
        ),
        migrations.RemoveField(
            model_name="testcase",
            name="requirement",
        ),
        migrations.RemoveField(
            model_name="testcase",
            name="vulnerability",
        ),
        migrations.RemoveField(
            model_name="testexecution",
            name="id",
        ),
        migrations.RemoveField(
            model_name="testplan",
            name="id",
        ),
        migrations.RemoveField(
            model_name="testrun",
            name="id",
        ),
        migrations.RemoveField(
            model_name="testrun",
            name="plan",
        ),
        migrations.RemoveField(
            model_name="testrun",
            name="sut",
        ),
        migrations.RemoveField(
            model_name="version",
            name="id",
        ),
        migrations.RemoveField(
            model_name="version",
            name="sut",
        ),
        migrations.RemoveField(
            model_name="vulnerability",
            name="asset",
        ),
        migrations.RemoveField(
            model_name="vulnerability",
            name="id",
        ),
        migrations.RemoveField(
            model_name="vulnerability",
            name="risks",
        ),
        migrations.AddField(
            model_name="customer",
            name="address",
            field=models.JSONField(
                default=dict, validators=[api.validators.validate_customer_address]
            ),
        ),
        migrations.AddField(
            model_name="customer",
            name="contacts",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.JSONField(default=dict),
                default=list,
                size=None,
                validators=[api.validators.validate_customer_contact],
            ),
        ),
        migrations.AddField(
            model_name="customer",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528c6-27e8-7cbd-8ad0-aa650dccb3eb"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AddField(
            model_name="customerobjectivesandscope",
            name="scope",
            field=models.CharField(default="", max_length=250),
        ),
        migrations.AddField(
            model_name="customerobjectivesandscope",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528c6-27eb-754f-8776-0b3ad2628e4c"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AddField(
            model_name="flowexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528c6-27ed-7d58-8f07-9cc3e0882661"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AddField(
            model_name="requirement",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528c6-27f3-7af2-a678-78fca2e38753"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AddField(
            model_name="retrievetestexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528c6-27f7-761c-99c1-1ebcaa2e2788"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AddField(
            model_name="retrievevulns",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528c6-27f6-7951-a615-afee3de042c6"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AddField(
            model_name="risk",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528c6-27f2-70fe-a436-53f40336a537"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AddField(
            model_name="riskanalysis",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528c6-27f0-79d4-b9c9-2eb0c5ee3e8a"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AddField(
            model_name="sut",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528c6-27e9-7767-b46e-90f8caf398f7"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AddField(
            model_name="testcase",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528c6-27f4-7df4-b0ce-6b4d8114e0dc"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AddField(
            model_name="testexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528c6-27f5-7a35-a200-0d25325d8d02"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AddField(
            model_name="testplan",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528c6-27ee-7546-899f-4aacee025e7c"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AddField(
            model_name="testrun",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528c6-27ef-72a1-b67a-78b6822df341"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AddField(
            model_name="version",
            name="default_image",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="version",
            name="description",
            field=models.TextField(blank=True, default=""),
        ),
        migrations.AddField(
            model_name="version",
            name="notes",
            field=models.TextField(blank=True, default="", null=True),
        ),
        migrations.AddField(
            model_name="version",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528c6-27ea-768d-8f55-e6822559604c"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AddField(
            model_name="vulnerability",
            name="gvm_uuid",
            field=models.CharField(editable=False, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="flowexecution",
            name="report",
            field=models.FileField(
                blank=True, null=True, upload_to="media/flow_reports"
            ),
        ),
        migrations.AlterField(
            model_name="riskanalysis",
            name="name",
            field=models.CharField(max_length=200),
        ),
        migrations.AlterField(
            model_name="vulnerability",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("019528c6-27f1-76d1-b4a3-4cbcdb4ca5b4"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.DeleteModel(
            name="Asset",
        ),
        migrations.DeleteModel(
            name="NodeRedFlow",
        ),
        migrations.AddField(
            model_name="customer",
            name="images",
            field=models.ManyToManyField(blank=True, to="api.imagefile"),
        ),
        migrations.AddField(
            model_name="version",
            name="images",
            field=models.ManyToManyField(blank=True, null=True, to="api.imagefile"),
        ),
    ]
