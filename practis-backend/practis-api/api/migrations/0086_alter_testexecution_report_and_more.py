# Generated by Django 4.2.7 on 2025-05-26 08:19

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0085_alter_testexecution_test_case_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="testexecution",
            name="report",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="testexecution",
            name="status",
            field=models.Char<PERSON>ield(
                choices=[
                    ("PASSED", "PASSED"),
                    ("WAVED", "WAVED"),
                    ("PAUSED", "PAUSED"),
                    ("FAILED", "FAILED"),
                    ("RUNNING", "RUNNING"),
                    ("IDLE", "IDLE"),
                    ("BLOCKED", "BLOCKED"),
                    ("ERROR", "ERROR"),
                ],
                default="IDLE",
                max_length=50,
            ),
        ),
    ]
