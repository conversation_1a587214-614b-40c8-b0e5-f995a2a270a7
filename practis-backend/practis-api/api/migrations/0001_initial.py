# Generated by Django 4.2.7 on 2024-03-14 13:20

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Asset",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=250)),
                ("description", models.TextField(blank=True)),
                ("confidentiality", models.BooleanField(default=True)),
                ("integrity", models.BooleanField(default=True)),
                ("availability", models.BooleanField(default=True)),
                ("parameters", models.J<PERSON><PERSON>ield()),
                ("type", models.CharField(max_length=150)),
            ],
        ),
        migrations.CreateModel(
            name="AttackPathOrVector",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=250)),
                ("description", models.TextField(blank=True)),
            ],
            options={
                "db_table": "attack_path_or_vector",
            },
        ),
        migrations.CreateModel(
            name="Customer",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=250)),
                ("description", models.TextField(blank=True)),
            ],
        ),
        migrations.CreateModel(
            name="Risk",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=250)),
                ("description", models.TextField(blank=True)),
                ("value", models.IntegerField()),
                ("level", models.CharField(max_length=250)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("new", "new"),
                            ("confirmed", "confirmed"),
                            ("unconfirmed", "unconfirmed"),
                            ("treated", "treated"),
                        ],
                        default="new",
                        max_length=50,
                    ),
                ),
            ],
            options={
                "db_table": "risk",
            },
        ),
        migrations.CreateModel(
            name="RiskAnalysis",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("docs", models.URLField()),
                ("metrics", models.IntegerField()),
            ],
            options={
                "db_table": "risk_analysis",
            },
        ),
        migrations.CreateModel(
            name="Sut",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=250)),
                ("description", models.TextField(blank=True)),
                ("assets", models.ManyToManyField(blank=True, to="api.asset")),
                (
                    "customer",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="api.customer",
                    ),
                ),
            ],
            options={
                "db_table": "sut",
            },
        ),
        migrations.CreateModel(
            name="TestScript",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=250)),
                ("description", models.TextField(blank=True)),
                ("source", models.URLField()),
            ],
            options={
                "db_table": "test_script",
            },
        ),
        migrations.CreateModel(
            name="Vulnerability",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=250)),
                ("description", models.TextField(blank=True)),
                ("cve_cwe", models.TextField(blank=True)),
                (
                    "attack_path_or_vector",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="api.attackpathorvector",
                    ),
                ),
                ("risks", models.ManyToManyField(blank=True, to="api.risk")),
            ],
            options={
                "db_table": "vulnerability",
            },
        ),
        migrations.CreateModel(
            name="Version",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("version", models.CharField(max_length=250)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("in development", "in development"),
                            ("in production", "in production"),
                        ],
                        default="in development",
                        max_length=50,
                    ),
                ),
                (
                    "sut",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="api.sut"
                    ),
                ),
            ],
            options={
                "db_table": "sut_version",
            },
        ),
        migrations.CreateModel(
            name="TestSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=250)),
                ("description", models.TextField(blank=True)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                (
                    "sut",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="api.sut",
                    ),
                ),
            ],
            options={
                "db_table": "test_session",
            },
        ),
        migrations.CreateModel(
            name="TestScriptExecution",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateTimeField()),
                (
                    "result",
                    models.CharField(
                        choices=[("pass", "pass"), ("fail", "fail"), ("n/a", "n/a")],
                        default="n/a",
                        max_length=50,
                    ),
                ),
                ("report", models.URLField()),
                (
                    "test_script",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="api.testscript"
                    ),
                ),
                (
                    "test_session",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="api.testsession",
                    ),
                ),
            ],
            options={
                "db_table": "test_script_execution",
            },
        ),
        migrations.AddField(
            model_name="testscript",
            name="vulnerability",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="api.vulnerability",
            ),
        ),
        migrations.AddField(
            model_name="risk",
            name="risk_analysis",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="api.riskanalysis",
            ),
        ),
        migrations.CreateModel(
            name="NodeRedFlow",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=250)),
                ("description", models.TextField(blank=True)),
                ("nodered_flow", models.URLField()),
                (
                    "asset",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="api.asset",
                    ),
                ),
            ],
            options={
                "db_table": "nodered_flow",
            },
        ),
        migrations.CreateModel(
            name="FlowExecution",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateTimeField()),
                (
                    "result",
                    models.CharField(
                        choices=[("pass", "pass"), ("fail", "fail"), ("n/a", "n/a")],
                        default="n/a",
                        max_length=50,
                    ),
                ),
                ("report", models.FileField(null=True, upload_to="media/")),
                (
                    "nodered_flow",
                    models.ForeignKey(
                        db_column="nodered_flow",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="api.noderedflow",
                    ),
                ),
                (
                    "sut",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="api.sut",
                    ),
                ),
            ],
            options={
                "db_table": "flow_execution",
            },
        ),
        migrations.CreateModel(
            name="CustomerObjectivesAndScope",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "objectives",
                    models.CharField(
                        choices=[
                            ("evaluating security", "evaluating security"),
                            ("validating risk analysis", "validating risk analysis"),
                            (
                                "verifying corrected vulnerabilities",
                                "verifying corrected vulnerabilities",
                            ),
                        ],
                        default="evaluating security",
                        max_length=100,
                    ),
                ),
                ("risk_level_objective", models.TextField()),
                ("test_coverage_objective", models.TextField()),
                ("assurance_level", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "customer",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="api.customer",
                    ),
                ),
            ],
        ),
    ]
