# Generated by Django 4.2.7 on 2024-04-26 11:47

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0003_vulnerability_asset"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="vulnerability",
            options={"ordering": ["name"]},
        ),
        migrations.AddField(
            model_name="vulnerability",
            name="uuid",
            field=models.CharField(
                help_text="don't fill if you create an instance manually",
                max_length=100,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="vulnerability",
            name="asset",
            field=models.ForeignKey(
                default=1, on_delete=django.db.models.deletion.CASCADE, to="api.asset"
            ),
        ),
        migrations.AlterField(
            model_name="vulnerability",
            name="attack_path_or_vector",
            field=models.CharField(blank=True, default="", max_length=300, null=True),
        ),
        migrations.CreateModel(
            name="Scan",
            fields=[
                (
                    "id",
                    models.Char<PERSON>ield(
                        default="openvas",
                        max_length=100,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(default="task created from API", max_length=200),
                ),
                ("comment", models.CharField(blank=True, max_length=250)),
                (
                    "scanner",
                    models.CharField(
                        choices=[("OpenVAS Default", "openvas"), ("CVE", "cve")],
                        max_length=50,
                    ),
                ),
                (
                    "config",
                    models.CharField(
                        choices=[("Full and fast", "Full and fast")],
                        default="Full and fast",
                        max_length=100,
                    ),
                ),
                (
                    "date",
                    models.DateTimeField(default=django.utils.timezone.now, null=True),
                ),
                (
                    "asset",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="api.asset"
                    ),
                ),
            ],
            options={
                "db_table": "scan",
            },
        ),
        migrations.CreateModel(
            name="RetrieveVulns",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "scan",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="api.scan"
                    ),
                ),
            ],
            options={
                "db_table": "retrieval",
            },
        ),
        migrations.AddField(
            model_name="vulnerability",
            name="scan",
            field=models.ForeignKey(
                default=1, on_delete=django.db.models.deletion.CASCADE, to="api.scan"
            ),
        ),
    ]
