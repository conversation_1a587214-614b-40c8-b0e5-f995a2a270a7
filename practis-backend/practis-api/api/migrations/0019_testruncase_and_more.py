# Generated by Django 4.2.7 on 2024-06-10 14:44

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        (
            "api",
            "0018_rename_retrievetestscriptexecution_retrievetestexecution_and_more",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="TestRunCase",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "action",
                    models.CharField(
                        choices=[("add", "add"), ("remove", "remove")],
                        default="add",
                        max_length=250,
                    ),
                ),
            ],
            options={
                "managed": False,
            },
        ),
        migrations.RenameField(
            model_name="testexecution",
            old_name="test_script",
            new_name="test_case",
        ),
        migrations.RenameField(
            model_name="testexecution",
            old_name="test_session",
            new_name="test_run",
        ),
        migrations.RemoveField(
            model_name="testexecution",
            name="result",
        ),
        migrations.AddField(
            model_name="testcase",
            name="id_kiwi",
            field=models.CharField(blank=True, max_length=250, null=True),
        ),
        migrations.AddField(
            model_name="testexecution",
            name="id_kiwi",
            field=models.CharField(blank=True, max_length=250, null=True),
        ),
        migrations.AddField(
            model_name="testexecution",
            name="status",
            field=models.CharField(
                choices=[
                    ("PASSED", "PASSED"),
                    ("WAVED", "WAVED"),
                    ("PAUSED", "PAUSED"),
                    ("FAILED", "FAILED"),
                    ("RUNNING", "RUNNING"),
                    ("IDLE", "IDLE"),
                    ("BLOCKED", "BLOCKED"),
                    ("ERROR", "ERROR"),
                ],
                default="ERROR",
                max_length=50,
            ),
        ),
        migrations.AddField(
            model_name="testrun",
            name="id_kiwi",
            field=models.CharField(blank=True, max_length=250, null=True),
        ),
        migrations.AlterModelTable(
            name="testcase",
            table="test_case",
        ),
        migrations.AlterModelTable(
            name="testexecution",
            table="test_execution",
        ),
        migrations.AlterModelTable(
            name="testrun",
            table="test_run",
        ),
    ]
