# Generated by Django 4.2.7 on 2024-06-13 10:56

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0024_testrun_plan"),
    ]

    operations = [
        migrations.CreateModel(
            name="TestPlan",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.Char<PERSON>ield(max_length=250)),
                ("id_kiwi", models.<PERSON>r<PERSON>ield(blank=True, max_length=250, null=True)),
            ],
            options={
                "db_table": "test_plan",
            },
        ),
        migrations.AlterField(
            model_name="testrun",
            name="plan",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="api.testplan",
            ),
        ),
    ]
