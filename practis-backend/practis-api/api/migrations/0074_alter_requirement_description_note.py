# Generated by Django 4.2.7 on 2025-04-07 09:30

from django.db import migrations, models
import django.db.models.deletion
import uuid6


class Migration(migrations.Migration):
    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("api", "0073_version_diagram_json"),
    ]

    operations = [
        migrations.AlterField(
            model_name="requirement",
            name="description",
            field=models.TextField(null=True),
        ),
        migrations.CreateModel(
            name="Note",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid6.uuid7,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("object_id", models.UUIDField()),
                ("text", models.TextField()),
                (
                    "content_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
            ],
            options={
                "db_table": "note",
            },
        ),
    ]
