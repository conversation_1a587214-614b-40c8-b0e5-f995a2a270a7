# Generated by Django 4.2.7 on 2025-05-16 13:07

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0084_testrun_version"),
    ]

    operations = [
        migrations.RemoveField('testexecution', 'test_case'),
        migrations.RemoveField('testexecution', 'test_run'),
        migrations.AddField(
            model_name="testexecution",
            name="test_case",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="api.testcase"
            ),
        ),
        migrations.AddField(
            model_name="testexecution",
            name="test_run",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="api.testrun"
            ),
        ),
    ]
