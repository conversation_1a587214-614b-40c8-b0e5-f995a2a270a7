# Generated by Django 4.2.7 on 2025-02-24 14:11

import api.validators
import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0049_imagefile_default_alter_customer_uuid_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ObjectivesAndScope",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.UUID("0195384b-ce5e-77ff-9438-5da338bc4282"),
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "objectives",
                    models.CharField(
                        choices=[
                            ("evaluating security", "evaluating security"),
                            ("validating risk analysis", "validating risk analysis"),
                            (
                                "verifying corrected vulnerabilities",
                                "verifying corrected vulnerabilities",
                            ),
                        ],
                        default="evaluating security",
                        max_length=100,
                    ),
                ),
                ("scope", models.Char<PERSON>ield(default="", max_length=250)),
                ("description", models.TextField(blank=True, null=True)),
            ],
            options={
                "db_table": "objectives_and_scope",
            },
        ),
        migrations.CreateModel(
            name="SutProvider",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.UUID("0195384b-ce5b-7f37-8234-73aadd908bfd"),
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("name", models.CharField(max_length=250)),
                ("description", models.TextField(blank=True)),
                (
                    "address",
                    models.JSONField(
                        default=dict,
                        validators=[api.validators.validate_customer_address],
                    ),
                ),
                (
                    "contacts",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.JSONField(default=dict),
                        default=list,
                        size=None,
                        validators=[api.validators.validate_customer_contact],
                    ),
                ),
            ],
            options={
                "db_table": "sut_provider",
            },
        ),
        migrations.RemoveField(
            model_name="customerobjectivesandscope",
            name="sut_version",
        ),
        migrations.RemoveField(
            model_name="sut",
            name="customer",
        ),
        migrations.AlterField(
            model_name="flow",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("0195384b-ce5f-7e66-a42c-6912109cdbd4"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="flowexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("0195384b-ce60-78fe-b671-decbd945c829"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="imagefile",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("0195384b-ce5a-7032-b5fc-09624a123433"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="requirement",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("0195384b-ce66-7be1-a373-3d1a01a54f07"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="retrievetestexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("0195384b-ce6a-7aa2-a9d1-251001c8a271"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="retrievevulns",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("0195384b-ce69-73d7-8537-fa097fe16211"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="risk",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("0195384b-ce65-796a-a196-a0ad523f8ace"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="riskanalysis",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("0195384b-ce63-7c41-a008-e63d00dcba4c"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="sut",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("0195384b-ce5c-72e6-89ed-ae2afc31c2f7"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testcase",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("0195384b-ce67-7e26-845b-74afe0f5e28c"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("0195384b-ce68-78ce-b57a-09ce2a9fdf97"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testplan",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("0195384b-ce61-72b6-a6ac-a3643df861ea"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testrun",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("0195384b-ce62-749c-90a0-aa6a1ef8f74f"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="version",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("0195384b-ce5d-79aa-947d-9dc7f3a80b9f"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="vulnerability",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("0195384b-ce64-7e62-ad10-4ab7d532bb64"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.DeleteModel(
            name="Customer",
        ),
        migrations.DeleteModel(
            name="CustomerObjectivesAndScope",
        ),
        migrations.AddField(
            model_name="sutprovider",
            name="images",
            field=models.ManyToManyField(blank=True, to="api.imagefile"),
        ),
        migrations.AddField(
            model_name="objectivesandscope",
            name="sut_version",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="api.version"
            ),
        ),
        migrations.AddField(
            model_name="sut",
            name="sut_provider",
            field=models.ForeignKey(
                default="",
                on_delete=django.db.models.deletion.CASCADE,
                to="api.sutprovider",
            ),
            preserve_default=False,
        ),
    ]
