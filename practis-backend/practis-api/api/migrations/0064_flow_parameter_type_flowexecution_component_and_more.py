# Generated by Django 4.2.7 on 2025-03-04 23:41

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("diagram", "0002_component_version_interface_version_port_version_and_more"),
        ("api", "0063_alter_riskanalysis_options_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="flow",
            name="parameter_type",
            field=models.ManyToManyField(
                null=True, related_name="flows", to="diagram.parametertype"
            ),
        ),
        migrations.AddField(
            model_name="flowexecution",
            name="component",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="flowexecutions",
                to="diagram.component",
            ),
        ),
        migrations.AddField(
            model_name="flowexecution",
            name="interface",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="flowexecutions",
                to="diagram.interface",
            ),
        ),
        migrations.AddField(
            model_name="flowexecution",
            name="port",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="flowexecutions",
                to="diagram.port",
            ),
        ),
        migrations.AddField(
            model_name="flowexecution",
            name="subcomponent",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="flowexecutions",
                to="diagram.subcomponent",
            ),
        ),
        migrations.AddField(
            model_name="vulnerability",
            name="component",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="vulnerabilities",
                to="diagram.component",
            ),
        ),
        migrations.AddField(
            model_name="vulnerability",
            name="interface",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="vulnearbilities",
                to="diagram.interface",
            ),
        ),
        migrations.AddField(
            model_name="vulnerability",
            name="port",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="vulnerabilities",
                to="diagram.port",
            ),
        ),
        migrations.AddField(
            model_name="vulnerability",
            name="subcomponent",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="vulnerabilities",
                to="diagram.subcomponent",
            ),
        ),
    ]
