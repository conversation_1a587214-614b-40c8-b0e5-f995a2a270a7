# Generated by Django 4.2.7 on 2024-06-13 08:51

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0020_alter_retrievetestexecution_product"),
    ]

    operations = [
        migrations.AddField(
            model_name="testcase",
            name="category",
            field=models.CharField(
                blank=True, default="--default--", max_length=250, null=True
            ),
        ),
        migrations.AddField(
            model_name="testcase",
            name="priority",
            field=models.CharField(
                choices=[
                    ("1", "P1"),
                    ("2", "P2"),
                    ("3", "P3"),
                    ("4", "P4"),
                    ("5", "P5"),
                ],
                default="P1",
                max_length=250,
            ),
        ),
        migrations.AddField(
            model_name="testcase",
            name="status",
            field=models.Char<PERSON>ield(
                choices=[
                    ("PROPOSED", "PROPOSED"),
                    ("CONFIRMED", "CONFIRMED"),
                    ("DISABLED", "DISABLED"),
                    ("NEED UPDATE", "NEED UPDATE"),
                ],
                default="PROPOSED",
                max_length=250,
            ),
        ),
    ]
