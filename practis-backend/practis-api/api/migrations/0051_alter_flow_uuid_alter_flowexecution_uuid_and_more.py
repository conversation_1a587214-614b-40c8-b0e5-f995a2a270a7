# Generated by Django 4.2.7 on 2025-02-24 14:21

import api.validators
from django.db import migrations, models
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0050_objectivesandscope_sutprovider_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="flow",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953855-8ade-7e65-b6e8-6918f18c4bdd"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="flowexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953855-8adf-7262-9cd9-6fcef25b8815"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="imagefile",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953855-8ad9-71c3-899c-3b6e4ca4b0f8"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="objectivesandscope",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953855-8add-7c46-8370-2089668157d5"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="requirement",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953855-8ae5-78b5-9ef1-f2e75ac37245"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="retrievetestexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953855-8ae9-709a-89d9-aa45a3ec109f"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="retrievevulns",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953855-8ae8-7264-8be0-013df785b529"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="risk",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953855-8ae4-7b07-94fc-dc36ab1d9d7d"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="riskanalysis",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953855-8ae2-7327-be22-a2316fa7be4c"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="sut",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953855-8adb-7cb5-86db-6722ee0ec627"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="sutprovider",
            name="address",
            field=models.JSONField(
                default={}, validators=[api.validators.validate_customer_address]
            ),
        ),
        migrations.AlterField(
            model_name="sutprovider",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953855-8ada-7e2b-98ab-d5258763dece"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testcase",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953855-8ae6-7734-b2ea-f7ca58df06da"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testexecution",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953855-8ae7-780e-aac4-e528e6deb7d4"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testplan",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953855-8ae0-749f-ab79-fd2ee13f7975"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="testrun",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953855-8ae1-7fe5-af99-7f1d917ebd52"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="version",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953855-8adc-72ed-a89b-a39368b7ffe0"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="vulnerability",
            name="uuid",
            field=models.UUIDField(
                default=uuid.UUID("01953855-8ae3-767c-8690-2a935eaaa0e4"),
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
    ]
