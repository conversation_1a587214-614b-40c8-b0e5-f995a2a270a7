# Generated by Django 4.2.7 on 2025-03-01 19:00

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0059_alter_flow_uuid_alter_flowexecution_uuid_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="flowexecution",
            name="flow",
            field=models.ForeignKey(
                db_column="flow",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="flowexecutions",
                to="api.flow",
            ),
        ),
        migrations.AlterField(
            model_name="objectivesandscope",
            name="sut_version",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="objectives",
                to="api.version",
            ),
        ),
        migrations.AlterField(
            model_name="requirement",
            name="sut_version",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="requirements",
                to="api.version",
            ),
        ),
        migrations.AlterField(
            model_name="risk",
            name="risk_analysis",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="risks",
                to="api.riskanalysis",
            ),
        ),
        migrations.AlterField(
            model_name="risk",
            name="vulnerability",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="risks",
                to="api.vulnerability",
            ),
        ),
        migrations.AlterField(
            model_name="riskanalysis",
            name="sut_version",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="riskanalysis",
                to="api.version",
            ),
        ),
    ]
