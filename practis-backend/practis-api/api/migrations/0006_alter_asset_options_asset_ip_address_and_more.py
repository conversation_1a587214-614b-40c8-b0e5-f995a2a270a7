# Generated by Django 4.2.7 on 2024-04-26 12:02

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0005_alter_asset_type_alter_asset_table_and_more"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="asset",
            options={"ordering": ["name"]},
        ),
        migrations.AddField(
            model_name="asset",
            name="ip_address",
            field=models.GenericIPAddressField(default="127.0.0.1"),
        ),
        migrations.AlterField(
            model_name="asset",
            name="type",
            field=models.CharField(
                choices=[("component", "Component"), ("interface", "Interface")],
                default="interface",
                max_length=150,
            ),
        ),
    ]
