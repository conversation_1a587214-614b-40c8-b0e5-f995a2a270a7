import json
import logging
from pathlib import Path

import requests
from apscheduler.schedulers.background import BackgroundScheduler
from django_apscheduler.jobstores import DjangoJobStore
from django.utils import timezone
from django.conf import settings
from django_apscheduler import util
from .models import Test<PERSON>ase, TestRun, Sut, TestPlan
from .serializers import TestPlanSerializer, TestRunSerializer

scheduler = BackgroundScheduler()
scheduler.add_jobstore(DjangoJobStore(), "default")


logger = logging.getLogger("api")


@util.close_old_connections
def files_sync():

    logger.info(f"TestCase Sync started...")
    queryset = TestCase.objects.all()
    for case in queryset:
        qs = TestCase.objects.get(tcms_external_id=case.tcms_external_id)

        # Specify the directory path
        folder_path = Path(
            f"{settings.ATTACHMENTS}/testcases_testcase/{case.tcms_external_id}"
        )

        # Check if the directory exists
        if folder_path.exists() and folder_path.is_dir():

            # List all files in the folder if it exists
            files = [str(f) for f in folder_path.iterdir() if f.is_file()]
        else:
            # Set to an empty list if the directory doesn't exist
            files = []

        qs.attachments = {"files": files}
        qs.save()

    logger.info(f"TestCase Sync completed!")


@util.close_old_connections
def testRun_sync():
    data = json.loads(
        requests.get(f"{settings.KIWI_BROKER}api/testrun/").content.decode("utf-8")
    )

    runs = []
    serializer_class = TestRunSerializer
    saving_problem = 0
    for item in data:
        sut = Sut.objects.get(tcms_external_id=str(item["build__version__product"])).id
        plan = TestPlan.objects.get(tcms_external_id=str(item["plan"])).id
        db_data = {
            "name": item["summary"],
            "description": item["notes"],
            "sut": sut,
            "tcms_external_id": str(item["id"]),
            "plan": plan,
            "attachments": {"files": []},
        }
        folder_path = Path(
            "{}/testruns_testrun/{}".format(
                settings.ATTACHMENTS, db_data.get("tcms_external_id")
            )
        )

        # Check if the directory exists
        if folder_path.exists() and folder_path.is_dir():
            # List all files in the folder if it exists
            files = [str(f) for f in folder_path.iterdir() if f.is_file()]
        else:
            # Set to an empty list if the directory doesn't exist
            files = []

        runs.append(db_data)
        try:
            temp = TestRun.objects.get(tcms_external_id=str(item["id"]))
            temp.attachments = {"files": files}
            temp.save()
        except:
            db_data["attachments"] = {"files": files}
            serializer = serializer_class(data=db_data)
            if serializer.is_valid():
                serializer.save()
            else:
                saving_problem += 1
    if saving_problem > 0:
        logger.error(f"TestRun Sync failed for {saving_problem} items")
    else:
        logger.info(f"TestRun Sync completed successfully!")


# @util.close_old_connections
def testPlan_sync():
    serializer_class = TestPlanSerializer
    problems = 0
    # populate the db with the data from the kiwi server
    try:
        data = json.loads(
            requests.get(f"{settings.KIWI_BROKER}api/testplan/").content.decode("utf-8")
        )
        # populate the db with the data from the kiwi server
        db_data = [
            {
                "name": item["name"],
                "tcms_external_id": item["id"],
                "attachments": {"files": []},
            }
            for item in data
        ]

        for item in db_data:
            folder_path = Path(
                "{}/testplans_testplan/{}".format(
                    settings.ATTACHMENTS, item.get("tcms_external_id")
                )
            )

            # Check if the directory exists
            if folder_path.exists() and folder_path.is_dir():
                # List all files in the folder if it exists
                files = [str(f) for f in folder_path.iterdir() if f.is_file()]
            else:
                # Set to an empty list if the directory doesn't exist
                files = []

            try:
                temp = TestPlan.objects.get(
                    tcms_external_id=str(item["tcms_external_id"])
                )
                temp.attachments = {"files": files}
                temp.save()
            except:
                item["attachments"] = {"files": files}
                serializer = serializer_class(data=item)
                if serializer.is_valid():
                    serializer.save()
                else:
                    problems += 1

    except Exception as e:
        logger.error(e.__str__)

    if problems > 0:
        logger.error(f"TestPlan Sync failed for {problems} items")
    else:
        logger.info(f"TestPlan Sync completed successfully!")


def start():
    scheduler.add_job(
        files_sync,
        trigger="interval",
        seconds=60,
        id="testcase_sync",
        max_instances=1,
        replace_existing=True,
    )

    scheduler.add_job(
        testPlan_sync,
        trigger="interval",
        seconds=60,
        id="testplan_sync",
        max_instances=1,
        replace_existing=True,
    )

    scheduler.add_job(
        testRun_sync,
        trigger="interval",
        seconds=60,
        id="testrun_sync",
        max_instances=1,
        replace_existing=True,
    )
    scheduler.start()
