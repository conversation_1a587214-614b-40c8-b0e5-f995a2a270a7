from pytest_factoryboy import register
from tests.factories import *
import pytest
from rest_framework.test import APIClient

# we register the models's factories

register(SutProviderFactory)

register(ObjectivesAndScopeFactory)

#register(AssetFactory)

register(SutFactory)

#register(SutWithAssetFactory)

register(VersionFactory)

register(FlowFactory)

register(FlowExecutionFactory)

register(TestPlanFactory)

register(TestRunFactory)

register(RiskAnalysisFactory)

register(RiskFactory)

register(VulnerabilityFactory)

register(RiskWithVulnerabilityFactory)

register(TestCaseFactory)

register(TestPlanFactory)

register(TestExecutionFactory)

register(RetrieveVulnsFactory)

register(RetrieveTestExecutionFactory)

register(ComponentFactory)

register(SubComponentFactory)

register(PortFactory)

register(InterfaceFactory)



# when we register a model factory it can be accessed by mapping the camelcase to the underscvore format
# for example CategoryFactory can be accessed with category_factory(Factory) method. It will create a new category with the name defined in the fatory class.
# for category, all rthe element will have the name "test_category".


@pytest.fixture
def api_client():
    return APIClient
