import pytest

pytestmark = pytest.mark.django_db


class Test_AssetView:
    endpoint = "/api/asset/"

    def test_list(self, asset_factory, api_client):
        # Arrange

        asset_factory()

        # Act

        response = api_client().get(self.endpoint)

        # Assert
        assert response.status_code == 200
        assert len(response.data) == 1

    def test_retrieve(self, asset_factory, api_client):
        asset_factory()
        response = api_client().get(f"{self.endpoint}1/")
        assert response.status_code == 200

    def test_create(self, api_client):
        response = api_client().post(
            self.endpoint,
            {
                "name": "Asset",
                "description": "description",
                "parameters": "{'name':'name'}",
                "type": "component",
            },
            format="json",
        )
        assert response.status_code == 201
        assert response.data["name"] == "Asset"
        assert response.data["description"] == "description"
        assert response.data["parameters"] == "{'name':'name'}"
        assert response.data["type"] == "component"

    def test_update(self, api_client):
        api_client().post(
            self.endpoint,
            {
                "name": "Asset",
                "description": "description",
                "parameters": "{'name':'name'}",
                "type": "component",
            },
            format="json",
        )
        response = api_client().put(
            f"{self.endpoint}1/",
            {
                "name": "Asset",
                "description": "description with update",
                "parameters": "{'name':'name'}",
                "type": "component",
            },
            format="json",
        )
        assert response.status_code == 200
        assert response.data["name"] == "Asset"
        assert response.data["description"] == "description with update"
        assert response.data["parameters"] == "{'name':'name'}"
        assert response.data["type"] == "component"

    def test_destroy(self, api_client):
        api_client().post(
            self.endpoint,
            {
                "name": "Asset",
                "description": "description",
                "parameters": "{'name':'name'}",
                "type": "component",
            },
            format="json",
        )
        response = api_client().delete(f"{self.endpoint}1/")
        assert response.status_code == 204
        assert response.data["message"] == "The object has been deleted"
