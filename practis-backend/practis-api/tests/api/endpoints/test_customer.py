import pytest

pytestmark = pytest.mark.django_db


class Test_SutProviderView:
    endpoint = "/api/sut_provider/"

    def test_list(self, sut_provider_factory, api_client):
        # Arrange

        sut_provider_factory()

        # Act

        response = api_client().get(self.endpoint)

        # Assert
        assert response.status_code == 200
        assert len(response.data) == 1
        assert response.data[0]["name"] == "SutProvider"
        assert response.data[0]["description"] == "description"

    def test_retrieve(self, sut_provider_factory, api_client):
        sut_provider_factory()
        response = api_client().get(f"{self.endpoint}1/")
        assert response.status_code == 200
        assert response.data["name"] == "SutProvider"
        assert response.data["description"] == "description"

    def test_create(self, api_client):
        response = api_client().post(
            self.endpoint,
            {"name": "testSutProvider", "description": "description"},
            format="json",
        )
        assert response.status_code == 201
        assert response.data["name"] == "testSutProvider"
        assert response.data["description"] == "description"

    def test_update(self, api_client):
        api_client().post(
            self.endpoint,
            {"name": "testSutProvider", "description": "description"},
            format="json",
        )
        response = api_client().put(
            f"{self.endpoint}1/",
            {"name": "clientUpdated", "description": "descriptionUpdated"},
            format="json",
        )
        assert response.status_code == 200
        assert response.data["name"] == "clientUpdated"
        assert response.data["description"] == "descriptionUpdated"

    def test_destroy(self, api_client):
        api_client().post(
            self.endpoint,
            {"name": "testSutProvider", "description": "description"},
            format="json",
        )
        response = api_client().delete(f"{self.endpoint}1/")
        assert response.status_code == 204
        assert response.data["message"] == "The object has been deleted"
