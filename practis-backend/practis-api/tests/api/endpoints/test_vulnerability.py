import pytest
from unittest import mock
import json

pytestmark = pytest.mark.django_db


def mocked_requests_post(*args, **kwargs):

    class MockResponse:
        def __init__(self, json_data, status_code):
            self.json_data = json_data
            self.status_code = status_code
            self.content = json.dumps(json_data).encode()

        def json(self):
            return self.json_data

    return MockResponse({"id": 2}, 200)


class Test_VulnerabilityView:
    endpoint = "/api/vulnerability/"

    def run_dependencies(self, api_client):
        api_client().post(
            "/api/sut_provider/",
            {"name": "testSutProvider", "description": "description"},
            format="json",
        )
        api_client().post(
            "/api/asset/",
            {
                "name": "Asset",
                "description": "description",
                "parameters": {"name": "name"},
                "type": "component",
            },
            format="json",
        )

    @mock.patch("requests.post", side_effect=mocked_requests_post)
    def run_sut_dependencies(self, api_client, mock_post):
        api_client().post(
            "/api/sut/",
            {
                "name": "Sut",
                "description": "description",
                "sut_provider": 1,
                "assets": [1],
            },
            format="json",
        )

        api_client().post(
            "/api/version/",
            {"version": "version", "status": "in development", "sut": 1},
        )

        api_client().post(
            "/api/riskanalysis/",
            {
                "name": "Risk Analysis",
                "docs": "http://docs.com/",
                "sut_version": 1,
            },
        )

        api_client().post(
            "/api/risk/",
            {
                "name": "Risk",
                "description": "description",
                "value": 1,
                "status": "new",
                "level": "high",
                "risk_analysis": 1,
            },
        )

    def test_list(self, vulnerability_factory, api_client):
        # Arrange

        vulnerability_factory()

        # Act

        response = api_client().get(self.endpoint)

        # Assert
        assert response.status_code == 200
        assert len(response.data) == 1

    def test_retrieve(self, vulnerability_factory, api_client):
        vulnerability_factory()
        response = api_client().get(f"{self.endpoint}1/")
        assert response.status_code == 200

    def test_create(self, api_client):
        self.run_dependencies(api_client)
        self.run_sut_dependencies(api_client)

        response = api_client().post(
            self.endpoint,
            {
                "uuid": "uuid",
                "name": "test Vulnerability",
                "description": "description",
                "cve_cwe": "cve_cwe",
                "status": "new",
                "attack_path_or_vector": "attack path vector",
                "asset": 1,
                "risks": [1],
            },
        )
        assert response.status_code == 201

    def test_update(self, api_client):
        self.run_dependencies(api_client)
        self.run_sut_dependencies(api_client)

        api_client().post(
            self.endpoint,
            {
                "uuid": "uuid",
                "name": "test Vulnerability",
                "description": "description",
                "cve_cwe": "cve_cwe",
                "status": "new",
                "attack_path_or_vector": "attack path vector",
                "asset": 1,
                "risks": [1],
            },
        )
        response = api_client().put(
            f"{self.endpoint}1/",
            {
                "uuid": "uuid",
                "name": "test Vulnerability",
                "description": "description with update",
                "cve_cwe": "cve_cwe",
                "status": "new",
                "attack_path_or_vector": "attack path vector",
                "asset": 1,
                "risks": [1],
            },
            format="json",
        )
        assert response.status_code == 200
        assert response.data["description"] == "description with update"

    def test_destroy(self, api_client):
        self.run_dependencies(api_client)
        self.run_sut_dependencies(api_client)

        api_client().post(
            self.endpoint,
            {
                "uuid": "uuid",
                "name": "test Vulnerability",
                "description": "description",
                "cve_cwe": "cve_cwe",
                "status": "new",
                "attack_path_or_vector": "attack path vector",
                "asset": 1,
                "risks": [1],
            },
        )
        response = api_client().delete(f"{self.endpoint}1/")
        assert response.status_code == 204
        assert response.data["message"] == "The object has been deleted"
