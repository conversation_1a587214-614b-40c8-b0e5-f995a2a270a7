import pytest
from unittest import mock
import json

pytestmark = pytest.mark.django_db


def mocked_requests_post(*args, **kwargs):
    class MockResponse:
        def __init__(self, json_data, status_code):
            self.json_data = json_data
            self.status_code = status_code
            self.content = json.dumps(json_data).encode()

        def json(self):
            return self.json_data

    return MockResponse({"id": 2}, 200)


class Test_TestCaseEndpoint:
    endpoint = "/api/testcase/"

    def run_dependencies(self, api_client):
        api_client().post(
            "/api/sut_provider/",
            {"name": "testSutProvider", "description": "description"},
            format="json",
        )
        api_client().post(
            "/api/asset/",
            {
                "name": "Asset",
                "description": "description",
                "parameters": {"name": "name"},
                "type": "component",
            },
            format="json",
        )

    @mock.patch("requests.post", side_effect=mocked_requests_post)
    def run_sut_dependencies(self, api_client, mock_post):
        api_client().post(
            "/api/sut/",
            {
                "name": "Sut",
                "description": "description",
                "sut_provider": 1,
                "assets": [1],
            },
            format="json",
        )

        api_client().post(
            "/api/version/",
            {"version": "version", "status": "in development", "sut": 1},
        )

        api_client().post(
            "/api/riskanalysis/",
            {
                "name": "Risk Analysis",
                "docs": "http://docs.com/",
                "sut_version": 1,
            },
        )

        api_client().post(
            "/api/risk/",
            {
                "name": "Risk",
                "description": "description",
                "value": 1,
                "status": "new",
                "level": "high",
                "risk_analysis": 1,
            },
        )

        api_client().post(
            "/api/vulnerability/",
            {
                "uuid": "uuid",
                "name": "Vulnerability",
                "description": "description",
                "cve_cwe": "cve_cwe",
                "risks": [1],
                "attack_path_or_vector": "attack_path_or_vector",
                "asset": 1,
                "status": "new",
            },
        )

    @mock.patch("requests.post", side_effect=mocked_requests_post)
    def create_test_case_instance(self, api_client, mock_post):
        response = api_client().post(
            "/api/testcase/",
            {
                "name": "Test case test",
                "description": "description",
                "attack_technique": "attack_technique",
                "status": "PROPOSED",
                "priority": 1,
                "category": "--default--",
                "tcms_external_id": 1,
                "vulnerability": 1,
                "tcms_external_id": 1,
            },
            format="json",
        )

        return response

    def test_list(self, test_case_factory, api_client):
        # Arrange

        test_case_factory()

        # Act

        response = api_client().get(self.endpoint)

        # Assert
        assert response.status_code == 200
        assert len(response.data) == 1

    def test_retrieve(self, test_case_factory, api_client):
        test_case_factory()
        response = api_client().get(f"{self.endpoint}1/")
        assert response.status_code == 200

    def test_create(self, api_client):
        self.run_dependencies(api_client)
        self.run_sut_dependencies(api_client)
        response = self.create_test_case_instance(api_client)

        assert response.status_code == 201
        assert response.data["name"] == "Test case test"

    @mock.patch("requests.put", side_effect=mocked_requests_post)
    def test_update(self, mock_put, api_client, test_case_factory):
        self.run_dependencies(api_client)
        self.run_sut_dependencies(api_client)
        response = self.create_test_case_instance(api_client)

        assert response.status_code == 201

        response = api_client().put(
            f"{self.endpoint}1/",
            {
                "name": "Test case test",
                "description": "description with update",
                "attack_technique": "attack_technique",
                "status": "PROPOSED",
                "priority": 1,
                "category": "--default--",
                "tcms_external_id": 1,
                "vulnerability": 1,
                "tcms_external_id": 1,
            },
            format="json",
        )
        assert response.status_code == 200
        assert response.data["description"] == "description with update"

    def test_destroy(self, api_client):
        self.run_dependencies(api_client)
        self.run_sut_dependencies(api_client)
        response = self.create_test_case_instance(api_client)

        assert response.status_code == 201
        response = api_client().delete(f"{self.endpoint}1/")
        assert response.status_code == 204
        assert response.data["message"] == "The object has been deleted"
