import pytest
from unittest import mock
from dotenv import load_dotenv
import json
import os

pytestmark = pytest.mark.django_db


class Test_FlowView:
    endpoint = "/api/flow/"

    def run_dependencies(self, api_client):
        api_client().post(
            "/api/asset/",
            {
                "name": "Asset",
                "description": "description",
                "confidentiality": True,
                "integrity": True,
                "availability": True,
                "parameters": {"name": "name"},
                "type": "component",
            },
            format="json",
        )

    def test_list(self, node_red_flow_factory, api_client):
        # Arrange

        node_red_flow_factory()

        # Act

        response = api_client().get(self.endpoint)

        # Assert
        assert response.status_code == 200
        assert len(response.data) == 1

    def test_retrieve(self, node_red_flow_factory, api_client):
        node_red_flow_factory()
        response = api_client().get(f"{self.endpoint}1/")
        assert response.status_code == 200

    def test_create(self, api_client):
        self.run_dependencies(api_client)
        response = api_client().post(
            self.endpoint,
            {
                "name": "NoderedFlow test",
                "description": "description",
                "address": "http://flow.com",
                "api_url": "http://api.com",
                "parameter": "parameters",
                "asset": 1,
            },
            format="json",
        )
        assert response.status_code == 201
        assert response.data["name"] == "NoderedFlow test"
        assert response.data["description"] == "description"
        assert response.data["address"] == "http://flow.com"
        assert response.data["api_url"] == "http://api.com"
        assert response.data["parameter"] == "parameters"
        assert response.data["asset"] == 1

    def test_update(self, node_red_flow_factory, api_client):
        self.run_dependencies(api_client)
        node_red_flow_factory()
        response = api_client().put(
            f"{self.endpoint}1/",
            {
                "name": "Flow",
                "description": "description with update",
                "address": "http://flow.com",
                "api_url": "http://api.com",
                "parameter": "parameters with update",
                "asset": 1,
            },
            format="json",
        )
        assert response.status_code == 200
        assert response.data["name"] == "Flow"
        assert response.data["description"] == "description with update"
        assert response.data["address"] == "http://flow.com"
        assert response.data["api_url"] == "http://api.com"
        assert response.data["parameter"] == "parameters with update"
        assert response.data["asset"] == 1

    def test_destroy(self, api_client):
        self.run_dependencies(api_client)
        api_client().post(
            self.endpoint,
            {
                "name": "Flow",
                "description": "description",
                "flow": "http://flow.com",
                "asset": 1,
            },
            format="json",
        )
        response = api_client().delete(f"{self.endpoint}1/")
        del_response = api_client().get(f"{self.endpoint}1/")
        assert del_response.status_code == 404
        assert response.status_code == 204
        assert response.data["message"] == "The object has been deleted"
