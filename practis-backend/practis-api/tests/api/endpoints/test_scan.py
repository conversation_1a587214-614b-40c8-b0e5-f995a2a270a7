import pytest
from unittest import mock
import json

pytestmark = pytest.mark.django_db


def mocked_requests_get(*args, **kwargs):
    class MockResponse:
        def __init__(self, json_data, status_code):
            self.json_data = json_data
            self.status_code = status_code
            self.content = json.dumps(json_data).encode()

        def json(self):
            return self.json_data

    return MockResponse(
        [
            {"name": "vuln1", "id": "2"},
            {"name": "vuln2", "id": "3"},
        ],
        200,
    )


class Test_ScanView:

    endpoint = "/api/scan/"

    @mock.patch("requests.get", side_effect=mocked_requests_get)
    def test_list(self, mock_get, api_client):
        # Act
        response = api_client().get(self.endpoint)
        # assert
        assert response.status_code == 200
        assert len(response.data) == 2
        assert response.data[0]["name"] == "vuln1"
        assert response.data[0]["id"] == "2"
        assert response.data[1]["name"] == "vuln2"
        assert response.data[1]["id"] == "3"
