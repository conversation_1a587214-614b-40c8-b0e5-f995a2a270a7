import pytest
from unittest.mock import patch
from dotenv import load_dotenv
import json

from django.utils import timezone as tz

pytestmark = pytest.mark.django_db


def mocked_requests_post(*args, **kwargs):
    load_dotenv()

    class MockResponse:
        def __init__(self, json_data, status_code):
            self.json_data = json_data
            self.status_code = status_code
            self.content = json.dumps(json_data).encode()

        def json(self):
            return self.json_data

    return MockResponse({"id": 2}, 200)


class Test_FlowExecutionView:
    endpoint = "/api/flowexecution/"

    def run_dependencies(self, api_client):
        api_client().post(
            "/api/sut_provider/",
            {"name": "test sut_provider", "description": "description"},
            format="json",
        )
        api_client().post(
            "/api/asset/",
            {
                "name": "Asset",
                "description": "description",
                "parameters": "{'name':'name'}",
                "type": "component",
            },
            format="json",
        )

        api_client().post(
            "/api/flow/",
            {
                "name": "Flow",
                "description": "description",
                "flow": "http://flow.com",
                "asset": 1,
            },
            format="json",
        )

    @patch("requests.post", side_effect=mocked_requests_post)
    def run_sut_dependencies(self, api_client, mock_post):
        api_client().post(
            "/api/sut/",
            {
                "name": "Sut",
                "description": "description",
                "sut_provider": 1,
                "assets": [1],
            },
            format="json",
        )

    def test_list(self, flow_execution_factory, api_client):
        # Arrange

        flow_execution_factory()

        # Act

        response = api_client().get(self.endpoint)

        # Assert
        assert response.status_code == 200
        assert len(response.data) == 1

    def test_retrieve(self, flow_execution_factory, api_client):
        flow_execution_factory()
        response = api_client().get(f"{self.endpoint}1/")
        assert response.status_code == 200

    def test_create(self, api_client):
        self.run_dependencies(api_client)
        self.run_sut_dependencies(api_client)

        with open("report.txt", "w") as f:
            f.write("report")

        response = api_client().post(
            self.endpoint,
            {
                "date": str(tz.now().date()),
                "result": "pass",
                "sut": 1,
                "flow": 1,
            },
            files={"report": open("report.txt", "rb")},
        )
        assert response.status_code == 201
        assert response.data["result"] == "pass"
        assert response.data["sut"] == 1
        assert response.data["flow"] == 1

    def test_update(self, flow_execution_factory, api_client):

        self.run_dependencies(api_client)
        self.run_sut_dependencies(api_client)

        with open("report.txt", "w") as f:
            f.write("report")

        # Initial POST request
        response = api_client().post(
            self.endpoint,
            {
                "date": str(tz.now().date()),
                "result": "pass",
                "sut": 1,
                "flow": 1,
            },
            files={"report": open("report.txt", "rb")},
        )
        id = response.data["id"]
        assert response.status_code == 201

        # Update with PUT request
        with open("report.txt", "rb") as report_file:
            response = api_client().put(
                f"{self.endpoint}{id}/",
                data={
                    "date": str(tz.now().date()),
                    "result": "fail",
                    "sut": 1,
                    "flow": 1,
                    "report": report_file,
                },
                format="multipart",
            )

        assert response.status_code == 201
        assert response.data["result"] == "fail"
        assert response.data["sut"] == 1
        assert response.data["flow"] == 1

    def test_destroy(self, api_client):
        self.run_dependencies(api_client)
        self.run_sut_dependencies(api_client)

        with open("report.txt", "w") as f:
            f.write("report")

        # Initial POST request
        response = api_client().post(
            self.endpoint,
            {
                "date": str(tz.now().date()),
                "result": "pass",
                "sut": 1,
                "flow": 1,
            },
            files={"report": open("report.txt", "rb")},
        )
        id = response.data["id"]
        assert response.status_code == 201

        # deleted the created object
        response = api_client().delete(f"{self.endpoint}{id}/")
        assert response.status_code == 204
        assert response.data["message"] == "The object has been deleted"
