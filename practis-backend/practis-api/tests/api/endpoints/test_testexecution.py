import pytest
from unittest import mock
import json
import time
from django.utils import timezone as tz

pytestmark = pytest.mark.django_db


def mocked_requests_post(*args, **kwargs):
    class MockResponse:
        def __init__(self, json_data, status_code):
            self.json_data = json_data
            self.status_code = status_code
            self.content = json.dumps(json_data).encode()

        def json(self):
            return self.json_data

    return MockResponse({"id": 2}, 200)


class Test_TestExecutionView:
    endpoint = "/api/testexecution/"

    def create_test_execution_object(self, api_client):
        response = api_client().post(
            self.endpoint,
            {
                "date": tz.localtime(tz.now()),
                "result": "pass",
                "report": "http://report.com",
                "test_case": 1,
                "test_run": 1,
                "tcms_external_id": "1",
            },
            format="json",
        )

        return response

    def test_list(self, test_execution_factory, api_client):
        # Arrange

        test_execution_factory()

        # Act

        response = api_client().get(self.endpoint)

        # Assert
        assert response.status_code == 200
        assert len(response.data) == 1

    def test_retrieve(self, test_execution_factory, api_client):
        test_execution_factory()
        response = api_client().get(f"{self.endpoint}1/")
        assert response.status_code == 200
        assert response.data["status"] == "PASSED"
        assert response.data["report"] == "http://report.com"
        assert response.data["test_case"] == 1
        assert response.data["test_run"] == 1

    def test_create(self, api_client):
        response = self.create_test_execution_object(api_client)
        assert response.status_code == 201

    @mock.patch("requests.put", side_effect=mocked_requests_post)
    def test_update(self, mock_put, api_client):
        reponse = self.create_test_execution_object(api_client)
        assert reponse.status_code == 201

        response = api_client().put(
            f"{self.endpoint}1/",
            {
                "date": tz.localtime(tz.now()),
                "status": "IDLE",
                "report": "http://report.com",
                "test_case": 1,
                "test_run": 1,
                "tcms_external_id": 1,
            },
            format="json",
        )
        assert response.status_code == 200
        assert response.data["status"] == "IDLE"

    @mock.patch("requests.put", side_effect=mocked_requests_post)
    def test_update_status_sets_date_automatically(self, mock_put, api_client):
        # Create a test execution
        response = self.create_test_execution_object(api_client)
        assert response.status_code == 201

        # Get the initial date
        initial_response = api_client().get(f"{self.endpoint}1/")
        initial_date = initial_response.data["date"]

        # Wait a moment to ensure time difference
        time.sleep(0.1)

        # Update the status to a different value without providing a date
        response = api_client().put(
            f"{self.endpoint}1/",
            {
                "status": "FAILED",  # Different status from initial
                "report": "http://report.com",
                "test_case": 1,
                "test_run": 1,
                "tcms_external_id": 1,
            },
            format="json",
        )

        assert response.status_code == 200
        assert response.data["status"] == "FAILED"

        # Verify that the date was automatically updated when status changed
        updated_date = response.data["date"]
        assert updated_date is not None
        assert updated_date != initial_date  # Date should be different (newer)

    @mock.patch("requests.put", side_effect=mocked_requests_post)
    def test_update_same_status_preserves_date(self, mock_put, api_client):
        # Create a test execution with initial status
        response = self.create_test_execution_object(api_client)
        assert response.status_code == 201

        # Get the initial date and status
        initial_response = api_client().get(f"{self.endpoint}1/")
        initial_date = initial_response.data["date"]
        initial_status = initial_response.data["status"]

        # Wait a moment to ensure time difference would be detectable
        time.sleep(0.1)

        # Update with the same status
        response = api_client().put(
            f"{self.endpoint}1/",
            {
                "status": initial_status,  # Same status as before
                "report": "http://updated-report.com",
                "test_case": 1,
                "test_run": 1,
                "tcms_external_id": 1,
            },
            format="json",
        )

        assert response.status_code == 200
        assert response.data["status"] == initial_status

        # Verify that the date was preserved (not changed)
        updated_date = response.data["date"]
        assert updated_date == initial_date  # Date should remain the same

    @mock.patch("requests.put", side_effect=mocked_requests_post)
    def test_update_without_date_never_saves_null(self, mock_put, api_client):
        # Create a test execution
        response = self.create_test_execution_object(api_client)
        assert response.status_code == 201

        # Get the initial date and status
        initial_response = api_client().get(f"{self.endpoint}1/")
        initial_date = initial_response.data["date"]
        initial_status = initial_response.data["status"]

        # Update without providing a date field and with same status
        response = api_client().put(
            f"{self.endpoint}1/",
            {
                "status": initial_status,  # Same status
                "report": "http://updated-report.com",
                "test_case": 1,
                "test_run": 1,
                "tcms_external_id": 1,
                # Note: no 'date' field provided
            },
            format="json",
        )

        assert response.status_code == 200

        # Verify that date is never null - should preserve existing date
        updated_date = response.data["date"]
        assert updated_date is not None
        assert updated_date == initial_date  # Should preserve existing date

    @mock.patch("requests.put", side_effect=mocked_requests_post)
    def test_update_with_null_date_sets_current_time(self, mock_put, api_client):
        # Create a test execution
        response = self.create_test_execution_object(api_client)
        assert response.status_code == 201

        # Get the initial status
        initial_response = api_client().get(f"{self.endpoint}1/")
        initial_status = initial_response.data["status"]

        # Update with explicit null date and same status
        response = api_client().put(
            f"{self.endpoint}1/",
            {
                "status": initial_status,  # Same status
                "date": None,  # Explicitly setting null date
                "report": "http://updated-report.com",
                "test_case": 1,
                "test_run": 1,
                "tcms_external_id": 1,
            },
            format="json",
        )

        assert response.status_code == 200

        # Verify that date is never null - should be set to current time
        updated_date = response.data["date"]
        assert updated_date is not None

    @mock.patch("requests.put", side_effect=mocked_requests_post)
    def test_update_with_existing_null_date_sets_current_time(self, mock_put, api_client):
        # This test simulates updating a test execution that somehow has a null date
        # The enhanced logic should ensure it gets set to current time

        # Create a test execution
        response = self.create_test_execution_object(api_client)
        assert response.status_code == 201

        # Get the initial status
        initial_response = api_client().get(f"{self.endpoint}1/")
        initial_status = initial_response.data["status"]

        # Simulate an update where the instance might have a null date
        # (this tests the fallback logic in the serializer)
        response = api_client().put(
            f"{self.endpoint}1/",
            {
                "status": initial_status,  # Same status
                "report": "http://updated-report.com",
                "test_case": 1,
                "test_run": 1,
                "tcms_external_id": 1,
                # No date field provided, and assuming instance.date could be None
            },
            format="json",
        )

        assert response.status_code == 200

        # Verify that date is never null
        updated_date = response.data["date"]
        assert updated_date is not None

    @mock.patch("requests.delete", side_effect=mocked_requests_post)
    def test_destroy(self, mock_delete, api_client):
        response = self.create_test_execution_object(api_client)

        assert response.status_code == 201

        response = api_client().delete(f"{self.endpoint}1/")
        assert response.status_code == 204
        assert response.data["message"] == "The object has been deleted"

        response = api_client().get(f"{self.endpoint}1/")
        assert response.status_code == 404

    def test_create_with_automation_enabled_and_execution_path(self, api_client):
        """Test creating test execution with automation enabled and valid execution path"""
        response = api_client().post(
            self.endpoint,
            {
                "date": tz.localtime(tz.now()),
                "status": "IDLE",
                "report": "Test report",
                "test_case": 1,
                "test_run": 1,
                "automated": True,
                "execution_path": "/path/to/test",
                "source": "https://example.com/source"
            },
            format="json",
        )
        assert response.status_code == 201
        assert response.data["automated"] is True
        assert response.data["execution_path"] == "/path/to/test"
        assert response.data["source"] == "https://example.com/source"

    def test_create_with_automation_enabled_without_execution_path_fails(self, api_client):
        """Test creating test execution with automation enabled but no execution path fails"""
        response = api_client().post(
            self.endpoint,
            {
                "date": tz.localtime(tz.now()),
                "status": "IDLE",
                "report": "Test report",
                "test_case": 1,
                "test_run": 1,
                "automated": True,
                "execution_path": "",  # Empty execution path
                "source": "https://example.com/source"
            },
            format="json",
        )
        assert response.status_code == 400
        assert "execution_path" in response.data
        assert "required when automated is True" in str(response.data["execution_path"])

    def test_create_with_automation_enabled_with_whitespace_execution_path_fails(self, api_client):
        """Test creating test execution with automation enabled but whitespace-only execution path fails"""
        response = api_client().post(
            self.endpoint,
            {
                "date": tz.localtime(tz.now()),
                "status": "IDLE",
                "report": "Test report",
                "test_case": 1,
                "test_run": 1,
                "automated": True,
                "execution_path": "   ",  # Whitespace-only execution path
                "source": "https://example.com/source"
            },
            format="json",
        )
        assert response.status_code == 400
        assert "execution_path" in response.data
        assert "required when automated is True" in str(response.data["execution_path"])

    def test_create_with_automation_disabled_without_execution_path_succeeds(self, api_client):
        """Test creating test execution with automation disabled and no execution path succeeds"""
        response = api_client().post(
            self.endpoint,
            {
                "date": tz.localtime(tz.now()),
                "status": "IDLE",
                "report": "Test report",
                "test_case": 1,
                "test_run": 1,
                "automated": False,
                "execution_path": "",  # Empty execution path is OK when automated=False
                "source": "https://example.com/source"
            },
            format="json",
        )
        assert response.status_code == 201
        assert response.data["automated"] is False
        assert response.data["execution_path"] == ""

    @mock.patch("requests.put", side_effect=mocked_requests_post)
    def test_update_enable_automation_with_execution_path_succeeds(self, _mock_put, api_client):
        """Test updating test execution to enable automation with valid execution path succeeds"""
        # Create initial test execution
        response = self.create_test_execution_object(api_client)
        assert response.status_code == 201

        # Update to enable automation with execution path
        response = api_client().put(
            f"{self.endpoint}1/",
            {
                "status": "IDLE",
                "report": "Test report",
                "test_case": 1,
                "test_run": 1,
                "automated": True,
                "execution_path": "/path/to/automated/test",
                "source": "https://example.com/source"
            },
            format="json",
        )
        assert response.status_code == 200
        assert response.data["automated"] is True
        assert response.data["execution_path"] == "/path/to/automated/test"

    @mock.patch("requests.put", side_effect=mocked_requests_post)
    def test_update_enable_automation_without_execution_path_fails(self, _mock_put, api_client):
        """Test updating test execution to enable automation without execution path fails"""
        # Create initial test execution
        response = self.create_test_execution_object(api_client)
        assert response.status_code == 201

        # Try to update to enable automation without execution path
        response = api_client().put(
            f"{self.endpoint}1/",
            {
                "status": "IDLE",
                "report": "Test report",
                "test_case": 1,
                "test_run": 1,
                "automated": True,
                "execution_path": "",  # Empty execution path
                "source": "https://example.com/source"
            },
            format="json",
        )
        assert response.status_code == 400
        assert "execution_path" in response.data
        assert "required when automated is True" in str(response.data["execution_path"])

    @mock.patch("requests.put", side_effect=mocked_requests_post)
    def test_update_disable_automation_with_empty_path_succeeds(self, _mock_put, api_client):
        """Test updating test execution to disable automation with empty execution path succeeds"""
        # Create initial test execution
        response = self.create_test_execution_object(api_client)
        assert response.status_code == 201

        # Update to disable automation (execution path can be empty)
        response = api_client().put(
            f"{self.endpoint}1/",
            {
                "status": "IDLE",
                "report": "Test report",
                "test_case": 1,
                "test_run": 1,
                "automated": False,
                "execution_path": "",  # Empty execution path is OK when automated=False
                "source": "https://example.com/source"
            },
            format="json",
        )
        assert response.status_code == 200
        assert response.data["automated"] is False
        assert response.data["execution_path"] == ""
