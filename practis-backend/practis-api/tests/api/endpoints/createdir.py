import os

modelsList = [
    "SutProvider",
    "ObjectivesAndScope",
    "Asset",
    "Sut",
    "Version",
    "Flow",
    "FlowExecution",
    "TestRun",
    "AttackPathvector",
    "RiskAnalysis",
    "Risk",
    "Vulnerability",
    "TestCase",
    "TestExecution",
]

for model in modelsList:
    with open(f"test_{model.lower()}.py", "w") as fout:
        fout.write("import pytest\n\npytestmark = pytest.mark.django_db")
