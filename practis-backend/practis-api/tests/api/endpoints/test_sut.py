import json
from unittest import mock
import pytest


pytestmark = pytest.mark.django_db


def mocked_requests_post(*args, **kwargs):

    class MockResponse:
        def __init__(self, json_data, status_code):
            self.json_data = json_data
            self.status_code = status_code
            self.content = json.dumps(json_data).encode()

        def json(self):
            return self.json_data

    return MockResponse({"id": 2}, 200)


class Test_SutView:

    endpoint = "/api/sut/"

    # This method will be used by the mock to replace requests.get

    def run_dependencies(self, api_client):
        api_client().post(
            "/api/sut_provider/",
            {"name": "testSutProvider", "description": "description"},
            format="json",
        )
        api_client().post(
            "/api/asset/",
            data={
                "name": "Asset",
                "description": "description",
                "confidentiality": True,
                "integrity": True,
                "availability": True,
                "parameters": {"name": "name"},
                "type": "component",
            },
            format="json",
        )

    def test_list(self, sut_factory, api_client):
        # Arrange

        self.run_dependencies(api_client)
        sut_factory()
        # Act

        response = api_client().get(self.endpoint)

        # Assert
        assert response.status_code == 200
        assert len(response.data) == 1

    def test_retrieve(self, sut_factory, api_client):
        self.run_dependencies(api_client)
        sut_factory()
        response = api_client().get(f"{self.endpoint}1/")
        assert response.status_code == 200
        assert response.data["name"] == "Sut"
        assert response.data["description"] == "description"
        assert response.data["tcms_external_id"] == "1"
        assert response.data["sut_provider"] == 2
        assert len(response.data["assets"]) >= 0

    @mock.patch("requests.post", side_effect=mocked_requests_post)
    def test_create(self, sut_factory, api_client):
        self.run_dependencies(api_client)
        sut_factory()

        response = api_client().post(
            self.endpoint,
            {
                "name": "Sut",
                "description": "description",
                "sut_provider": 1,
                "assets": [1],
            },
            format="json",
        )

        # Check if the response is as expected
        assert response.status_code == 201
        assert response.data["name"] == "Sut"
        assert response.data["description"] == "description"
        assert response.data["sut_provider"] == 1
        assert response.data["assets"][0] == 1
        assert response.data["tcms_external_id"] == "2"

    def test_update(self, sut_factory, api_client):
        self.run_dependencies(api_client)
        sut_factory()

        # api_client().post(
        #     self.endpoint,
        #     {"name": "Sut", "description": "description", "sut_provider": 1, "assets": [1]},
        #     format="json",
        # )

        response = api_client().put(
            f"{self.endpoint}1/",
            {
                "name": "Sut",
                "description": "description with update",
                "sut_provider": 1,
                "assets": [1],
            },
            format="json",
        )
        assert response.status_code == 200
        assert response.data["name"] == "Sut"
        assert response.data["description"] == "description with update"
        assert response.data["sut_provider"] == 1

    def test_destroy(self, sut_factory, api_client):
        self.run_dependencies(api_client)
        sut_factory()

        response = api_client().delete(f"{self.endpoint}1/")
        assert response.status_code == 204
        assert response.data["message"] == "The object has been deleted"
