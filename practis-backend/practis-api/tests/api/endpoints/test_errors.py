import pytest
from django.test import TestCase
from rest_framework import status

pytestmark = pytest.mark.django_db


class TestError404View:
    endpoint = "/api/nonexistent/"

    def test_error404(self, api_client):
        response = api_client().get(self.endpoint)
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.data == {"message": "The requested resource can not be found"}


class TestError500View:
    endpoint = "/api/test500/"

    def test_error500(self, api_client):
        response = api_client().get(self.endpoint)
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert response.data == {"message": "Unable to reach the server"}
