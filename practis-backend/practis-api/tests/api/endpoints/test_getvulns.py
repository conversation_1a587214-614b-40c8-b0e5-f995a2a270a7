import pytest
from unittest import mock
import json

pytestmark = pytest.mark.django_db


def mocked_response_get(*args, **kwargs):
    class MockResponse:
        def __init__(self, json_data, status_code):
            self.json_data = json_data
            self.status_code = status_code
            self.content = json.dumps(json_data).encode()

        def json(self):
            return self.json_data

    return MockResponse(
        [
            {
                "uuid": "uuid",
                "name": "test Vulnerability",
                "description": "description",
                "cve_cwe": "cve_cwe",
                "status": "new",
                "attack_path_or_vector": "attack path vector",
            }
        ],
        200,
    )


def mocked_response_get_error(*args, **kwargs):
    class MockResponse:
        def __init__(self, json_data, status_code):
            self.json_data = json_data
            self.status_code = status_code
            self.content = json.dumps(json_data).encode()

        def json(self):
            return self.json_data

    return MockResponse({"error": "An error occured when reaching the server"}, 500)


class Test_GetVulnsView:

    endpoint = "/api/retrievevuln/"

    def run_dependencies(self, api_client):

        api_client().post(
            "/api/asset/",
            {
                "name": "Asset",
                "description": "description",
                "parameters": {"name": "name"},
                "type": "component",
            },
            format="json",
        )

    @mock.patch("requests.get", side_effect=mocked_response_get)
    def test_create(self, mock_get, api_client):
        self.run_dependencies(api_client)
        # Act
        response = api_client().post(self.endpoint, {"report_id": 1, "asset_id": 1})
        # assert
        assert response.status_code == 200
        assert response.data[0]["name"] == "test Vulnerability"

    @mock.patch("requests.get", side_effect=mocked_response_get_error)
    def test_create_error(self, mock_get, api_client):
        self.run_dependencies(api_client)
        # Act
        response = api_client().post(self.endpoint, {"report_id": 1, "asset_id": 1})
        # assert
        assert response.status_code == 500
        assert response.data["error"] == "An error occured when reaching the server"
