import pytest
from unittest import mock
import json
from django.utils import timezone as tz

pytestmark = pytest.mark.django_db


def mocked_requests_post(*args, **kwargs):

    class MockResponse:
        def __init__(self, json_data, status_code):
            self.json_data = json_data
            self.status_code = status_code
            self.content = json.dumps(json_data).encode()

        def json(self):
            return self.json_data

    return MockResponse({"id": "2"}, 200)


def mocked_requests_get(*args, **kwargs):

    class MockResponse:
        def __init__(self, json_data, status_code):
            self.json_data = json_data
            self.status_code = status_code
            self.content = json.dumps(json_data).encode()

        def json(self):
            return self.json_data

    return MockResponse([{"name": "test_plan", "id": 2}], 200)


class TestTestRunView:
    endpoint = "/api/testrun/"

    def run_dependencies(self, api_client):
        api_client().post(
            "/api/sut_provider/",
            {"name": "testSutProvider", "description": "description"},
            format="json",
        )
        api_client().post(
            "/api/asset/",
            {
                "name": "Asset",
                "description": "description",
                "parameters": {"name": "name"},
                "type": "component",
            },
            format="json",
        )

    @mock.patch("requests.post", side_effect=mocked_requests_post)
    def run_sut_dependencies(self, api_client, mock_post):
        api_client().post(
            "/api/sut/",
            {
                "name": "Sut",
                "description": "description",
                "sut_provider": 1,
                "assets": [1],
            },
            format="json",
        )

    @mock.patch("requests.get", side_effect=mocked_requests_get)
    def run_testplan_dependencies(self, api_client, mock_post):
        response = api_client().get(
            "/api/testplan/",
        )

    @mock.patch("requests.post", side_effect=mocked_requests_post)
    def create_testrun_instance(self, api_client, mock_post):
        response = api_client().post(
            self.endpoint,
            {
                "name": "Test Run",
                "description": "description",
                "start_date": tz.now().date(),
                "end_date": tz.now().date(),
                "sut": 1,
                "plan": 1,
            },
        )

    def test_list(self, test_run_factory, api_client):
        # Arrange

        test_run_factory()

        # Act

        response = api_client().get(self.endpoint)

        # Assert
        assert response.status_code == 200
        assert len(response.data) == 1

    def test_retrieve(self, test_run_factory, api_client):
        test_run_factory()
        response = api_client().get(f"{self.endpoint}1/")
        assert response.status_code == 200

    @mock.patch("requests.post", side_effect=mocked_requests_post)
    def test_create(self, test_run_factory, api_client):
        self.run_dependencies(api_client)
        self.run_sut_dependencies(api_client)
        self.run_testplan_dependencies(api_client)

        response = api_client().post(
            self.endpoint,
            {
                "name": "Test Run",
                "description": "description",
                "start_date": tz.now().date(),
                "end_date": tz.now().date(),
                "sut": 1,
                "plan": 1,
            },
            format="json",
        )
        assert response.status_code == 201
