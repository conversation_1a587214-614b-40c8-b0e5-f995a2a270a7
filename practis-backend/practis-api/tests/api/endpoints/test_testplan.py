import pytest
from unittest import mock
import json

pytestmark = pytest.mark.django_db


def mocked_requests_get(*args, **kwargs):

    class MockResponse:
        def __init__(self, json_data, status_code):
            self.json_data = json_data
            self.status_code = status_code
            self.content = json.dumps(json_data).encode()

        def json(self):
            return self.json_data

    return MockResponse(
        [
            {"name": "test_plan", "id": "2"},
            {"name": "test_plan_2", "id": "3"},
        ],
        200,
    )


class Test_TestPlanView:

    endpoint = "/api/testplan/"

    @mock.patch("requests.get", side_effect=mocked_requests_get)
    def test_list(self, test_plan_factory, api_client):
        # Arrange
        test_plan_factory()
        # Act
        response = api_client().get(self.endpoint)
        # assert
        assert response.status_code == 200
        assert len(response.data) == 2
        assert response.data[0]["name"] == "test_plan"
        assert response.data[0]["id"] == "2"
        assert response.data[1]["name"] == "test_plan_2"
        assert response.data[1]["id"] == "3"
