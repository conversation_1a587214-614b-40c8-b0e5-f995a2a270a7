import pytest

pytestmark = pytest.mark.django_db


class Test_ObjectivesAndScopeView:
    endpoint = "/api/objectivesandscope/"

    def test_list(self, objectives_and_scope_factory, api_client):
        # Arrange

        objectives_and_scope_factory()

        # Act

        response = api_client().get(self.endpoint)

        # Assert
        assert response.status_code == 200
        assert len(response.data) == 1
        assert response.data[0]["objectives"] == "evaluating security"
        assert response.data[0]["risk_level_objective"] == "risk_level_objective"
        assert response.data[0]["test_coverage_objective"] == "test_coverage_objective"
        assert response.data[0]["assurance_level"] == "assurance_level"
        assert response.data[0]["description"] == "description"
        assert response.data[0]["sut_provider"] == 1

    def test_retrieve(self, objectives_and_scope_factory, api_client):
        objectives_and_scope_factory()
        response = api_client().get(f"{self.endpoint}1/")
        assert response.status_code == 200

    def test_create(self, api_client):
        api_client().post(
            "/api/sut_provider/",
            {"name": "testSutProvider", "description": "description"},
            format="json",
        )
        response = api_client().post(
            self.endpoint,
            {
                "objectives": "evaluating security",
                "risk_level_objective": "risk_level_objective",
                "test_coverage_objective": "test_coverage_objective",
                "assurance_level": "assurance_level",
                "description": "description",
                "sut_provider": 1,
            },
            format="json",
        )
        assert response.status_code == 201
        assert response.data["objectives"] == "evaluating security"
        assert response.data["risk_level_objective"] == "risk_level_objective"
        assert response.data["test_coverage_objective"] == "test_coverage_objective"
        assert response.data["assurance_level"] == "assurance_level"
        assert response.data["description"] == "description"
        assert response.data["sut_provider"] == 1

    def test_update(self, api_client):
        api_client().post(
            "/api/sut_provider/",
            {"name": "testSutProvider", "description": "description"},
            format="json",
        )
        api_client().post(
            self.endpoint,
            {
                "objectives": "evaluating security",
                "risk_level_objective": "risk_level_objective",
                "test_coverage_objective": "test_coverage_objective",
                "assurance_level": "assurance_level",
                "description": "description",
                "sut_provider": 1,
            },
            format="json",
        )
        response = api_client().put(
            f"{self.endpoint}1/",
            {
                "objectives": "evaluating security",
                "risk_level_objective": "risk_level_objective",
                "test_coverage_objective": "test_coverage_objective",
                "assurance_level": "assurance_level",
                "description": "description with update",
                "sut_provider": 1,
            },
            format="json",
        )
        assert response.status_code == 200
        assert response.data["objectives"] == "evaluating security"
        assert response.data["risk_level_objective"] == "risk_level_objective"
        assert response.data["test_coverage_objective"] == "test_coverage_objective"
        assert response.data["assurance_level"] == "assurance_level"
        assert response.data["description"] == "description with update"
        assert response.data["sut_provider"] == 1

    def test_destroy(self, api_client):
        api_client().post(
            "/api/sut_provider/",
            {"name": "testSutProvider", "description": "description"},
            format="json",
        )
        api_client().post(
            self.endpoint,
            {
                "objectives": "evaluating security",
                "risk_level_objective": "risk_level_objective",
                "test_coverage_objective": "test_coverage_objective",
                "assurance_level": "assurance_level",
                "description": "description",
                "sut_provider": 1,
            },
            format="json",
        )
        response = api_client().delete(f"{self.endpoint}1/")
        assert response.status_code == 204
        assert response.data["message"] == "The object has been deleted"
