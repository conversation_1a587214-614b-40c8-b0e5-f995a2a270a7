import pytest
from datetime import datetime
from django.utils import timezone as tz

pytestmark = pytest.mark.django_db


class TestSutProviderModel:
    def test_attributes(self, sut_provider_factory):
        sut_provider = sut_provider_factory()

        assert sut_provider.name == "SutProvider"
        assert sut_provider.description == "description"

    def test_str_method(self, sut_provider_factory):
        sut_provider = sut_provider_factory()

        assert sut_provider.name == sut_provider.__str__() == "SutProvider"


class TestObjectivesAndScopeModel:
    def test_attributes(self, objectives_and_scope_factory):
        objectivesandscope = objectives_and_scope_factory()

        assert objectivesandscope.objectives == "evaluating security"
        assert objectivesandscope.risk_level_objective == "risk_level_objective"
        assert objectivesandscope.test_coverage_objective == "test_coverage_objective"
        assert objectivesandscope.assurance_level == "assurance_level"
        assert objectivesandscope.description == "description"


class TestAssetModel:
    def test_attributes(self, asset_factory):
        asset = asset_factory()

        assert asset.name == "Asset"
        assert asset.description == "description"
        assert asset.parameters == {"name": "name"}
        assert asset.type == "component"
        assert asset.confidentiality == True
        assert asset.integrity == True
        assert asset.availability == True
        assert asset.parameters == {"name": "name"}

    def test_str_method(self, asset_factory):
        asset = asset_factory()
        assert asset.name == asset.__str__() == "Asset"


class TestSutModel:
    def test_attributes(self, sut_factory):
        sut = sut_factory()

        assert sut.name == "Sut"
        assert sut.description == "description"
        assert sut.tcms_external_id == "1"
        assert sut.sut_provider.name == "SutProvider"
        assert len(sut.assets.all()) >= 0

    def test_str_method(self, sut_factory):
        sut = sut_factory()

        assert sut.name == sut.__str__() == "Sut"


class TestVersionModel:
    def test_attributes(self, version_factory):
        version = version_factory()
        assert version.version == "version"
        assert version.status == "in development"
        assert version.sut.name == "Sut"


class TestFlowModel:
    def test_attributes(self, node_red_flow_factory):
        flow = node_red_flow_factory()
        assert flow.name == "Flow"
        assert flow.description == "description"
        assert flow.address == "http://address.com"
        assert flow.api_url == "http://api_url.com"
        assert flow.parameter == "parameter"
        assert flow.asset.name == "Asset"

    def test_str_method(self, node_red_flow_factory):
        flow = node_red_flow_factory()
        assert flow.name == flow.__str__() == "Flow"


class TestFlowExecutionModel:
    def test_attributes(self, flow_execution_factory):
        flowexecution = flow_execution_factory()
        assert flowexecution.date.day == datetime.now().day
        assert flowexecution.result == "pass"
        assert flowexecution.report == "/media/report.pdf"
        assert flowexecution.sut.name == "Sut"
        assert flowexecution.flow.name == "Flow"


class TestTestPlanModel:
    def test_attributes(self, test_plan_factory):
        testplan = test_plan_factory()
        assert testplan.name == "Test Plan"
        assert testplan.tcms_external_id == "1"

    def test_str_method(self, test_plan_factory):
        testplan = test_plan_factory()
        assert testplan.name == testplan.__str__() == "Test Plan"


class TestTestRunModel:
    def test_attributes(self, test_run_factory):
        test_run = test_run_factory()
        assert test_run.name == "Test Run"
        assert test_run.description == "description"
        assert test_run.start_date.day == datetime.now().day
        assert test_run.end_date.day == datetime.now().day
        assert test_run.sut.name == "Sut"
        assert test_run.tcms_external_id == "1"
        assert test_run.plan.name == "Test Plan"

    def test_str_method(self, test_run_factory):
        test_run = test_run_factory()
        assert test_run.name == test_run.__str__() == "Test Run"


class TestRiskAnalysisModel:
    def test_attributes(self, risk_analysis_factory):
        riskanalysis = risk_analysis_factory()
        assert riskanalysis.name == "Risk Analysis"
        assert riskanalysis.docs == "http://docs.com/"
        assert riskanalysis.sut_version.version == "version"


class TestRiskModel:
    def test_attributes(self, risk_factory):
        risk = risk_factory()
        assert risk.name == "Risk"
        assert risk.description == "description"
        assert risk.value == 0
        assert risk.level == "level"
        assert risk.status == "new"
        assert risk.risk_analysis.name == "Risk Analysis"

    def test_str_method(self, risk_factory):
        risk = risk_factory()
        assert risk.name == risk.__str__() == "Risk"


class TestVulnerabilityModel:
    def test_attributes(self, vulnerability_factory):
        vulnerability = vulnerability_factory()
        assert vulnerability.name == "Vulnerability"
        assert vulnerability.description == "description"
        assert vulnerability.cve_cwe == "cve_cwe"
        assert vulnerability.attack_path_or_vector == "attack_path_or_vector"
        assert len(vulnerability.risks.all()) >= 0
        assert vulnerability.uuid == "uuid"
        assert vulnerability.asset.name == "Asset"
        assert vulnerability.status == "new"

    def test_str_method(self, vulnerability_factory):
        vulnerability = vulnerability_factory()
        assert vulnerability.name == vulnerability.__str__() == "Vulnerability"


class TestTestCaseModel:
    def test_attributes(self, test_case_factory):
        test_case = test_case_factory()
        assert test_case.name == "Test Case"
        assert test_case.description == "description"
        assert test_case.attack_technique == "attack_technique"
        assert test_case.vulnerability.name == "Vulnerability"
        assert test_case.status == "PROPOSED"
        assert test_case.priority == 1
        assert test_case.category == "--default--"
        assert test_case.tcms_external_id == "1"

    def test_str_method(self, test_case_factory):
        test_case = test_case_factory()
        assert test_case.name == test_case.__str__() == "Test Case"


class TestTestExecutionModel:
    def test_attributes(self, test_execution_factory):
        test_execution = test_execution_factory()
        assert test_execution.date.day == datetime.now().day
        assert test_execution.report == "http://report.com"
        assert test_execution.status == "PASSED"
        assert test_execution.tcms_external_id == "1"
        assert test_execution.test_case == 1
        assert test_execution.test_run == 1


class TestRetrieveVulnsModel:
    def test_attributes(self, retrieve_vulns_factory):
        retrievevulns = retrieve_vulns_factory()
        assert retrievevulns.report == "report"

    def test_str_method(self, retrieve_vulns_factory):
        retrievevulns = retrieve_vulns_factory()
        assert retrievevulns.report == retrievevulns.__str__() == "report"


class TestRetrieveTestExecutionModel:
    def test_attributes(self, retrieve_test_execution_factory):
        retrievetestexecution = retrieve_test_execution_factory()
        assert retrievetestexecution.sut_id.name == "Sut"

class TestComponentModel:
    def test_attributes(self, component_factory):
        component = component_factory()
        assert component.name == "Component"
        assert component.description == "description"
        assert component.version.name == "version"

class TestSubComponentModel:
    def test_attributes(self, sub_component_factory):
        subcomponent = sub_component_factory()
        assert subcomponent.name == "SubComponent"
        assert subcomponent.description == "description"
        assert subcomponent.component.name == "Component"
class TestPortModel:
    def test_attributes(self, port_factory):
        port = port_factory()
        assert port.name == "Port"
        assert port.description == "description"
        assert port.component.name == "Component"

class TestInterfaceModel:
    def test_attributes(self, interface_factory):
        interface = interface_factory()
        assert interface.name == "Interface"
        assert interface.type == "external"
        assert interface.port_from.name == "Port"
        assert interface.port_to_port.name == "Port"
        assert interface.port_to_subcomponent.name == "SubComponent"

