import factory
from api.models import *
from diagram.models import *
from django.utils import timezone as tz


class SutProviderFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = SutProvider

    name = "SutProvider"
    description = "description"
class ObjectivesAndScopeFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ObjectivesAndScope

    objectives = "evaluating security"
    risk_level_objective = "risk_level_objective"
    test_coverage_objective = "test_coverage_objective"
    assurance_level = "assurance_level"
    description = "description"
    sut_provider = factory.SubFactory(SutProviderFactory)

"""
class AssetFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Asset

    name = "Asset"
    description = "description"
    confidentiality = True
    integrity = True
    availability = True
    parameters = {"name": "name"}
    type = "component"

"""
class SutFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Sut

    name = "Sut"
    description = "description"
    sut_provider = factory.SubFactory(SutProviderFactory)
    #assets = factory.RelatedFactory(AssetFactory, "sut")
    #tcms_external_id = "1"


#class SutWithAssetFactory(AssetFactory):
#    asset = factory.RelatedFactory(SutFactory, "asset")


class VersionFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Version

    name = "version"
    description = "description"
    sut = factory.SubFactory(SutFactory)


class FlowFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Flow

    name = "Flow"
    description = "description"
    address = "http://address.com"
    api_url = "http://api_url.com"
    parameter = "parameter"
 #   asset = factory.SubFactory(AssetFactory)


class FlowExecutionFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = FlowExecution

    date = tz.now().date()
    result = "pass"
    report = "/media/report.pdf"
    sut = factory.SubFactory(SutFactory)
    flow = factory.SubFactory(FlowFactory)


class TestPlanFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = TestPlan

    name = "Test Plan"
    tcms_external_id = "1"


class TestRunFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = TestRun

    name = "Test Run"
    description = "description"
    start_date = tz.now().date()
    end_date = tz.now().date()
    sut = factory.SubFactory(SutFactory)
    tcms_external_id = "1"
    plan = factory.SubFactory(TestPlanFactory)


class RiskAnalysisFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = RiskAnalysis

    name = "Risk Analysis"
    docs = "http://docs.com/"
    sut_version = factory.SubFactory(VersionFactory)


class RiskFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Risk

    name = "Risk"
    description = "description"
    value = 0
    level = "level"
    status = "new"
    risk_analysis = factory.SubFactory(RiskAnalysisFactory)


class VulnerabilityFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Vulnerability

    uuid = "uuid"
    name = "Vulnerability"
    description = "description"
    cve_cwe = "cve_cwe"
    risks = factory.RelatedFactory(RiskFactory)
    attack_path_or_vector = "attack_path_or_vector"
   # asset = factory.SubFactory(AssetFactory)
    status = "new"


class RiskWithVulnerabilityFactory(RiskFactory):
    risks = factory.RelatedFactory(VulnerabilityFactory)


class TestCaseFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = TestCase

    name = "Test Case"
    description = "description"
    attack_technique = "attack_technique"
    vulnerability = factory.SubFactory(VulnerabilityFactory)
    status = "PROPOSED"
    priority = 1
    category = "--default--"
    tcms_external_id = "1"


class TestExecutionFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = TestExecution

    date = tz.now().date()
    status = "PASSED"
    report = "http://report.com"
    tcms_external_id = "1"
    test_case = 1
    test_run = 1


class RetrieveVulnsFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = RetrieveVulns

    report = "report"


class RetrieveTestExecutionFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = RetrieveTestExecution

    sut_id = factory.SubFactory(SutFactory)


class ParameterTypeFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ParameterType

    name = "ParameterType"
    description = "description"
    generic = False

class parametersFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Parameter	

    name = "parameters"
    secret = False
    value = "value"
    parametertype = factory.SubFactory(ParameterTypeFactory)


class ComponentFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Component

    name = "Component"
    description = "description"
    version = factory.SubFactory(VersionFactory)

class SubComponentFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = SubComponent

    name = "SubComponent"
    description = "description"
    version = factory.SubFactory(VersionFactory)
    component = factory.SubFactory(ComponentFactory)

class PortFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Port

    name = "Port"
    description = "description"
    version = factory.SubFactory(VersionFactory)
    component = factory.SubFactory(ComponentFactory)

class InterfaceFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Interface

    name = "Interface"
    description = "description"
    type = "external"
    version = factory.SubFactory(VersionFactory)
    port_from = factory.SubFactory(PortFactory)
    port_to_port = factory.SubFactory(PortFactory)
    port_to_subcomponent = factory.SubFactory(SubComponentFactory)

