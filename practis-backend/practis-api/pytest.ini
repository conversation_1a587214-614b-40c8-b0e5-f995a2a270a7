[pytest]

DJANGO_SETTINGS_MODULE=practis.settings.test_settings
python_files=diagramtest_*.py
filterwarnings =
    ignore::Warning
addopts = 
    --ignore=*/migrations/*
    --ignore=*/tests/*
    --ignore=*/tests.py
    --ignore=*/urls.py
    --ignore=*/admin.py
    --ignore=*/apps.py
    --ignore=*/__pycache__/*
    --ignore=*/__init__.py
    --ignore=*/settings/*
    --ignore=*/production_files/*
    --ignore=*/wsgi.py
    --ignore=*/asgi.py
    --ignore=*/manage.py
    --ignore=*/practis/*
    --cov=.
    --cov



[report]
exclude_lines =
    pragma: no cover
    if __name__ == '__main__':
    def __repr__
