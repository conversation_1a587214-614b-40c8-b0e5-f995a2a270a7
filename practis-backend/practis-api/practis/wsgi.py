"""
WSGI config for practis project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/howto/deployment/wsgi/
"""

import os

from django.core.wsgi import get_wsgi_application

os.environ.setdefault(
    "DJANGO_SETTINGS_MODULE",
    "practis.settings.settings",
    # f"practis.settings.{['prod','dev'][int(os.environ['DEBUG'])]}_settings",
)

application = get_wsgi_application()
