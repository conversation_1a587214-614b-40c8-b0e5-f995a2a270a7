"""
URL configuration for practis project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from django.conf.urls import handler400, handler403, handler404, handler500
from django.conf import settings
from django.conf.urls.static import static
from rest_framework.permissions import AllowAny

from api.views import *
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from diagram.urls import router, urlpatterns as diagram_urls


schema_view = get_schema_view(
    openapi.Info(
        title="PRACTIS API",
        default_version="v1",
        description="This API is used to manage a centralized product that help you to handle the vulnerabilities, risks and penetration testig of a Cyber Physical System.",
        terms_of_service="https://www.google.com/policies/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    public=True,
    permission_classes=[AllowAny],
)

router.register(r"image-file", ImageFileView)
router.register(r"sut-provider", SutProviderView)
router.register(r"objectivesandscope", ObjectivesAndScopeView)
router.register(r"sut", SutView)
router.register(r"version", VersionView)
router.register(r"flow", FlowView)
router.register(r"flowexecution", FlowExecutionView)
# router.register(r"flowreport", FlowReportView)
router.register(r"riskanalysis", RiskAnalysisView)
router.register(r"risk", RiskView)
router.register(r"vulnerability", VulnerabilityView)
router.register(r"requirement", RequirementView)
router.register(r"testcase", TestCaseView)
router.register(r"testplan", TestPlanView)
router.register(r"testrun", TestRunView)
router.register(r"testexecution", TestExecutionView)
# router.register(r"testruncase", TestRunCaseView)
router.register(r"retrievevuln", RetrieveVulnsView)
router.register(r"menu", MenuView, basename="menu")
router.register(r"element", ElementView, basename="element")
router.register(r"home", HomeView, basename="home")
router.register(r"dashboard", DashboardView, basename="dashboard")
router.register(r"note", NoteView, basename="note")

urlpatterns = [
    path("admin/", admin.site.urls),
    path(
        "api/docs/",
        schema_view.with_ui("swagger", cache_timeout=0),
        name="schema-swagger-ui",
    ),
    path(
        "api/redoc/",
        schema_view.with_ui("redoc", cache_timeout=0),
        name="schema-redoc",
    ),
    path(
        "api/flowexecution/<uuid:pk>/report/",
        FlowReportView.as_view({"get": "retrieve"}),
        name="api_report",
    ),
    path(
        "flowexecution/<uuid:pk>/report/",
        FlowReportView.as_view({"get": "retrieve"}),
        name="report",
    ),
    # path("api/fbv/", test_fbv, name="fbv"),
    path("api/not-found/", NotFoundView.as_view(), name="not-found"),
    # path("api/", include(router.urls)),
    path("api/", include(router.urls)),
    path("", include(router.urls)),
    path("api/scan/", ScanView.as_view({"get": "list"}), name="scan"),
    path("api/robots.txt/", robots_txt, name="robots.txt"),
]
urlpatterns += diagram_urls

urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)


handler404 = error_404
handler401 = error_401
handler500 = error_500
