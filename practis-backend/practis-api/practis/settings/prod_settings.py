from pathlib import Path
import os
from dotenv import load_dotenv

# Build paths inside the project like this: BASE_DIR / 'subdir'.


load_dotenv()

BASE_DIR = Path(__file__).resolve().parent.parent


# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = open(f"{os.environ['PROD_SECRET_KEY_FILE']}").readline().rstrip("\n")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ["DEBUG"]

ALLOWED_HOSTS = ["*"]

SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.sites",
    "django_extensions",
    "rest_framework",
    "api",
    "diagram",
    "drf_yasg",
    "corsheaders",
    "django_apscheduler",
    # "csp",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
]

CORS_ALLOWED_ORIGINS = [
    "https://************:4200",
    "http://localhost:4200",
    "https://localhost:4200",
    "https://127.0.0.1:4200",
    "http://127.0.0.1:4200",
]

CSRF_TRUSTED_ORIGINS = [
    "https://************:4200",
    "http://localhost:4200",
    "https://localhost:4200",
    "https://127.0.0.1:4200",
    "http://127.0.0.1:4200",
]

OPENVAS_BROKER = open(f"{os.environ['OPENVAS_BROKER_FILE']}").readline().rstrip("\n")
CSP_DEFAULT_SRC = ["'self'"]
CSP_SCRIPT_SRC = ["'self'", "https://trusted-cdn.com"]
CSP_STYLE_SRC = ["'self'", "https://trusted-cdn.com"]
CSP_IMG_SRC = ["'self'", "https://trusted-cdn.com"]
CSP_OBJECT_SRC = ["'none'"]
CSP_FRAME_ANCESTORS = ["'none'"]
ATTACHMENTS = "uploads/attachments/"
KEYCLOAK_SERVER_URL = open(f"{os.environ['SERVER_URL_FILE']}").readline().rstrip("\n")
KEYCLOAK_REALM_NAME = open(f"{os.environ['REALM_FILE']}").readline().rstrip("\n")
KEYCLOAK_CLIENT_ID = open(f"{os.environ['CLIENT_ID_FILE']}").readline().rstrip("\n")
KEYCLOAK_CLIENT_SECRET = (
    open(f"{os.environ['CLIENT_SECRET_FILE']}").readline().rstrip("\n")
)
MEDIA_ROOT = os.path.join(BASE_DIR, "media")
MEDIA_URL = "/media/"

# MEDIA_ROOT = "/var/lib/practis-data"
# MEDIA_URL = "/"

# FILES_UPLOAD_HANDLERS = [
#     "django.core.files.uploadhandler.MemoryFileUploadHandler",
#     "django.core.files.uploadhandler.TemporaryFileUploadHandler",
# ]


ROOT_URLCONF = "practis.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "practis.wsgi.application"


# Database

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": open(f"{os.environ['PROD_DB_NAME_FILE']}").readline().rstrip("\n"),
        "USER": open(f"{os.environ['DB_USERNAME_FILE']}").readline().rstrip("\n"),
        "PASSWORD": open(f"{os.environ['DB_PASS_FILE']}").readline().rstrip("\n"),
        "HOST": os.environ["DB_HOST"],
        "PORT": os.environ["DB_PORT"],
    }
}

# Password validation

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# STATIC_ROOT = BASE_DIR / "production_files"
STATIC_URL = "static/"
STATICFILES_DIRS = [BASE_DIR / "production_files"]

# Default primary key field type

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

REST_FRAMEWORK = {
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
    # "DEFAULT_AUTHENTICATION_CLASSES": ["api.auth.auth.KeycloakAuthentication"],
    # "DEFAULT_PERMISSION_CLASSES": [
    #     "rest_framework.permissions.IsAuthenticated",
    # ],
}

SPECTACULAR_SETTINGS = {
    "TITLE": "CYRUS API",
}
SWAGGER_SETTINGS = {
    "DEFAULT_FIELD_INSPECTORS": [
        "drf_yasg.inspectors.CamelCaseJSONFilter",
        "drf_yasg.inspectors.InlineSerializerInspector",
        "drf_yasg.inspectors.RelatedFieldInspector",
        "drf_yasg.inspectors.ChoiceFieldInspector",
        "drf_yasg.inspectors.FileFieldInspector",
        "drf_yasg.inspectors.DictFieldInspector",
        "drf_yasg.inspectors.SimpleFieldInspector",
        "drf_yasg.inspectors.StringDefaultFieldInspector",
    ],
    "SECURITY_DEFINITIONS": {
        "Token": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header",
            "description": "Token-based authentication. Format: <your-token>",
        }
    },
    "USE_SESSION_AUTH": False,  # Disable session-based authentication if not needed
    "LOGIN_URL": None,  # Remove the login URL for Swagger UI
    "LOGOUT_URL": None,
}


LOGGING = {
    "version": 1,
    "disable_existing_loggers": True,
    "formatters": {
        "verbose": {
            "format": "{levelname} | {asctime} | {module} | {message}",
            "style": "{",
        },
        "simple": {
            "format": "{levelname} | {message}",
            "style": "{",
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "simple",
        },
        "file": {
            "class": "logging.handlers.RotatingFileHandler",
            "filename": os.path.join(BASE_DIR, "logs", "practis_api.log"),
            "formatter": "verbose",
            "maxBytes": 1024 * 1024 * 10,  # 10 MB
            "backupCount": 5,
        },
    },
    "loggers": {
        "keycloak": {
            "handlers": ["console", "file"],
            "level": "INFO",
            "propagate": False,
        },
        "api": {
            "handlers": ["console", "file"],
            "level": "INFO",
            "propagate": False,
        },
    },
}
