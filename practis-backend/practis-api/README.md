# API

To run this project, you need to have python 3.X, <PERSON><PERSON> and <PERSON><PERSON>-compose installed. Here are some useful settings to apply before launching your compose.


## Setting up your API environment

- Open the API directory

>$ cd API

- Set your environment variables

Create a __.env__ file and store your environment variables inside. Here are the environment variables you need to run the project.

```bash
DEBUG= 1 for dev or 0 for prod
DEV_SECRET_KEY_FILE_= The file  where the container will read the dev secret key
PROD_SECRET_KEY_FILE= The file  where the container will read the prod secret key
DB_HOST= your database host
DB_PORT= your database port
DB_NAME_FILE= your database name for production
DEV_DB_NAME= your database name for development
DB_USERNAME_FILE= your database username
DB_PASS_FILE= The file  where the container will read the database password
CLIENT_ID= your openid client id
REALM_FILE= The file  where the container will read your openid connect realm name
CLIENT_SECRET_FILE= The file  where the container will read your openid connect client secret
WELL_KNOWN_FILE= The file  where the container will read your openid connect well known url

# ⛔️ Avoid mentionning your secret data here!!! ⛔️
# That's why we use files instead of values for secret variables.
```
You can use this site to generate a django key faster : https://djecrety.ir/
You can change some secret key's characters for security.

Now you are good! 
Back to the [main ReadMe file](../README.md)

