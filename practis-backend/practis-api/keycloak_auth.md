# Test authentication without using a third party software


When you authenticate using keycloak, you can use either your username or your email, and your password. Anyway, you receive a token in the server response that has a lifespan defined in your Realm settings on  the Keycloak server.

Now, the API requires an authentication in order to access resources. the request sent must contain a valid Bearer token. In our Cyrus context, all requests come from the frontend. But you don't have access to the frontend code. Below a short program that will help you to test the API without accessing the frontend.

The program is going to make a secured call to the API backend and retrieve the sut-provider information. It simulates what a fronted application would do. Because in a short program it

- makes it easy to follow the authentication and secured API request
- avoids having to use a real frontend or a tool like Insomnia or Postman
- it helps in debugging authentication

```python
import requests
import os
from django.conf import settings
import time

# we set our keycloak configuration
keycloak_url = f"{os.environ.get("SERVER_URL")}realms/{os.environ.get("REALM_NAME")}/protocol/openid-connect/token"
data = {
    "client_id": os.environ.get("CLIENT_ID"),
    "client_secret": os.environ.get("CLIENT_SECRET"),
    "grant_type": "password",
    "username": os.environ.get("USERNAME"),
    "password": os.environ.get("PASSWORD"),
}

# we get the access token
response = requests.post(keycloak_url, data=data)
# print(response.json())   # uncomment this line to see the complete response
access_token = response.json().get("access_token")

# we save the token in a file to avoid surchaging logs 
# This is not mandatory, it is just to make sure that you actually received the token
with open("token.txt", "w") as f:
    f.write(access_token)

# we set our introspect configuration for token verification
introspect_url = f"{os.environ.get("SERVER_URL")}realms/{os.environ.get("REALM_NAME")}/protocol/openid-connect/token/introspect"
introspect_data = {
    "client_id": os.environ.get("CLIENT_ID"),
    "client_secret": os.environ.get("CLIENT_SECRET"),
    "token": access_token,
}

print("\n\n********** checking token... ***********\n\n")
if access_token:
    response = requests.post(introspect_url, data=introspect_data)
    # print(response.json())     # uncomment this line to see the complete response

    if response.json().get("active"):
        print("Token is active")
        # we print relevant user info
        print(
            {
                "first_name": response.json().get("given_name"),
                "last_name": response.json().get("family_name"),
                "email": response.json().get("email"),
                "username": response.json().get("preferred_username"),
            }
        )

    # we can now use the token to access protected resources of the remote server
    response = requests.get(
        "http://localhost:8080/api/sut-provider/",
        headers={"Authorization": access_token},
    )

    # check the response
    print(response.json())
else:
    print("Token not found")

```

Since this file helps you to test the authentication, you should provide some settings and credentials. All the environment variables in this code can be found on gitlab except __USERNAME__ , __PASSWORD__, and __API_SERVER__. the username and password are your personal credentials. If you don't have an account on keycloak, a realm admin can create an account for you (@GGI or @LGR).
if you are testing on a remote machine, change localhost into the remote machine's address.