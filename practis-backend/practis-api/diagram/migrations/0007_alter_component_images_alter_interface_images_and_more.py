# Generated by Django 4.2.7 on 2025-04-02 09:46

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0073_version_diagram_json"),
        ("diagram", "0006_alter_component_version_alter_interface_version_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="component",
            name="images",
            field=models.ManyToManyField(
                blank=True, null=True, related_name="%(class)s", to="api.imagefile"
            ),
        ),
        migrations.AlterField(
            model_name="interface",
            name="images",
            field=models.ManyToManyField(
                blank=True, null=True, related_name="%(class)s", to="api.imagefile"
            ),
        ),
        migrations.AlterField(
            model_name="port",
            name="images",
            field=models.ManyToManyField(
                blank=True, null=True, related_name="%(class)s", to="api.imagefile"
            ),
        ),
        migrations.AlterField(
            model_name="subcomponent",
            name="images",
            field=models.ManyToManyField(
                blank=True, null=True, related_name="%(class)s", to="api.imagefile"
            ),
        ),
    ]
