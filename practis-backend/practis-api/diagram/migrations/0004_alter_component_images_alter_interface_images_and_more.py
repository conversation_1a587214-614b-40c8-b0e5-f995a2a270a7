# Generated by Django 4.2.7 on 2025-03-05 00:46

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0064_flow_parameter_type_flowexecution_component_and_more"),
        ("diagram", "0003_component_images_interface_images_port_images_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="component",
            name="images",
            field=models.ManyToManyField(
                blank=True,
                null=True,
                related_name="%(class)s",
                serialize=False,
                to="api.imagefile",
            ),
        ),
        migrations.AlterField(
            model_name="interface",
            name="images",
            field=models.ManyToManyField(
                blank=True,
                null=True,
                related_name="%(class)s",
                serialize=False,
                to="api.imagefile",
            ),
        ),
        migrations.AlterField(
            model_name="port",
            name="images",
            field=models.ManyToManyField(
                blank=True,
                null=True,
                related_name="%(class)s",
                serialize=False,
                to="api.imagefile",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="subcomponent",
            name="images",
            field=models.ManyToManyField(
                blank=True,
                null=True,
                related_name="%(class)s",
                serialize=False,
                to="api.imagefile",
            ),
        ),
    ]
