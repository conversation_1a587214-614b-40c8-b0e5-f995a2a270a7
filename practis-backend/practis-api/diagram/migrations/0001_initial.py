# Generated by Django 4.2.7 on 2025-03-03 19:29

from django.db import migrations, models
import django.db.models.deletion
import uuid6


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Component",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid6.uuid7,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("availability", models.BooleanField(default=False)),
                ("confidentiality", models.BooleanField(default=False)),
                ("description", models.TextField(blank=True, null=True)),
                ("integrity", models.BooleanField(default=False)),
                ("name", models.CharField(max_length=255)),
                ("notes", models.TextField(blank=True, null=True)),
            ],
            options={
                "db_table": "component",
            },
        ),
        migrations.CreateModel(
            name="Interface",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid6.uuid7,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("availability", models.Bo<PERSON>anField(default=False)),
                ("confidentiality", models.<PERSON><PERSON>anField(default=False)),
                ("description", models.TextField(blank=True, null=True)),
                ("integrity", models.BooleanField(default=False)),
                ("notes", models.TextField(blank=True, null=True)),
                ("name", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("internal", "Internal Interface"),
                            ("external", "External Interface"),
                        ],
                        default="external",
                        max_length=10,
                    ),
                ),
            ],
            options={
                "db_table": "interface",
            },
        ),
        migrations.CreateModel(
            name="ParameterType",
            fields=[
                ("description", models.TextField(blank=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid6.uuid7,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=255)),
            ],
            options={
                "db_table": "parameter_type",
            },
        ),
        migrations.CreateModel(
            name="SubComponent",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid6.uuid7,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("availability", models.BooleanField(default=False)),
                ("confidentiality", models.BooleanField(default=False)),
                ("description", models.TextField(blank=True, null=True)),
                ("integrity", models.BooleanField(default=False)),
                ("name", models.CharField(max_length=255)),
                ("notes", models.TextField(blank=True, null=True)),
                (
                    "component",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subcomponents",
                        to="diagram.component",
                    ),
                ),
            ],
            options={
                "db_table": "subcomponent",
            },
        ),
        migrations.CreateModel(
            name="Port",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid6.uuid7,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("availability", models.BooleanField(default=False)),
                ("confidentiality", models.BooleanField(default=False)),
                ("description", models.TextField(blank=True, null=True)),
                ("integrity", models.BooleanField(default=False)),
                ("name", models.CharField(max_length=255)),
                ("notes", models.TextField(blank=True, null=True)),
                (
                    "component",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ports",
                        to="diagram.component",
                    ),
                ),
            ],
            options={
                "db_table": "port",
            },
        ),
        migrations.CreateModel(
            name="Parameter",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid6.uuid7,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        default="Default parameter",
                        help_text="Enter the name of the parameter",
                        max_length=255,
                    ),
                ),
                ("secret", models.BooleanField(default=False)),
                ("value", models.TextField(blank=True, null=True)),
                (
                    "component",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="parameters",
                        to="diagram.component",
                    ),
                ),
                (
                    "interface",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="parameters",
                        to="diagram.interface",
                    ),
                ),
                (
                    "parameter_type",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="parameters",
                        to="diagram.parametertype",
                    ),
                ),
                (
                    "port",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="parameters",
                        to="diagram.port",
                    ),
                ),
                (
                    "subcomponent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="parameters",
                        to="diagram.subcomponent",
                    ),
                ),
            ],
            options={
                "db_table": "parameter",
            },
        ),
        migrations.AddField(
            model_name="interface",
            name="port_from",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="interfaces_from",
                to="diagram.port",
            ),
        ),
        migrations.AddField(
            model_name="interface",
            name="port_to_port",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="interfaces_to_port",
                to="diagram.port",
            ),
        ),
        migrations.AddField(
            model_name="interface",
            name="port_to_subcomponent",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="interfaces_to_subcomponent",
                to="diagram.subcomponent",
            ),
        ),
    ]
