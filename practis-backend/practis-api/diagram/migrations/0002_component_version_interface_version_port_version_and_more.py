# Generated by Django 4.2.7 on 2025-03-04 13:10

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0063_alter_riskanalysis_options_and_more"),
        ("diagram", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="component",
            name="version",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s_versions",
                to="api.version",
            ),
        ),
        migrations.AddField(
            model_name="interface",
            name="version",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s_versions",
                to="api.version",
            ),
        ),
        migrations.AddField(
            model_name="port",
            name="version",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s_versions",
                to="api.version",
            ),
        ),
        migrations.AddField(
            model_name="subcomponent",
            name="version",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s_versions",
                to="api.version",
            ),
        ),
    ]
