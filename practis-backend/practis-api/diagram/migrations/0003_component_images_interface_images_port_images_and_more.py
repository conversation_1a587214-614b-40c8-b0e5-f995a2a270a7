# Generated by Django 4.2.7 on 2025-03-04 23:41

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0064_flow_parameter_type_flowexecution_component_and_more"),
        ("diagram", "0002_component_version_interface_version_port_version_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="component",
            name="images",
            field=models.ManyToManyField(blank=True, null=True, to="api.imagefile"),
        ),
        migrations.AddField(
            model_name="interface",
            name="images",
            field=models.ManyToManyField(blank=True, null=True, to="api.imagefile"),
        ),
        migrations.AddField(
            model_name="port",
            name="images",
            field=models.ManyToManyField(blank=True, null=True, to="api.imagefile"),
        ),
        migrations.AddField(
            model_name="subcomponent",
            name="images",
            field=models.ManyToManyField(blank=True, null=True, to="api.imagefile"),
        ),
    ]
