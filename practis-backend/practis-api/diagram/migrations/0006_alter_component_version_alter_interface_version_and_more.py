# Generated by Django 4.2.7 on 2025-03-06 11:37

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0069_alter_flowexecution_options"),
        ("diagram", "0005_parametertype_generic"),
    ]

    operations = [
        migrations.AlterField(
            model_name="component",
            name="version",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s",
                to="api.version",
            ),
        ),
        migrations.AlterField(
            model_name="interface",
            name="version",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s",
                to="api.version",
            ),
        ),
        migrations.AlterField(
            model_name="port",
            name="version",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s",
                to="api.version",
            ),
        ),
        migrations.AlterField(
            model_name="subcomponent",
            name="version",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s",
                to="api.version",
            ),
        ),
    ]
