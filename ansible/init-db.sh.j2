#!/bin/bash
set -e
echo "initializing the database..."
psql -v ON_ERROR_STOP=1 -U {{ generated_secrets.postgres_username.value }} -d {{  generated_secrets.practis_api_prod_db.value }} <<-EOSQL
    CREATE USER {{ generated_secrets.kiwi_db_username.value }} WITH ENCRYPTED PASSWORD '{{ generated_secrets.kiwi_db_password_init.value }}';
    CREATE DATABASE {{ generated_secrets.kiwi_db_name.value }};
    ALTER DATABASE {{ generated_secrets.kiwi_db_name.value }} OWNER TO {{ generated_secrets.kiwi_db_username.value }};
    CREATE SCHEMA IF NOT EXISTS api;
    CREATE SCHEMA IF NOT EXISTS public;
EOSQL
echo "initialization of the database done ✅"
