export const environment = {
  production: {{ practis_frontend_prod | str | lower }},
  apiUrl: '{{ generated_secrets.practis_api_host.value }}:{{ generated_secrets.practis_api_port.value }}',
  imagePrefixUrl: '/images',
  noderedUrl: '{{ generated_secrets.nodered_url.value }}',
  gvmUrl: 'http://127.0.0.1:9392/',
  gvmInstance: '127.0.0.1',
  kiwiUrl: '{{ generated_secrets.kiwi_api_kiwi_server.value }}',
  rulesReportUrl: 'http://127.0.0.1:8000/',
  importUrl: 'http://localhost:9394/retrieve/',
  keycloakUrl: '{{ generated_secrets.api_kc_server_url.value }}',
  keycloakRealm: '{{ generated_secrets.api_kc_realm_name.value }}',
  keycloakClientId: '{{ generated_secrets.api_kc_client_id.value }}',
};
