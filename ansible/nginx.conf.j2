server {
    listen 4200 ssl;
    server_name {{ frontend_host | default(ansible_fact['ansible_default_ipv4']['address'], true)}};

    ssl_certificate /etc/nginx/ssl/server.crt;
    ssl_certificate_key /etc/nginx/ssl/server.key;

    root /usr/share/nginx/html;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

        # Reverse proxy for API requests

    # Optional: Disable weak SSL protocols
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
}
