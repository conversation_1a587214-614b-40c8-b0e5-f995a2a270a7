# vim: ft=yaml.ansible
# code: language=ansible

- name: Generating secrets {{ item.key }}
  ansible.builtin.set_fact:
    secret_value: "{{ item.value.value | default(lookup('ansible.builtin.password', '/dev/null length=' + (item.value.length | default(16) | string) + ' chars=' + (item.value.chars | default('ascii_letters') | join(',')) ), true) }}"

- name: Insert into generated_secrets.yml
  ansible.builtin.lineinfile:
    path: "generated_secrets.yml"
    regexp: "  {{ item.key }}:.*"
    value: "  {{ item.key }}: { name: '{{ item.key }}', value: '{{ secret_value }}', paths: {{ item.value.paths | default([]) }} }"
