# Practis Project Deployment Guide

## Overview

This guide explains how to deploy the Practis platform using Ansible. The deployment automates the installation and configuration of several components including Docker, PostgreSQL, Kiwi TCMS (Test Case Management System), and the Practis backend and frontend applications.

## Architecture Overview

The Practis platform consists of the following components:

```mermaid
graph TD
    A[Client] --> B[NGINX/Frontend]
    B --> C[Practis API]
    C --> D[PostgreSQL]
    C --> E[Kiwi Broker]
    F --> D
    E --> F[Kiwi TCMS]
    
    subgraph "Docker Containers"
        B
        C
        D
        F
        E
    end
```

### Key Components:

1. **Practis Frontend**: Angular-based web interface
2. **Practis API**: Backend service
3. **PostgreSQL**: Database for both Practis API and Kiwi TCMS
4. **Kiwi TCMS**: Test case management system
5. **Kiwi Broker**: Integration service for Kiwi TCMS

## Deployment Workflow

The deployment process uses Ansible playbooks to automate configuration and installation. Here's the workflow:

```mermaid
sequenceDiagram
    participant User
    participant Secrets
    participant GenerateSecrets
    participant InstallPlaybook
    participant TargetVM

    User->>Secrets: Create secrets.yml
    User->>GenerateSecrets: Run generate_secrets_playbook.yml
    GenerateSecrets->>GenerateSecrets: Process secrets
    GenerateSecrets->>User: Create generated_secrets.yml
    User->>InstallPlaybook: Run install_playbook.yml
    InstallPlaybook->>TargetVM: Setup Docker (if needed)
    InstallPlaybook->>TargetVM: Clone repository
    InstallPlaybook->>TargetVM: Create configuration files
    InstallPlaybook->>TargetVM: Generate SSL certificates
    InstallPlaybook->>TargetVM: Start containers
```

## File Structure

```
├── secrets_sample.yml         # Template for secrets configuration
├── hosts.yml                  # Target hosts inventory
├── generate_secrets_playbook.yml   # Playbook to generate secrets
├── install_playbook.yml       # Main installation playbook
├── ansible/                   # Ansible templates and tasks
│   ├── generate_secrets.yml   # Secret generation tasks
│   ├── api.env.j2             # API environment template
│   ├── tcms.env.j2            # TCMS environment template
│   ├── tcms_broker.env.j2     # TCMS Broker environment template
│   ├── init-db.sh.j2          # Database initialization template
│   └── environment.ts.j2      # Frontend environment template
```

## Requirements

- Linux or MacOS host (or Windows with WSL)
- Ansible 2.9 or higher
- Ansible Role: geerlingguy.docker
- SSH access to the target VM with an account that has passwordless sudo privileges
- Git access to the Practis repository

## Preparation

1. Install required Ansible role:

```sh
ansible-galaxy install geerlingguy.docker
```

2. Create your secrets configuration file:

```sh
cp secrets_sample.yml secrets.yml
```

3. Edit the `secrets.yml` file to match your environment requirements (see Configuration section below)

## Deployment Steps

### 1. Generate Secrets

This step processes your configuration and generates secure values for undefined secrets:

```sh
ansible-playbook generate_secrets_playbook.yml -e @secrets.yml
```

What this does:
- Creates `generated_secrets.yml` with all required secrets
- Generates random passwords where values are set to `null`
- Formats secrets for use by the installation playbook

### 2. Install Practis

Execute the installation playbook against your target host(s):

```sh
ansible-playbook install_playbook.yml -i hosts.yml --user my_user --private-key ~/.ssh/my_key -e @secrets.yml -e @generated_secrets.yml
```

What this does:
- Installs Docker if specified
- Creates the installation directory
- Clones the Practis repository
- Creates secret files in appropriate directories
- Generates environment configuration files
- Creates SSL certificates
- Builds and starts Docker containers

## Configuration Parameters

The `secrets.yml` file controls the deployment configuration. Here are the key parameters:

### Core Settings

| Parameter | Description | Default |
|-----------|-------------|---------|
| `git_user` | Git username or token name | (empty) |
| `git_token` | Git personal access token | (empty) |
| `install_docker` | Enable Docker installation | `false` |
| `practis_repo` | Git repository URL | `git.cetic.be/practis/platform/practis-all.git` |
| `practis_branch` | Git branch to deploy | `main` |
| `install_path` | Installation directory | `/opt/practis` |
| `kiwi_db_password` | Kiwi database password (referenced by multiple components) | `secret12345` |
| `practis_frontend_prod` | Enable production mode in frontend | `false` |

### Secret Management Logic

The `secrets` section in `secrets.yml` defines various secrets needed by the application components:

- Setting a value to `null` triggers automatic generation of a secure random value
- Each secret can have multiple destination paths where it should be stored
- Some secrets (like database credentials) are referenced in multiple components

## Secret Configuration Map

The diagram below shows how secrets are distributed across components:

```mermaid
graph TD
    A[secrets.yml] --> B[generate_secrets_playbook.yml]
    B --> C[generated_secrets.yml]
    C --> D[Practis Backend Secrets]
    C --> E[Kiwi TCMS Secrets]
    C --> F[Frontend Environment]
    
    subgraph "Deployment"
        D --> G[API .env]
        E --> H[TCMS .env]
        E --> I[TCMS Broker .env]
        F --> J[environment.ts]
    end
```

## Detailed Secret Configuration

The table below shows all configurable secrets and their destinations. Values set to `null` will be auto-generated.

| Secret Name | Default Value | Storage Paths |
|-------------|---------------|---------------|
| postgres_username | practis | practis-backend/secrets |
| postgres_password | null | practis-backend/secrets |
| practis_api_dev_db | practis | practis-backend/secrets |
| practis_api_prod_db | practis | practis-backend/secrets |
| pgadmin_pwd | null | practis-backend/secrets |
| practis_api_dev_key | null | practis-backend/secrets |
| practis_api_prod_key | null | practis-backend/secrets |
| api_kc_realm_name | practis | practis-backend/secrets |
| api_kc_client_id | practis | practis-backend/secrets |
| api_kc_client_secret | "" | practis-backend/secrets |
| api_kc_well_known | https://sso.apps.cyber-factory-core.cetic.be/realms/practis/.well-known/openid-configuration | practis-backend/secrets |
| api_kc_server_url | https://sso.apps.cyber-factory-core.cetic.be | practis-backend/secrets |
| api_kw_broker | http://tcms-api:8080 | practis-backend/secrets |
| openvas_broker | https://10.134.2.196:8000 | practis-backend/secrets |
| kiwi_name | kiwi | practis-backend/secrets |
| kiwi_db_name | kiwi | practis-kiwi/kiwi/secrets |
| kiwi_db_username | kiwi | practis-kiwi/kiwi/secrets |
| kiwi_db_password_init | kiwi | practis-kiwi/kiwi/secrets |
| db_kiwi_name | kiwi | practis-backend/secrets |
| db_kiwi_username | kiwi | practis-backend/secrets |
| db_kiwi_password | kiwi | practis-backend/secrets |
| kiwi_admin_username | kiwi_admin | practis-kiwi/PractisKiwi/secrets, practis-kiwi/kiwi/secrets |
| kiwi_admin_password | kiwi_admin | practis-kiwi/kiwi/secrets |
| kiwi_api_kiwi_server | https://web:8443/xml-rpc/ | practis-kiwi/PractisKiwi/secrets |
| kiwi_api_secret_key | null | practis-kiwi/PractisKiwi/secrets |
| kiwi_broker_django_secrets | null |
| practis_debug_mode | 1 |

## Network Configuration

Various port mappings control how services are exposed:

| Parameter | Default Value | Description |
|-----------|---------------|-------------|
| postgres_host | postgres | PostgreSQL hostname |
| postgres_port | 5432 | PostgreSQL port |
| practis_api_host | practis-api | API service hostname |
| practis_api_port | 8000 | API service port |
| kiwi_web_local_port_1 | 80 | Kiwi HTTP port (host) |
| kiwi_api_container_port_1 | 8081 | |
| kiwi_web_container_port_1 | 8080 | Kiwi HTTP port (container) |
| kiwi_web_local_port_2 | 443 | Kiwi HTTPS port (host) |
| kiwi_web_container_port_2 | 8443 | Kiwi HTTPS port (container) |
| frontend_host | null | Frontend hostname (uses IP address if null) |
| proxy_port_container_port | 8443 | |
| proxy_port_local_port | 4343 | |
| nodered_url | "" |
| gvm_url | "" | |

## Files Overview

Here's a description of the key files in the deployment system:

| File | Purpose |
|------|---------|
| `secrets_sample.yml` | Template for creating your `secrets.yml` with configurable options |
| `generate_secrets_playbook.yml` | Playbook that processes `secrets.yml` and generates `generated_secrets.yml` |
| `generate_secrets.yml` | Ansible task that handles the actual secret generation |
| `install_playbook.yml` | Main playbook that handles the deployment to the target VM |
| `hosts.yml` | Inventory file defining target hosts |
| `api.env.j2`, `tcms.env.j2`, `tcms_broker.env.j2` | Templates for environment configuration files |
| `init-db.sh.j2` | Template for database initialization script |

## Troubleshooting

### Common Issues

1. **Permission denied when cloning the repository**
   - Verify that your `git_user` and `git_token` values are correct
   - Ensure the token has proper repository access permissions

2. **Docker installation failures**
   - If you already have Docker installed, set `install_docker: false`
   - Check system requirements for Docker (kernel version, etc.)

3. **Database initialization errors**
   - Check database host connectivity
   - Verify that database credentials are correctly set

### Logs and Debugging

- Docker logs can be accessed on the target VM:
  ```sh
  docker logs <container-name>
  ```

- Enable Ansible verbose mode for debugging:
  ```sh
  ansible-playbook install_playbook.yml -i hosts.yml -vvv -e @secrets.yml -e @generated_secrets.yml
  ```

- Check the environment files created in the installation directory for correct values

## Security Considerations

- Generated passwords use secure randomization with a mix of letters and digits
- SSL certificates are generated for secure communication
- Sensitive files are created with restricted permissions (mode 640 or 750)
- Database and API credentials are stored separately
- Consider using a secrets management system like HashiCorp Vault for production

## Post-Installation

After installation, you can access:

- The Practis frontend at `https://<target-ip>:4200`
- Kiwi TCMS at `http://<target-ip>:80` or `https://<target-ip>:443`
- The API at `http://<target-ip>:8000`

Default admin credentials for Kiwi TCMS are set in the `secrets.yml` file (default: kiwi_admin/kiwi_admin).
